[{"F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\main.js": "1", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\App.vue": "2", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\router\\index.js": "3", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\LoginView.vue": "4", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\ActivityView.vue": "5", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\AdminView.vue": "6", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\RegisterView.vue": "7"}, {"size": 4699, "mtime": 1756275079604, "results": "8", "hashOfConfig": "9"}, {"size": 5291, "mtime": 1756372619260, "results": "10", "hashOfConfig": "9"}, {"size": 2667, "mtime": 1756272655502, "results": "11", "hashOfConfig": "9"}, {"size": 13740, "mtime": 1756372757681, "results": "12", "hashOfConfig": "9"}, {"size": 124951, "mtime": 1756373969426, "results": "13", "hashOfConfig": "9"}, {"size": 19875, "mtime": 1756373464258, "results": "14", "hashOfConfig": "9"}, {"size": 19052, "mtime": 1756372938675, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, "rtuktq", {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\main.js", [], [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\App.vue", [], [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\router\\index.js", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\LoginView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\ActivityView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\AdminView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\RegisterView.vue", []]