{"ast": null, "code": "export default {\n  name: 'RegisterView',\n  data() {\n    return {\n      username: '',\n      full_name: '',\n      password: '',\n      team_pin: '',\n      governorate: '',\n      busy: false,\n      error: '',\n      governorates: ['بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى', 'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين', 'ميسان', 'دهوك', 'السليمانية', 'حلبجة']\n    };\n  },\n  methods: {\n    async doRegister() {\n      this.error = '';\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\n        this.error = 'يرجى تعبئة جميع الحقول';\n        return;\n      }\n      this.busy = true;\n      try {\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: this.username,\n            full_name: this.full_name,\n            password: this.password,\n            team_pin: this.team_pin,\n            governorate: this.governorate\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\n        // persist auth and team pin\n        localStorage.setItem('ndyt_token', data.token);\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.replace(redirect);\n      } catch (e) {\n        this.error = e.message;\n      } finally {\n        this.busy = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "username", "full_name", "password", "team_pin", "governorate", "busy", "error", "governorates", "methods", "doRegister", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "json", "ok", "Error", "localStorage", "setItem", "token", "user", "redirect", "$route", "query", "$router", "replace", "e", "message"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\RegisterView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'RegisterView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      full_name: '',\r\n      password: '',\r\n      team_pin: '',\r\n      governorate: '',\r\n      busy: false,\r\n      error: '',\r\n      governorates: [\r\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\r\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\r\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    async doRegister() {\r\n      this.error = '';\r\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\r\n        // persist auth and team pin\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-text\">\r\n        <span class=\"org-name\">المجلس الأعلى للشباب</span>\r\n        <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"team-name\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"auth-wrapper\">\r\n    <div class=\"card\">\r\n      <h1>إنشاء حساب</h1>\r\n      <div class=\"field\">\r\n        <label>اسم المستخدم</label>\r\n        <input v-model=\"username\" type=\"text\" dir=\"rtl\" placeholder=\"ادخل اسم المستخدم\" />\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>الاسم الكامل</label>\r\n        <input v-model=\"full_name\" type=\"text\" dir=\"rtl\" placeholder=\"ادخل الاسم الكامل\" />\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>المحافظة</label>\r\n        <select v-model=\"governorate\" class=\"governorate-select\" dir=\"rtl\">\r\n          <option value=\"\" disabled>اختر المحافظة</option>\r\n          <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\r\n        </select>\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>كلمة المرور</label>\r\n        <input v-model=\"password\" type=\"password\" dir=\"rtl\" placeholder=\"ادخل كلمة المرور\" />\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>رمز الفريق الرقمي</label>\r\n        <input v-model=\"team_pin\" type=\"text\" dir=\"rtl\" placeholder=\"ادخل رمز الفريق الرقمي\" />\r\n      </div>\r\n      <button :disabled=\"busy\" @click=\"doRegister\">\r\n        <span v-if=\"!busy\">تسجيل</span>\r\n        <span v-else>... جاري التسجيل</span>\r\n      </button>\r\n      <p v-if=\"error\" class=\"error\">{{ error }}</p>\r\n      <p class=\"hint\">لديك حساب بالفعل؟ <router-link to=\"/login\">سجّل الدخول</router-link></p>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n.navbar {\r\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n  border: 2px solid #3a3a5e;\r\n  border-radius: 12px;\r\n  margin: 16px;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navbar-content {\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.navbar-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.org-name, .team-name {\r\n  font-weight: 700;\r\n  font-size: clamp(16px, 3vw, 24px);\r\n  color: #f5f5f5;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n}\r\n\r\n.logo {\r\n  height: clamp(50px, 8vw, 70px);\r\n  width: auto;\r\n  border: 2px solid #4a5568;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.auth-wrapper {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #101214, #1b1f24);\r\n    padding: 16px;\r\n}\r\n\r\n.card {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(20px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 20px;\r\n    padding: 32px;\r\n    width: 100%;\r\n    max-width: 420px;\r\n    color: #e5e7eb;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    transition: all 0.3s ease;\r\n    direction: rtl;\r\n}\r\n\r\n.card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.card h1 {\r\n    font-size: 28px;\r\n    margin: 0 0 32px;\r\n    text-align: center;\r\n    font-weight: 700;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.field {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin: 20px 0;\r\n}\r\n\r\n.field label {\r\n    font-size: 14px;\r\n    color: #cbd5e1;\r\n    font-weight: 600;\r\n    text-align: right;\r\n}\r\n\r\ninput {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n}\r\n\r\ninput:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\ninput::placeholder {\r\n    color: #cbd5e1;\r\n    text-align: right;\r\n    opacity: 1;\r\n}\r\n\r\n.governorate-select {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    cursor: pointer;\r\n}\r\n\r\n.governorate-select:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\n.governorate-select option {\r\n    background: #1b1f24;\r\n    color: #e5e7eb;\r\n    padding: 8px;\r\n}\r\n\r\nbutton {\r\n    width: 100%;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: white;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n    margin-top: 8px;\r\n}\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.error {\r\n    color: #ef4444;\r\n    margin-top: 10px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint {\r\n    margin-top: 24px;\r\n    color: #cbd5e1;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint a {\r\n    color: #4f46e5;\r\n    text-decoration: none;\r\n    font-weight: 600;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.hint a:hover {\r\n    color: #6366f1;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 24px;\r\n        margin: 16px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 24px;\r\n        margin-bottom: 24px;\r\n    }\r\n    \r\n    input {\r\n        padding: 12px 14px;\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n    \r\n    button {\r\n        padding: 12px;\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 16px 0;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .auth-wrapper {\r\n        padding: 12px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 22px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 14px 0;\r\n    }\r\n}\r\n</style>"], "mappings": "AACA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,CACZ,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAC1E,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EACrE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,OAAM;IAEzC,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,UAAUA,CAAA,EAAG;MACjB,IAAI,CAACH,KAAI,GAAI,EAAE;MACf,IAAI,CAAC,IAAI,CAACN,QAAO,IAAK,CAAC,IAAI,CAACC,SAAQ,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,WAAW,EAAE;QAC9F,IAAI,CAACE,KAAI,GAAI,wBAAwB;QACrC;MACF;MACA,IAAI,CAACD,IAAG,GAAI,IAAI;MAChB,IAAI;QACF,MAAMK,GAAE,GAAI,MAAMC,KAAK,CAAC,uCAAuC,EAAE;UAC/DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,SAAS,EAAE,IAAI,CAACA,SAAS;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,WAAW,EAAE,IAAI,CAACA;UAAY,CAAC;QAC9J,CAAC,CAAC;QACF,MAAML,IAAG,GAAI,MAAMW,GAAG,CAACO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACP,GAAG,CAACQ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACpB,IAAI,CAACO,KAAI,IAAK,kBAAkB,CAAC;QAC9D;QACAc,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEtB,IAAI,CAACuB,KAAK,CAAC;QAC9CF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEN,IAAI,CAACC,SAAS,CAACjB,IAAI,CAACwB,IAAG,IAAK,CAAC,CAAC,CAAC,CAAC;QAClEH,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAClB,QAAQ,CAAC;QACpD,MAAMqB,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAO,IAAK,GAAG;QAClD,IAAI,CAACG,OAAO,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,EAAE,OAAOK,CAAC,EAAE;QACV,IAAI,CAACvB,KAAI,GAAIuB,CAAC,CAACC,OAAO;MACxB,UAAU;QACR,IAAI,CAACzB,IAAG,GAAI,KAAK;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}