{"ast": null, "code": "import { RouterView } from 'vue-router';\nimport './assets/styles/design-system.css';\nexport default {\n  name: 'App',\n  components: {\n    RouterView\n  },\n  computed: {\n    routeKey() {\n      // Force component re-render on route change to prevent caching\n      return this.$route.fullPath + Date.now();\n    }\n  }\n};", "map": {"version": 3, "names": ["RouterView", "name", "components", "computed", "routeKey", "$route", "fullPath", "Date", "now"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\App.vue"], "sourcesContent": ["<script>\nimport { RouterView } from 'vue-router';\nimport './assets/styles/design-system.css';\n\nexport default {\n  name: 'App',\n  components: {\n    RouterView\n  },\n  computed: {\n    routeKey() {\n      // Force component re-render on route change to prevent caching\n      return this.$route.fullPath + Date.now();\n    }\n  }\n};\n</script>\n<template>\n  <div class=\"layout-container\">\n    <RouterView :key=\"routeKey\" />\n  </div>\n</template>\n<style>\n/* SweetAlert2 Custom Styles - Using Design System Variables */\n.swal-popup {\n  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary)) !important;\n  border: 2px solid var(--border-primary) !important;\n  border-radius: var(--radius-2xl) !important;\n  box-shadow: var(--shadow-dark-xl) !important;\n  backdrop-filter: blur(20px) !important;\n}\n\n.swal2-title {\n  color: var(--text-primary) !important;\n  font-weight: var(--font-weight-bold) !important;\n  font-size: var(--font-size-xl) !important;\n  font-family: var(--font-family-arabic) !important;\n}\n\n.swal2-content {\n  color: var(--text-secondary) !important;\n  font-size: var(--font-size-base) !important;\n  font-family: var(--font-family-arabic) !important;\n  direction: rtl !important;\n  text-align: right !important;\n}\n\n.swal-confirm-btn {\n  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600)) !important;\n  border: none !important;\n  border-radius: var(--radius-xl) !important;\n  padding: var(--space-3) var(--space-6) !important;\n  font-weight: var(--font-weight-semibold) !important;\n  font-family: var(--font-family-arabic) !important;\n  box-shadow: var(--shadow-dark-md) !important;\n  transition: all var(--transition-base) !important;\n  min-height: var(--btn-height-base) !important;\n}\n\n.swal-confirm-btn:hover {\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500)) !important;\n  transform: translateY(-2px) !important;\n  box-shadow: var(--shadow-dark-lg) !important;\n}\n\n.swal-cancel-btn {\n  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-700)) !important;\n  border: none !important;\n  border-radius: var(--radius-xl) !important;\n  padding: var(--space-3) var(--space-6) !important;\n  font-weight: var(--font-weight-semibold) !important;\n  font-family: var(--font-family-arabic) !important;\n  box-shadow: var(--shadow-dark-md) !important;\n  transition: all var(--transition-base) !important;\n  min-height: var(--btn-height-base) !important;\n}\n\n.swal-cancel-btn:hover {\n  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600)) !important;\n  transform: translateY(-2px) !important;\n  box-shadow: var(--shadow-dark-lg) !important;\n}\n\n/* Toast Notifications Custom Styles - Using Design System Variables */\n.Vue-Toastification__toast {\n  border-radius: var(--radius-2xl) !important;\n  box-shadow: var(--shadow-dark-lg) !important;\n  border: 2px solid var(--border-primary) !important;\n  backdrop-filter: blur(20px) !important;\n  font-family: var(--font-family-arabic) !important;\n}\n\n.toast-success {\n  background: linear-gradient(135deg, var(--color-success-600), var(--color-success-700)) !important;\n  color: white !important;\n  border-color: rgba(34, 197, 94, 0.3) !important;\n}\n\n.toast-error {\n  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-700)) !important;\n  color: white !important;\n  border-color: rgba(239, 68, 68, 0.3) !important;\n}\n\n.toast-warning {\n  background: linear-gradient(135deg, var(--color-warning-600), var(--color-warning-700)) !important;\n  color: white !important;\n  border-color: rgba(245, 158, 11, 0.3) !important;\n}\n\n.toast-info {\n  background: linear-gradient(135deg, var(--color-info-600), var(--color-info-700)) !important;\n  color: white !important;\n  border-color: rgba(59, 130, 246, 0.3) !important;\n}\n\n.toast-body {\n  font-weight: var(--font-weight-semibold) !important;\n  font-size: var(--font-size-sm) !important;\n  direction: rtl !important;\n  text-align: right !important;\n  line-height: var(--line-height-relaxed) !important;\n}\n\n.Vue-Toastification__progress-bar {\n  background: rgba(255, 255, 255, 0.3) !important;\n  height: 3px !important;\n}\n\n.Vue-Toastification__close-button {\n  color: rgba(255, 255, 255, 0.8) !important;\n  font-size: var(--font-size-lg) !important;\n  font-weight: var(--font-weight-bold) !important;\n  transition: all var(--transition-fast) !important;\n  border-radius: var(--radius-lg) !important;\n  padding: var(--space-1) !important;\n}\n\n.Vue-Toastification__close-button:hover {\n  color: white !important;\n  background: rgba(255, 255, 255, 0.1) !important;\n  transform: scale(1.1) !important;\n}\n\n/* Enhanced Toast Container */\n.Vue-Toastification__container {\n  z-index: var(--z-tooltip) !important;\n}\n\n/* Toast Animation Improvements */\n.Vue-Toastification__toast-enter-active {\n  animation: slideInRight 0.3s ease-out !important;\n}\n\n.Vue-Toastification__toast-leave-active {\n  animation: slideOutRight 0.3s ease-in !important;\n}\n\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes slideOutRight {\n  from {\n    transform: translateX(0);\n    opacity: 1;\n  }\n  to {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n}\n</style>"], "mappings": "AACA,SAASA,UAAS,QAAS,YAAY;AACvC,OAAO,mCAAmC;AAE1C,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,QAAQ,EAAE;IACRC,QAAQA,CAAA,EAAG;MACT;MACA,OAAO,IAAI,CAACC,MAAM,CAACC,QAAO,GAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}