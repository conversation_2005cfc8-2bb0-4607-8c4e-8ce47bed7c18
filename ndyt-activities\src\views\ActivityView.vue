<script>
export default {
    name: 'ActivityView',
    data() {
        return {
            activities: [],
            user: null,
            showUserMenu: false,
            tokenCheckInterval: null,
            myActivities: [],
            loadingActivities: false,
            editingActivity: null,
            showMyActivities: false,
            currentView: 'submit',
            coordinatorName: '',
            selectedFilter: null, // null means show all, otherwise filter by state
            showAccountSettings: false,
            accountForm: {
                fullName: '',
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
                teamPin: ''
            },
            updatingAccount: false,
            showPinConfirmation: false,
            pinConfirmationData: {
                pin: '',
                action: '', // 'edit' or 'delete'
                activity: null,
                callback: null
            }
        };
    },
    computed: {
        filteredActivities() {
            if (!this.selectedFilter) {
                return this.myActivities;
            }
            return this.myActivities.filter(activity => activity.state === this.selectedFilter);
        }
    },
    methods: {
        selectFilter(state) {
            // Toggle filter: if same state is clicked, clear filter; otherwise set new filter
            this.selectedFilter = this.selectedFilter === state ? null : state;
        },
        toggleUserMenu() {
            this.showUserMenu = !this.showUserMenu;
        },
        logout() {
            localStorage.removeItem('ndyt_token');
            localStorage.removeItem('ndyt_user');
            localStorage.removeItem('ndyt_team_pin');
            this.showUserMenu = false;
            this.$toast.success('تم تسجيل الخروج بنجاح');
            this.$router.push('/login');
        },

        async checkTokenValidity() {
            const token = localStorage.getItem('ndyt_token');
            if (!token) {
                this.logout();
                return false;
            }

            try {
                const response = await fetch('http://localhost:3000/api/auth/validate', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401 || response.status === 403) {
                    this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');
                    this.logout();
                    return false;
                }

                return response.ok;
            } catch (error) {
                console.error('Error validating token:', error);
                return true; // Don't logout on network errors
            }
        },
        goToAdmin() {
            this.showUserMenu = false;
            this.$router.push('/admin');
        },
        openAccountSettings() {
            this.showUserMenu = false;
            this.accountForm.fullName = this.user?.full_name || '';
            this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';
            this.accountForm.currentPassword = '';
            this.accountForm.newPassword = '';
            this.accountForm.confirmPassword = '';
            this.showAccountSettings = true;
        },
        closeAccountSettings() {
            this.showAccountSettings = false;
            this.accountForm = {
                fullName: '',
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
                teamPin: ''
            };
        },
        async updateAccountSettings() {
            // Validate form
            if (!this.accountForm.fullName.trim()) {
                await this.$swal.fire({
                    title: 'خطأ في البيانات',
                    text: 'يرجى إدخال الاسم الكامل',
                    icon: 'error'
                });
                return;
            }

            if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {
                await this.$swal.fire({
                    title: 'خطأ في البيانات',
                    text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',
                    icon: 'error'
                });
                return;
            }

            if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {
                await this.$swal.fire({
                    title: 'خطأ في البيانات',
                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
                    icon: 'error'
                });
                return;
            }

            this.updatingAccount = true;

            try {
                const token = localStorage.getItem('ndyt_token');
                const updateData = {
                    full_name: this.accountForm.fullName.trim(),
                    team_pin: this.accountForm.teamPin.trim()
                };

                // Only include password if user wants to change it
                if (this.accountForm.newPassword) {
                    updateData.current_password = this.accountForm.currentPassword;
                    updateData.new_password = this.accountForm.newPassword;
                }

                const response = await fetch('/api/v1/ndyt-activities/user/update-profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    await response.json();
                    
                    // Update local user data
                    this.user.full_name = this.accountForm.fullName.trim();
                    localStorage.setItem('ndyt_user', JSON.stringify(this.user));
                    localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());

                    await this.$swal.fire({
                        title: 'تم التحديث بنجاح',
                        text: 'تم تحديث معلومات الحساب بنجاح',
                        icon: 'success'
                    });

                    this.closeAccountSettings();
                } else {
                    const errorData = await response.json();
                    await this.$swal.fire({
                        title: 'خطأ في التحديث',
                        text: errorData.error || 'فشل في تحديث معلومات الحساب',
                        icon: 'error'
                    });
                }
            } catch (error) {
                console.error('Error updating account:', error);
                await this.$swal.fire({
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ أثناء تحديث معلومات الحساب',
                    icon: 'error'
                });
            } finally {
                this.updatingAccount = false;
            }
        },
        async submitCV() {
            try {
                // Validate coordinator name
                if (!this.coordinatorName.trim()) {
                    await this.$swal.fire({
                        title: 'خطأ في البيانات',
                        text: 'يرجى إدخال اسم منسق المحافظة',
                        icon: 'error'
                    });
                    return;
                }
                
                // Collect all activity items
                const activityItems = document.querySelectorAll('.activity-item');
                
                if (activityItems.length === 0) {
                    await this.$swal.fire({
                        title: 'خطأ في البيانات',
                        text: 'يرجى إضافة نشاط واحد على الأقل',
                        icon: 'error'
                    });
                    return;
                }
                
                const token = localStorage.getItem('ndyt_token');
                const teamPin = localStorage.getItem('ndyt_team_pin');
                
                if (!token) {
                    this.$toast.error('يرجى تسجيل الدخول أولاً');
                    this.$router.push('/login');
                    return;
                }
                
                const activities = [];
                let hasErrors = false;
                
                // Process each activity item
                for (let index = 0; index < activityItems.length; index++) {
                    const item = activityItems[index];
                    const inputs = item.querySelectorAll('.activity-input');
                    const ownerName = inputs[0]?.value?.trim();
                    const title = inputs[1]?.value?.trim();
                    const shortDescription = inputs[2]?.value?.trim();
                    const activityDate = inputs[3]?.value;
                    const state = inputs[4]?.value;
                    
                    // Validate required fields
                    if (!ownerName || !title || !activityDate || !state) {
                        this.$swal.fire({
                            title: 'خطأ في البيانات',
                            text: `يرجى ملء جميع الحقول المطلوبة للنشاط رقم ${index + 1}`,
                            icon: 'error'
                        });
                        hasErrors = true;
                        break;
                    }
                    
                    let fileId = null;
                    
                    // Handle file upload if a file is selected
                    if (item.selectedFile) {
                        try {
                            const formData = new FormData();
                            formData.append('file', item.selectedFile);
                            
                            const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${token}`
                                },
                                body: formData
                            });
                            
                            if (uploadResponse.ok) {
                                const uploadResult = await uploadResponse.json();
                                if (uploadResult.file && uploadResult.file.id) {
                                    fileId = uploadResult.file.id;
                                }
                            } else {
                                const errorData = await uploadResponse.json();
                                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);
                                hasErrors = true;
                                break;
                            }
                        } catch (uploadError) {
                            console.error('File upload error:', uploadError);
                            this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);
                            hasErrors = true;
                            break;
                        }
                    }
                    
                    activities.push({
                        owner_name: ownerName,
                        title: title,
                        short_description: shortDescription || '',
                        activity_date: activityDate,
                        state: state,
                        file_id: fileId
                    });
                }
                
                if (hasErrors) return;
                
                // Prepare submission data
                const submissionData = {
                    coordinator_name: this.coordinatorName.trim(),
                    activities: activities
                };
                
                // Submit to backend
                const response = await fetch('/api/v1/ndyt-activities/submissions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'x-team-pin': teamPin
                    },
                    body: JSON.stringify(submissionData)
                });
                
                if (response.ok) {
                    await response.json();
                    this.$toast.success('تم إرسال النشاطات بنجاح!');
                    
                    // Clear the form
                    this.coordinatorName = '';
                    
                    // Remove all activity items
                    activityItems.forEach(item => item.remove());
                    
                    // Refresh my activities if they're currently shown
                    if (this.showMyActivities) {
                        this.fetchMyActivities();
                    }
                } else {
                    let errorData = {};
                    try {
                        errorData = await response.json();
                    } catch (jsonError) {
                        console.error('Error parsing response JSON:', jsonError);
                    }
                    this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);
                }
                
            } catch (error) {
                console.error('Error submitting activities:', error);
                this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');
            }
        },
        async fetchMyActivities() {
            this.loadingActivities = true;
            this.myActivities = []; // Clear existing activities
            
            try {
                const token = localStorage.getItem('ndyt_token');
                
                if (!token) {
                    this.$toast.error('يرجى تسجيل الدخول أولاً');
                    this.$router.push('/login');
                    return;
                }
                
                const response = await fetch('/api/v1/ndyt-activities/activities', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // The new endpoint returns activities directly with role-based filtering
                    if (data.activities && Array.isArray(data.activities)) {
                        this.myActivities = data.activities.map(activity => ({
                            ...activity,
                            submission_info: {
                                id: activity.submission_id,
                                governorate: activity.governorate,
                                coordinator_name: activity.coordinator_name,
                                created_at: activity.created_at
                            }
                        }));
                    }
                } else if (response.status === 401) {
                    this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');
                    localStorage.removeItem('ndyt_token');
                    localStorage.removeItem('ndyt_user');
                    this.$router.push('/login');
                } else if (response.status === 404) {
                    // No activities found - this is normal, not an error
                    this.myActivities = [];
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('Failed to fetch activities:', errorData);
                    this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);
                }
            } catch (error) {
                console.error('Error fetching activities:', error);
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');
                } else {
                    this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');
                }
            } finally {
                this.loadingActivities = false;
            }
        },
        toggleMyActivities() {
            this.showMyActivities = !this.showMyActivities;
            if (this.showMyActivities && this.myActivities.length === 0) {
                this.fetchMyActivities();
            }
        },
        editActivity(activity) {
            this.showPinConfirmationModal('edit', activity, () => {
                this.openEditModal(activity);
            });
        },
        openEditModal(activity) {
            this.editingActivity = { ...activity };
            // Ensure date is properly formatted for date input (YYYY-MM-DD)
            if (this.editingActivity.activity_date) {
                const date = new Date(this.editingActivity.activity_date);
                // Use timezone-safe formatting to avoid date shifting
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                this.editingActivity.activity_date = `${year}-${month}-${day}`;
            }
        },
        async saveActivity() {
            if (!this.editingActivity) return;
            
            try {
                const token = localStorage.getItem('ndyt_token');
                const teamPin = localStorage.getItem('ndyt_team_pin');
                
                const response = await fetch(`/api/v1/ndyt-activities/activities/${this.editingActivity.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'x-team-pin': teamPin
                    },
                    body: JSON.stringify({
                        owner_name: this.editingActivity.owner_name,
                        title: this.editingActivity.title,
                        short_description: this.editingActivity.short_description,
                        activity_date: this.editingActivity.activity_date,
                        state: this.editingActivity.state
                    })
                });
                
                if (response.ok) {
                    const updatedActivity = await response.json();
                    // Update the activity in the list
                    const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);
                    if (index !== -1) {
                        this.myActivities[index] = { ...updatedActivity, submission_info: this.myActivities[index].submission_info };
                    }
                    this.editingActivity = null;
                    this.$toast.success('تم تحديث النشاط بنجاح!');
                } else {
                    this.$toast.error('فشل في تحديث النشاط');
                }
            } catch (error) {
                console.error('Error updating activity:', error);
                this.$toast.error('حدث خطأ أثناء تحديث النشاط');
            }
        },
        cancelEdit() {
            this.editingActivity = null;
        },
        deleteActivity(activityId) {
            const activity = this.myActivities.find(a => a.id === activityId);
            this.showPinConfirmationModal('delete', activity, async () => {
                await this.performDeleteActivity(activityId);
            });
        },
        async performDeleteActivity(activityId) {
            try {
                const token = localStorage.getItem('ndyt_token');
                const teamPin = localStorage.getItem('ndyt_team_pin');
                
                const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'x-team-pin': teamPin
                    }
                });
                
                if (response.ok) {
                    this.myActivities = this.myActivities.filter(a => a.id !== activityId);
                    this.$toast.success('تم حذف النشاط بنجاح!');
                } else {
                    this.$toast.error('فشل في حذف النشاط');
                }
            } catch (error) {
                console.error('Error deleting activity:', error);
                this.$toast.error('حدث خطأ أثناء حذف النشاط');
            }
        },
        getStatusClass(status) {
            const statusMap = {
                'منفذ بصرف': 'status-executed-paid',
                'منفذ بدون صرف': 'status-executed-unpaid',
                'مقبول': 'status-accepted',
                'مرفوض': 'status-rejected',
                'يحتاج تعديل': 'status-needs-edit',
                'صرف و لم ينفذ': 'status-paid-not-executed',
                'مقبول دون صرف': 'status-accepted-unpaid',
                'مرسل': 'status-sent'
            };
            return statusMap[status] || 'status-default';
        },
        AddActivityItem() {
            const activityDiv = document.createElement('div');
            activityDiv.className = 'activity-item';
            const activityOwner = document.createElement('input');
            activityOwner.type = 'text';
            activityOwner.placeholder = 'اسم صاحب االنشاط';
            activityOwner.className = 'activity-input';
            activityDiv.appendChild(activityOwner);
            const activityTitle = document.createElement('input');
            activityTitle.type = 'text';
            activityTitle.placeholder = 'عنوان النشاط';
            activityTitle.className = 'activity-input';
            activityDiv.appendChild(activityTitle);
            const activityShortDescription = document.createElement('input');
            activityShortDescription.type = 'text';
            activityShortDescription.placeholder = 'وصف قصير للنشاط';
            activityShortDescription.className = 'activity-input';
            activityDiv.appendChild(activityShortDescription);
            const activityDateLabel = document.createElement('label');
            activityDateLabel.textContent = 'تاريخ النشاط';
            activityDiv.appendChild(activityDateLabel);
            const activityDate = document.createElement('input');
            activityDate.type = 'date';
            activityDate.className = 'activity-input';
            activityDiv.appendChild(activityDate);
            const activityStateLabel = document.createElement('label');
            activityStateLabel.textContent = 'حالة النشاط';
            activityDiv.appendChild(activityStateLabel);
            const activityApplyState = document.createElement('select');
            activityApplyState.className = 'activity-input';
            const states = 
            ['منفذ بصرف',  'منفذ بدون صرف', 'مقبول', 'مرفوض',
            'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'
            ];
            states.forEach(state => {
                const option = document.createElement('option');
                option.value = state;
                option.textContent = state;
                activityApplyState.appendChild(option);
            });
            const activityFileLabel = document.createElement('label');
            activityFileLabel.textContent = 'ملف النشاط';
            const activityFileInput = document.createElement('label');
            activityFileInput.className = 'activity-file-input';
            activityFileInput.textContent = 'اختر ملف';
            
            // Create hidden file input
            const hiddenFileInput = document.createElement('input');
            hiddenFileInput.type = 'file';
            hiddenFileInput.accept = '.pdf,.doc,.docx,.jpg,.png';
            hiddenFileInput.style.display = 'none';
            hiddenFileInput.onchange = (event) => {
                const file = event.target.files[0];
                if (file) {
                    const fileName = file.name;
                    activityFileInput.textContent = fileName;
                    // Store the file object in the activity div for later use
                    activityDiv.selectedFile = file;
                } else {
                    activityFileInput.textContent = 'اختر ملف';
                    activityDiv.selectedFile = null;
                }
            };
            
            activityFileInput.onclick = () => {
                hiddenFileInput.click();
            };
            
            // Append hidden input to activity div
            activityDiv.appendChild(hiddenFileInput);
            const activityDeleteButton = document.createElement('button');
            activityDeleteButton.className = 'activity-delete-button';
            activityDeleteButton.textContent = 'حذف النشاط';
            activityDeleteButton.onclick = () => {
                activityDiv.remove();
            };
            activityDiv.appendChild(activityApplyState);
            activityDiv.appendChild(activityFileLabel);
            activityDiv.appendChild(activityFileInput);
            activityDiv.appendChild(activityDeleteButton);
            this.activities.push(activityDiv);
            document.querySelector('.activities-list').appendChild(activityDiv);
        },
        handleClickOutside(event) {
            const userSection = event.target.closest('.user-section');
            if (!userSection) {
                this.showUserMenu = false;
            }
        },
        setCurrentView(view) {
            this.currentView = view;
            // Automatically fetch activities when switching to view tab
            if (view === 'view') {
                this.fetchMyActivities();
            }
        },
        refreshActivities() {
            this.fetchMyActivities();
        },
        async loadCoordinatorName() {
            try {
                const token = localStorage.getItem('ndyt_token');
                if (!token) return;

                const response = await fetch('/api/v1/ndyt-activities/coordinator', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.coordinatorName = data.coordinator_name || '';
                } else if (response.status === 404) {
                    // No coordinator found for user's governorate
                    this.coordinatorName = '';
                }
            } catch (error) {
                console.error('Error loading coordinator name:', error);
                // Don't show alert for this error as it's not critical
            }
        },
        exportToCSV() {
            if (this.filteredActivities.length === 0) {
                this.$toast.error('لا توجد نشاطات للتصدير');
                return;
            }

            // Define CSV headers in Arabic
            const headers = [
                'عنوان النشاط',
                'صاحب النشاط', 
                'وصف النشاط',
                'تاريخ النشاط',
                'حالة النشاط',
                'المحافظة',
                'منسق المحافظة',
                'تاريخ الإرسال'
            ];

            // Convert activities data to CSV format
            const csvData = this.filteredActivities.map(activity => {
                return [
                    `"${activity.title || ''}"`,
                    `"${activity.owner_name || ''}"`,
                    `"${activity.short_description || ''}"`,
                    activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '',
                    `"${activity.state || ''}"`,
                    `"${activity.submission_info?.governorate || ''}"`,
                    `"${activity.submission_info?.coordinator_name || ''}"`,
                    activity.submission_info?.created_at ? new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''
                ].join(',');
            });

            // Combine headers and data
            const csvContent = [headers.join(','), ...csvData].join('\n');

            // Add BOM for proper Arabic text encoding in Excel
            const BOM = '\uFEFF';
            const csvWithBOM = BOM + csvContent;

            // Create and download the file
            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                
                // Generate filename with current date and filter info
                const currentDate = new Date().toISOString().split('T')[0];
                const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';
                const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;
                
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);
            } else {
                this.$toast.error('المتصفح لا يدعم تحميل الملفات');
            }
        },
        // PIN Confirmation Methods
        showPinConfirmationModal(action, activity, callback) {
            this.pinConfirmationData = {
                pin: '',
                action: action,
                activity: activity,
                callback: callback
            };
            this.showPinConfirmation = true;
            // Focus on PIN input after modal opens
            this.$nextTick(() => {
                const pinInput = document.getElementById('confirmPin');
                if (pinInput) {
                    pinInput.focus();
                }
            });
        },
        closePinConfirmation() {
            this.showPinConfirmation = false;
            this.pinConfirmationData = {
                pin: '',
                action: '',
                activity: null,
                callback: null
            };
        },
        async confirmPinAction() {
            const enteredPin = this.pinConfirmationData.pin;
            const storedPin = localStorage.getItem('ndyt_team_pin');
            
            if (enteredPin !== storedPin) {
                this.$toast.error('رمز الفريق الرقمي غير صحيح');
                return;
            }
            
            // Store callback before closing modal
            const callback = this.pinConfirmationData.callback;
            
            // Close modal first
            this.closePinConfirmation();
            
            // Execute the callback function after DOM update
            if (callback) {
                this.$nextTick(async () => {
                    await callback();
                });
            }
        },
        downloadFile(file) {
            try {
                // Create a direct download link
                const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;
                
                // Create a temporary anchor element to trigger download
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = file.file_name;
                link.target = '_blank'; // Open in new tab as fallback
                link.style.display = 'none';
                
                // Append to body, click, and remove
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.$toast.success(`تم تحميل الملف: ${file.file_name}`);
            } catch (error) {
                console.error('Download error:', error);
                this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);
            }
        },
        getShortFileName(fileName) {
            // Truncate long file names for display
            if (fileName.length > 20) {
                const extension = fileName.split('.').pop();
                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;
            }
            return fileName;
        }
    },
    async mounted() {
        // Check token validity first
        const isValidToken = await this.checkTokenValidity();
        if (!isValidToken) {
            return; // Will redirect to login
        }

        // Get user info from localStorage
        const userStr = localStorage.getItem('ndyt_user');
        if (userStr) {
            this.user = JSON.parse(userStr);
        }

        // Add click outside listener
        document.addEventListener('click', this.handleClickOutside);

        // Load coordinator name from previous submissions
        this.loadCoordinatorName();

        // Set up periodic token validation (every 5 minutes)
        this.tokenCheckInterval = setInterval(() => {
            this.checkTokenValidity();
        }, 5 * 60 * 1000);
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside);
        if (this.tokenCheckInterval) {
            clearInterval(this.tokenCheckInterval);
        }
    }
}
</script>
<template>
    
        <div class="navbar">
            <div class="navbar-content">
                <div class="navbar-brand">
                    <span class="navbar-text">المجلس الأعلى للشباب</span>
                    <img class="navbar-logo" src="../assets/ndyt_logo.jpg" alt="شعار الفريق">
                    <span class="navbar-text">الفريق الوطني للشباب الرقمي</span>
                </div>
                <div class="nav-actions">
                    <button class="btn nav-btn" :class="{ 'btn-primary': currentView === 'submit', 'btn-secondary': currentView !== 'submit' }" @click="setCurrentView('submit')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        <span>إرسال النشاطات</span>
                    </button>
                    <button class="btn nav-btn" :class="{ 'btn-primary': currentView === 'view', 'btn-secondary': currentView !== 'view' }" @click="setCurrentView('view')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>النشاطات المرسلة</span>
                    </button>
                </div>
                <div class="user-section">
                    <div class="user-button" @click="toggleUserMenu">
                        <div class="user-avatar">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                        <div class="user-info">
                            <span class="user-name">{{ user?.full_name || user?.username || 'المستخدم' }}</span>
                            <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 10l5 5 5-5z"/>
                            </svg>
                        </div>
                    </div>
                    <div v-if="showUserMenu" class="user-menu">
                        <div class="user-menu-item" @click="openAccountSettings">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
                            </svg>
                            <span>إعدادات الحساب</span>
                        </div>
                        <div v-if="user && user.rank === 'admin'" class="user-menu-item" @click="goToAdmin">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z"/>
                            </svg>
                            <span>لوحة الإدارة</span>
                        </div>
                        <div class="user-menu-item" @click="logout">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                            </svg>
                            <span>تسجيل الخروج</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <!-- Submit Activities View -->
            <div v-if="currentView === 'submit'" class="submit-view">
                <div class="view-header">
                    <h2 class="view-title">إرسال النشاطات الجديدة</h2>
                    <p class="view-description">قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة</p>
                </div>
                <div class="base-info-container">
                    <span class="base-info-label">المعلومات الأساسية</span>
                    <label for="coordinator-name" class="field-label">منسق المحافظة</label>
                    <input type="text" id="coordinator-name" placeholder="اسم منسق المحافظة" class="base-info-input" v-model="coordinatorName" required>
                </div>
                
                <div class="splitter"></div>
                
                <div class="activities-info-container">
                    <span class="base-info-label">إضافة نشاطات جديدة</span>
                    <div class="activities-list">
                    </div>
                    <button style="margin: 10px; max-width: 250px;" @click="AddActivityItem">
                        <span>إضافة نشاط جديد</span>
                    </button>
                </div>
                <div class="splitter"></div>
                <button style="margin: 0 50px; background-color: orange; max-width: 180px;" @click="submitCV">
                    <span>إرسال النشاطات</span>
                </button>
            </div>
            
            <!-- View Activities Section -->
            <div v-if="currentView === 'view'" class="view-activities">
                <div class="view-header">
                    <div class="view-header-content">
                        <h2 class="view-title">النشاطات</h2>
                        <p class="view-description">عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)</p>
                    </div>
                    <div class="header-actions">
                        <button @click="exportToCSV" class="export-btn" :disabled="loadingActivities || filteredActivities.length === 0" title="تصدير إلى Excel">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            <span>تصدير CSV</span>
                        </button>
                        <button @click="refreshActivities" class="refresh-btn" :disabled="loadingActivities">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" :class="{ 'spinning': loadingActivities }">
                                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                            </svg>
                            <span>تحديث</span>
                        </button>
                    </div>
                </div>
                
                <div class="activities-container">
                    <div v-if="loadingActivities" class="loading-message">
                        <div class="loading-spinner"></div>
                        <span>جاري تحميل النشاطات...</span>
                    </div>
                    
                    <div v-else-if="myActivities.length === 0" class="no-activities-message">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor" class="empty-icon">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <h3>لا توجد نشاطات</h3>
                        <p>لا توجد نشاطات متاحة حسب صلاحياتك حالياً</p>
                    </div>
                    
                    <!-- Edit Form Modal -->
                    <div v-if="editingActivity" class="edit-modal-overlay" @click="cancelEdit">
                        <div class="edit-modal" @click.stop>
                            <div class="edit-modal-header">
                                <h3>تعديل النشاط</h3>
                                <button @click="cancelEdit" class="close-btn">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="edit-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>اسم صاحب النشاط:</label>
                                        <input v-model="editingActivity.owner_name" type="text" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>عنوان النشاط:</label>
                                        <input v-model="editingActivity.title" type="text" class="form-input">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>تاريخ النشاط:</label>
                                        <input v-model="editingActivity.activity_date" type="date" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>حالة النشاط:</label>
                                        <select v-model="editingActivity.state" class="form-select">
                                            <option value="مرسل">مرسل</option>
                                            <option value="منفذ بصرف">منفذ بصرف</option>
                                            <option value="منفذ بدون صرف">منفذ بدون صرف</option>
                                            <option value="مقبول">مقبول</option>
                                            <option value="مرفوض">مرفوض</option>
                                            <option value="يحتاج تعديل">يحتاج تعديل</option>
                                            <option value="صرف و لم ينفذ">صرف و لم ينفذ</option>
                                            <option value="مقبول دون صرف">مقبول دون صرف</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label>وصف مختصر:</label>
                                        <textarea v-model="editingActivity.short_description" class="form-textarea" rows="3"></textarea>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button @click="saveActivity" class="save-btn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                        </svg>
                                        حفظ التغييرات
                                    </button>
                                    <button @click="cancelEdit" class="cancel-btn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                        </svg>
                                        إلغاء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activities Table -->
                    <div v-else-if="myActivities.length > 0" class="activities-table-container">
                        <div class="table-header">
                            <div class="table-stats">
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === null }" @click="selectFilter(null)">
                                    <span class="stat-number">{{ myActivities.length }}</span>
                                    <span class="stat-label">إجمالي النشاطات</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'منفذ بصرف' }" @click="selectFilter('منفذ بصرف')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'منفذ بصرف').length }}</span>
                                    <span class="stat-label">منفذ بصرف</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'منفذ بدون صرف' }" @click="selectFilter('منفذ بدون صرف')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'منفذ بدون صرف').length }}</span>
                                    <span class="stat-label">منفذ بدون صرف</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'مقبول' }" @click="selectFilter('مقبول')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'مقبول').length }}</span>
                                    <span class="stat-label">مقبول</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'مرفوض' }" @click="selectFilter('مرفوض')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'مرفوض').length }}</span>
                                    <span class="stat-label">مرفوض</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'يحتاج تعديل' }" @click="selectFilter('يحتاج تعديل')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'يحتاج تعديل').length }}</span>
                                    <span class="stat-label">يحتاج تعديل</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'صرف و لم ينفذ' }" @click="selectFilter('صرف و لم ينفذ')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'صرف و لم ينفذ').length }}</span>
                                    <span class="stat-label">صرف و لم ينفذ</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'مقبول دون صرف' }" @click="selectFilter('مقبول دون صرف')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'مقبول دون صرف').length }}</span>
                                    <span class="stat-label">مقبول دون صرف</span>
                                </div>
                                <div class="stat-item clickable" :class="{ selected: selectedFilter === 'مرسل' }" @click="selectFilter('مرسل')">
                                    <span class="stat-number">{{ myActivities.filter(a => a.state === 'مرسل').length }}</span>
                                    <span class="stat-label">مرسل</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-wrapper">
                            <table class="activities-table">
                                <thead>
                                    <tr>
                                        <th class="col-title">عنوان النشاط</th>
                                        <th class="col-owner">صاحب النشاط</th>
                                        <th class="col-date">تاريخ النشاط</th>
                                        <th class="col-status">الحالة</th>
                                        <th class="col-files">الملفات</th>
                                        <th class="col-governorate">المحافظة</th>
                                        <th class="col-coordinator">المنسق</th>
                                        <th class="col-actions">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="activity in filteredActivities" :key="activity.id" class="activity-row">
                                        <td class="col-title">
                                            <div class="activity-title">
                                                <h4>{{ activity.title }}</h4>
                                                <p class="activity-description">{{ activity.short_description || 'لا يوجد وصف' }}</p>
                                            </div>
                                        </td>
                                        <td class="col-owner">
                                            <div class="owner-info">
                                                <span>{{ activity.owner_name }}</span>
                                            </div>
                                        </td>
                                        <td class="col-date">
                                            <div class="date-info">
                                                <span class="date-main">{{ new Date(activity.activity_date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }) }}</span>
                                                <span class="date-year">{{ new Date(activity.activity_date).getFullYear() }}</span>
                                            </div>
                                        </td>
                                        <td class="col-status">
                                            <span class="status-badge" :class="getStatusClass(activity.state)">
                                                <div class="status-indicator"></div>
                                                {{ activity.state }}
                                            </span>
                                        </td>
                                        <td class="col-files">
                                            <div class="files-container">
                                                <div v-if="activity.files && activity.files.length > 0" class="file-buttons">
                                                    <button 
                                                        v-for="file in activity.files" 
                                                        :key="file.id"
                                                        @click="downloadFile(file)"
                                                        class="file-download-btn"
                                                        :title="`تحميل ${file.file_name}`"
                                                    >
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                                        </svg>
                                                        <span class="file-name">{{ getShortFileName(file.file_name) }}</span>
                                                    </button>
                                                </div>
                                                <span v-else class="no-files">لا توجد ملفات</span>
                                            </div>
                                        </td>
                                        <td class="col-governorate">
                                            <span class="governorate-name">{{ activity.submission_info.governorate }}</span>
                                        </td>
                                        <td class="col-coordinator">
                                            <span class="coordinator-name">{{ activity.submission_info.coordinator_name }}</span>
                                        </td>
                                        <td class="col-actions">
                                            <div class="action-buttons">
                                                <button @click="editActivity(activity)" class="action-btn edit-btn" title="تعديل">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                                    </svg>
                                                </button>
                                                <button @click="deleteActivity(activity.id)" class="action-btn delete-btn" title="حذف">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Settings Modal -->
        <div v-if="showAccountSettings" class="modal-overlay" @click="closeAccountSettings">
            <div class="modal-content account-settings-modal" @click.stop>
                <div class="modal-header">
                    <h3>إعدادات الحساب</h3>
                    <button @click="closeAccountSettings" class="close-btn">&times;</button>
                </div>
                
                <form @submit.prevent="updateAccountSettings" class="account-form">
                    <div class="form-group">
                        <label for="fullName">الاسم الكامل:</label>
                        <input 
                            type="text" 
                            id="fullName" 
                            v-model="accountForm.fullName" 
                            required 
                            class="form-input"
                            placeholder="أدخل اسمك الكامل"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="teamPin">رمز الفريق الرقمي:</label>
                        <input 
                            type="text" 
                            id="teamPin" 
                            v-model="accountForm.teamPin" 
                            class="form-input"
                            placeholder="أدخل رمز الفريق الرقمي"
                        >
                    </div>
                    
                    <div class="password-section">
                        <h4>تغيير كلمة المرور (اختياري)</h4>
                        
                        <div class="form-group">
                            <label for="currentPassword">كلمة المرور الحالية:</label>
                            <input 
                                type="password" 
                                id="currentPassword" 
                                v-model="accountForm.currentPassword" 
                                class="form-input"
                                placeholder="أدخل كلمة المرور الحالية"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="newPassword">كلمة المرور الجديدة:</label>
                            <input 
                                type="password" 
                                id="newPassword" 
                                v-model="accountForm.newPassword" 
                                class="form-input"
                                placeholder="أدخل كلمة المرور الجديدة (6 أحرف على الأقل)"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور الجديدة:</label>
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                v-model="accountForm.confirmPassword" 
                                class="form-input"
                                placeholder="أعد إدخال كلمة المرور الجديدة"
                            >
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" @click="closeAccountSettings" class="cancel-btn">إلغاء</button>
                        <button type="submit" :disabled="updatingAccount" class="save-btn">
                            <span v-if="updatingAccount">جاري الحفظ...</span>
                            <span v-else>حفظ التغييرات</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- PIN Confirmation Modal -->
        <div v-if="showPinConfirmation" class="modal-overlay" @click="closePinConfirmation">
            <div class="modal-content pin-modal" @click.stop>
                <div class="modal-header">
                    <h3>تأكيد العملية</h3>
                    <button @click="closePinConfirmation" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p class="pin-message">
                        {{ pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' }}
                    </p>
                    <div class="form-group">
                        <label for="confirmPin">رمز الفريق الرقمي:</label>
                        <input 
                            type="password" 
                            id="confirmPin"
                            v-model="pinConfirmationData.pin" 
                            placeholder="أدخل رمز الفريق الرقمي"
                            @keyup.enter="confirmPinAction"
                            class="form-control"
                        >
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="closePinConfirmation" class="cancel-btn">إلغاء</button>
                    <button @click="confirmPinAction" class="confirm-btn" :disabled="!pinConfirmationData.pin">
                        {{ pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل' }}
                    </button>
                </div>
            </div>
        </div>
</template>
<style>
/* Enhanced Navigation Styling */
.nav-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
    flex-wrap: wrap;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-xl);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    transition: all var(--transition-base);
    min-width: auto;
    white-space: nowrap;
}

.nav-btn svg {
    flex-shrink: 0;
}

.nav-btn span {
    display: none;
}

@media (min-width: 768px) {
    .nav-btn span {
        display: inline;
    }

    .nav-btn {
        padding: var(--space-3) var(--space-6);
    }
}

/* Enhanced User Section */
.user-section {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.user-button {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-base);
    color: var(--text-primary);
}

.user-button:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.user-name {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.dropdown-arrow {
    transition: transform var(--transition-fast);
}

.user-button:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* Enhanced User Menu */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-2);
    background: rgba(30, 30, 46, 0.95);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-dark-2xl);
    backdrop-filter: blur(20px);
    min-width: 200px;
    z-index: 999999;
    animation: slideDown 0.2s ease-out;
    pointer-events: auto;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    pointer-events: auto;
    user-select: none;
}

.user-menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
}

.user-menu-item:first-child {
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.user-menu-item:last-child {
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.user-menu-item svg {
    flex-shrink: 0;
    opacity: 0.7;
}

.user-menu-item:hover svg {
    opacity: 1;
}

/* Enhanced Content Container */
.content-container {
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: var(--space-6) var(--space-4);
}

/* Enhanced View Header */
.view-header {
    text-align: center;
    margin-bottom: var(--space-8);
    padding: var(--space-6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
}

.view-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.view-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

/* Enhanced Form Styling */
.form-container {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-3xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-dark-lg);
    margin-bottom: var(--space-8);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
    direction: rtl;
    text-align: right;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

/* Enhanced Activity Cards */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-6);
    padding: 0 var(--space-2);
}

.activity-card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-3xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-dark-lg);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.activity-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-dark-xl);
    border-color: var(--border-secondary);
}

.activity-card:hover::before {
    opacity: 1;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-4);
    gap: var(--space-3);
}

.activity-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    line-height: var(--line-height-tight);
}

.activity-status {
    flex-shrink: 0;
}

.activity-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-5);
}

.activity-detail {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.activity-detail svg {
    flex-shrink: 0;
    opacity: 0.7;
}

.activity-actions {
    display: flex;
    gap: var(--space-2);
    margin-top: auto;
    padding-top: var(--space-4);
    border-top: 1px solid var(--border-primary);
}

/* Enhanced Table Styling */
.table-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-2xl);
    border: 2px solid var(--border-primary);
    backdrop-filter: blur(10px);
    overflow: hidden;
    box-shadow: var(--shadow-dark-lg);
    margin-top: var(--space-6);
}

.table-header {
    background: rgba(255, 255, 255, 0.08);
    padding: var(--space-4) var(--space-6);
    border-bottom: 2px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.table-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
}

.table-filters {
    display: flex;
    gap: var(--space-2);
    align-items: center;
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--space-2) var(--space-4);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.table-wrapper {
    overflow-x: auto;
}

.activities-table {
    width: 100%;
    border-collapse: collapse;
    direction: rtl;
}

.activities-table th {
    background: rgba(255, 255, 255, 0.08);
    padding: var(--space-4) var(--space-3);
    text-align: right;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    border-bottom: 2px solid var(--border-primary);
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.activities-table td {
    padding: var(--space-4) var(--space-3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.activities-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.activities-table tr:last-child td {
    border-bottom: none;
}

/* Enhanced Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal-backdrop);
    padding: var(--space-4);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-3xl);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-dark-2xl);
    animation: slideUp 0.3s ease-out;
    z-index: var(--z-modal);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 2px solid var(--border-primary);
    background: var(--bg-tertiary);
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    min-width: auto;
    min-height: auto;
}

.modal-close:hover {
    background: var(--border-primary);
    color: var(--text-primary);
    transform: none;
}

.modal-body {
    padding: var(--space-6);
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

/* Enhanced Loading States */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    padding: var(--space-16) var(--space-4);
    color: var(--text-tertiary);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--accent-primary);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

/* Enhanced Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-16) var(--space-4);
    color: var(--text-tertiary);
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    opacity: 0.5;
}

.empty-state-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    margin-bottom: var(--space-3);
}

.empty-state-description {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--space-6);
}

/* Responsive Design Improvements */
@media (max-width: 480px) {
    .navbar {
        margin: var(--space-2);
    }

    .navbar-content {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .navbar-brand {
        flex-direction: column;
        gap: var(--space-3);
    }

    .nav-actions {
        width: 100%;
        justify-content: center;
    }

    .user-section {
        width: 100%;
        justify-content: center;
    }

    .content-container {
        padding: var(--space-4) var(--space-2);
    }

    .view-header {
        padding: var(--space-4);
    }

    .view-title {
        font-size: var(--font-size-2xl);
    }

    .view-description {
        font-size: var(--font-size-base);
    }

    .activities-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: 0;
    }

    .activity-card {
        padding: var(--space-4);
    }

    .form-container {
        padding: var(--space-4);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-3);
    }

    .table-header {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
    }

    .table-filters {
        justify-content: center;
    }

    .activities-table th,
    .activities-table td {
        padding: var(--space-2);
        font-size: var(--font-size-xs);
    }

    .modal-content {
        margin: var(--space-2);
        max-width: calc(100vw - var(--space-4));
    }

    .modal-header,
    .modal-body {
        padding: var(--space-4);
    }
}

@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .navbar-brand {
        flex-direction: column;
        gap: var(--space-4);
    }

    .activities-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-5);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
    }

    .activities-table {
        font-size: var(--font-size-sm);
    }

    .activities-table th,
    .activities-table td {
        padding: var(--space-3) var(--space-2);
    }
}

/* Enhanced Focus States for Accessibility */
.nav-btn:focus-visible,
.user-button:focus-visible,
.filter-btn:focus-visible,
.modal-close:focus-visible,
.form-group input:focus-visible,
.form-group select:focus-visible,
.form-group textarea:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Enhanced Animation Performance */
.activity-card,
.user-button,
.nav-btn,
.filter-btn,
.modal-content {
    will-change: transform;
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    .activity-card,
    .user-button,
    .nav-btn,
    .filter-btn,
    .modal-content,
    .loading-spinner,
    .dropdown-arrow {
        animation: none !important;
        transition: none !important;
    }

    .activity-card:hover,
    .user-button:hover,
    .nav-btn:hover {
        transform: none !important;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .nav-actions,
    .user-section,
    .activity-actions,
    .table-filters,
    .modal-overlay {
        display: none;
    }

    .activity-card,
    .table-container,
    .form-container {
        box-shadow: none;
        border: 1px solid #000;
        background: white;
        color: black;
    }

    .activities-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .activity-card,
    .table-container,
    .form-container,
    .modal-content {
        border: 3px solid;
        background: var(--bg-primary);
    }

    .nav-btn,
    .filter-btn,
    .user-button {
        border: 2px solid;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        border: 2px solid;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .activity-card,
    .table-container,
    .form-container {
        background: rgba(255, 255, 255, 0.06);
        border-color: var(--border-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        background: rgba(255, 255, 255, 0.06);
    }
}

/* Enhanced Scrollbar Styling */
.table-wrapper::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-wrapper::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
}

.table-wrapper::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: var(--radius-lg);
}

.table-wrapper::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary-400);
}

.user-section {
    position: relative;
    z-index: 100;
}

.user-button {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-button:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: #f5f5f5;
    white-space: nowrap;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    color: #a0aec0;
}

.user-button:hover .dropdown-arrow {
    transform: rotate(180deg);
}



/* Navigation Buttons */
.nav-buttons {
    display: flex;
    gap: 16px;
    align-items: center;
}

/* Arabic RTL alignment for navbar actions */
.nav-actions {
    display: flex;
    gap: 12px;
    flex-direction: row-reverse;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    min-width: 160px; /* unified width with global buttons */
    justify-content: center;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 0.2px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

/* Compact button utility for small actions */
.btn-compact {
    min-width: 96px;
    height: 40px;
    padding: 8px 14px;
    font-size: 13px;
    font-weight: 600;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-btn.active {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-color: #4f46e5;
    color: #ffffff;
    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);
}

.nav-btn.active:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);
}

/* Main Content */
.main-content {
    margin: 24px;
    margin-left: auto;
    margin-right: auto;
}

.submit-view {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.submit-header {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
}

.submit-header h2 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

/* View Activities */
.view-activities {
    margin-top: 32px;
    padding: 0 8px;
}

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 24px;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));
    border: 2px solid rgba(79, 70, 229, 0.3);
    border-radius: 16px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
}

.view-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);
}

.view-header-content {
    text-align: center;
    flex: 1;
}

.view-title {
    color: #ffffff;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.view-description {
    color: #e2e8f0;
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    min-width: 120px;
    height: auto;
}

.export-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.export-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    min-width: 100px;
    height: auto;
}

.refresh-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.refresh-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.refresh-btn svg.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.my-activities-container {
    margin-top: 24px;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 24px;
    margin-top: 24px;
    padding: 0 4px;
}

.activity-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.activity-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);
    border-color: rgba(79, 70, 229, 0.4);
}

.activity-card:hover::before {
    transform: scaleX(1);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid rgba(79, 70, 229, 0.2);
}

.activity-header h4 {
    color: #ffffff;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    margin-right: 16px;
}

.activity-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.edit-btn, .delete-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    min-width: 120px;
    padding: 10px 16px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-align: center;
}

.edit-btn {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    color: white;
    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);
}

.edit-btn:hover {
    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);
}

.delete-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.delete-btn:hover {
    background: linear-gradient(135deg, #c53030, #a02626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

.activity-details {
    margin-top: 16px;
}

.activity-details p {
    color: #e2e8f0;
    font-size: 15px;
    margin: 12px 0;
    line-height: 1.6;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activity-details strong {
    color: #ffffff;
    font-weight: 700;
    min-width: 100px;
    display: inline-block;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #ffffff;
    text-align: center;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    border: 1px solid rgba(79, 70, 229, 0.4);
}

.no-activities-message, .loading-message {
    text-align: center;
    padding: 48px 24px;
    color: #cbd5e0;
    font-size: 18px;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    margin: 24px 0;
}

.loading-message {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
    border-color: rgba(79, 70, 229, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



span {
    font-weight: 600;
    color: #f5f5f5;
}

.view {
    padding: 16px;
    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);
    border: 1px solid #3a3a4e;
    border-radius: 20px;
    max-width: 90%;
    margin: 16px auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

button {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #ffffff;
    height: 48px;
    min-width: 160px;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    cursor: pointer;
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 0.2px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

/* Make icon+label buttons look consistent */
button svg { flex-shrink: 0; }
button span { display: inline-block; }

/* Inputs: improve placeholder visibility */
input::placeholder,
select::placeholder,
textarea::placeholder {
    color: #cbd5e1; /* brighter placeholder */
    opacity: 1;
}

/* Edge/Firefox vendor prefixes */
input::-ms-input-placeholder { color: #cbd5e1; }
input::-webkit-input-placeholder { color: #cbd5e1; }
textarea::-webkit-input-placeholder { color: #cbd5e1; }
select::-ms-input-placeholder { color: #cbd5e1; }

button:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

button:active {
    transform: translateY(0);
}

button span {
    font-size: 16px;
    font-weight: 600;
}

.info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

.base-info-container,
.activities-info-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    direction: rtl;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 24px;
}

.base-info-label {
    font-size: 18px;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 16px;
    text-align: right;
    border-bottom: 2px solid #4f46e5;
    padding-bottom: 8px;
}

.field-label {
    font-size: 14px;
    font-weight: 600;
    color: #cbd5e1;
    margin-bottom: 8px;
    text-align: right;
    display: block;
}

input[type="text"],
input[type="date"],
select {
    direction: rtl;
    width: 100%;
    max-width: 400px;
    margin: 8px 0;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.12);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="date"]:focus,
select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
    background: rgba(255, 255, 255, 0.18);
    color: #ffffff;
}

select {
    cursor: pointer;
}

select option {
    background: #1a1a2e;
    color: #f0f0f0;
    padding: 8px;
}
.activity-item {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.activity-file-input {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    width: fit-content;
    max-width: 200px;
    text-align: center;
    padding: 12px 16px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    align-self: flex-start;
}

.activity-file-input:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #4f46e5;
}

.activity-delete-button {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    border-radius: 12px;
    margin: 8px 0;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.activity-delete-button:hover {
    background: linear-gradient(135deg, #f87171, #ef4444);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .navbar-text {
        flex-direction: column;
        gap: 12px;
    }
    
    .org-name, .team-name {
        font-size: 18px;
    }
    
    .user-button {
        width: 100%;
        justify-content: center;
    }
    
    .info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .base-info-container,
    .activities-info-container {
        padding: 16px;
    }
    
    .view {
        margin: 8px;
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .navbar {
        margin: 8px;
    }
    
    .navbar-content {
        padding: 12px 16px;
    }
    
    .org-name, .team-name {
        font-size: 16px;
    }
    
    .logo {
        height: 50px;
    }
    
    .user-name {
        font-size: 12px;
    }
    
    input[type="text"],
    input[type="date"],
    select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* RTL Improvements */
.view {
    direction: rtl;
}

.navbar-content {
    flex-direction: row-reverse;
}

.navbar-text {
    flex-direction: row-reverse;
    text-align: right;
}

.nav-actions .nav-btn {
    flex-direction: row-reverse;
}

.view-header-content,
.view-title,
.view-description {
    text-align: right;
}

.activities-list {
    direction: rtl;
}

label {
    text-align: right;
    font-weight: 600;
    color: #cbd5e1;
    margin-bottom: 4px;
    display: block;
}

/* Inputs RTL */
input[type="text"],
input[type="date"],
select,
textarea {
    direction: rtl;
    text-align: right;
}

/* Fine color and spacing tweaks for Arabic */
.navbar {
    border-color: #4b5563;
}
.nav-btn {
    letter-spacing: 0.2px; /* tighter Arabic rhythm */
}
.view-header {
    border-color: rgba(79, 70, 229, 0.35);
}
.activity-card {
    padding-inline: 24px;
}
.activity-header h4 {
    margin-left: 0;
    margin-right: 15px; /* move spacing to the right for RTL */
}
.activity-details,
.activity-details p {
    text-align: right;
}

.splitter {
    height: 2px;
    background: linear-gradient(90deg, transparent, #4f46e5, transparent);
    margin: 24px 0;
    border-radius: 1px;
}

/* My Activities Section Styles */
.my-activities-section {
    margin: 30px 0;
    direction: rtl;
    text-align: right;
}

.toggle-activities-btn {
    width: 100%;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toggle-activities-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.toggle-icon {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.my-activities-container {
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.loading-message, .no-activities-message {
    text-align: center;
    padding: 40px 20px;
    color: #8892b0;
    font-size: 16px;
    font-weight: 500;
}

/* Activities Container */
.activities-container {
    margin-top: 20px;
    padding: 0;
}

/* Loading and Empty States */
.loading-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #8892b0;
    font-size: 16px;
    font-weight: 500;
    gap: 16px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-activities-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    color: #8892b0;
    gap: 16px;
}

.empty-icon {
    opacity: 0.6;
    margin-bottom: 8px;
}

.no-activities-message h3 {
    margin: 0;
    color: #e6f1ff;
    font-size: 24px;
    font-weight: 600;
}

.no-activities-message p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

/* Edit Modal - Clean Flexbox Design */
.edit-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
}

.edit-modal {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.edit-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #334155;
    background: #0f172a;
    flex-shrink: 0;
}

.edit-modal-header h3 {
    margin: 0;
    color: #f1f5f9;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #334155;
    color: #f1f5f9;
}

.edit-form {
    padding: 24px;
    direction: rtl;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 0;
}

.form-row.single {
    flex-direction: column;
}

.form-row.double {
    flex-direction: row;
}

.form-row.double .form-group {
    flex: 1;
    min-width: 0;
}

.form-group.full-width {
    grid-column: 1 / -1;
}






/* Table Styles */
.activities-table-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.table-header {
    padding: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-stats {
    display: flex;
    gap: 32px;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-item.clickable {
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stat-item.clickable:hover {
    background: rgba(79, 70, 229, 0.1);
    border-color: rgba(79, 70, 229, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);
}

.stat-item.selected {
    background: rgba(79, 70, 229, 0.2);
    border-color: #4f46e5;
    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);
}

.stat-item.selected .stat-number {
    color: #6366f1;
}

.stat-item.selected .stat-label {
    color: #c7d2fe;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #4f46e5;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #8892b0;
    font-weight: 500;
}

.table-wrapper {
    overflow-x: auto;
}

.activities-table {
    width: 100%;
    border-collapse: collapse;
    direction: rtl;
}

.activities-table th {
    background: rgba(255, 255, 255, 0.08);
    padding: 16px 12px;
    text-align: right;
    font-weight: 600;
    color: #e6f1ff;
    font-size: 14px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
}

.activities-table td {
    padding: 16px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.activity-row {
    transition: all 0.3s ease;
}

.activity-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Column Specific Styles */
.col-title {
    min-width: 250px;
}

.activity-title h4 {
    margin: 0 0 4px 0;
    color: #e6f1ff;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.activity-description {
    margin: 0;
    color: #8892b0;
    font-size: 13px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.owner-info {
    display: flex;
    align-items: center;
}

.date-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.date-main {
    color: #e6f1ff;
    font-weight: 600;
    font-size: 14px;
}

.date-year {
    color: #8892b0;
    font-size: 12px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Status Colors */
.status-executed-paid {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-executed-unpaid {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-accepted {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-needs-edit {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-paid-not-executed {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
    border: 1px solid rgba(168, 85, 247, 0.3);
}

.status-accepted-unpaid {
    background: rgba(6, 182, 212, 0.2);
    color: #06b6d4;
    border: 1px solid rgba(6, 182, 212, 0.3);
}

.status-sent {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.governorate-name, .coordinator-name {
    color: #ccd6f6;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn.edit-btn {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.action-btn.edit-btn:hover {
    background: rgba(59, 130, 246, 0.3);
    transform: scale(1.1);
}

.action-btn.delete-btn {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.action-btn.delete-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    transform: scale(1.1);
}

/* File Download Styles */
.files-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}

.file-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.file-download-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 6px;
    color: #22c55e;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-height: 28px;
    max-width: 140px;
}

.file-download-btn:hover {
    background: rgba(34, 197, 94, 0.25);
    border-color: rgba(34, 197, 94, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.file-download-btn svg {
    flex-shrink: 0;
    color: #22c55e;
}

.file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
}

.no-files {
    color: #64748b;
    font-size: 12px;
    font-style: italic;
    text-align: center;
    padding: 8px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Edit Form Styles */
.edit-form {
    direction: rtl;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 0;
}

.form-group label {
    color: #cbd5e1;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 0;
    text-align: right;
}

.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #475569;
    border-radius: 6px;
    background: #334155;
    color: #f1f5f9;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
    direction: rtl;
    text-align: right;
    box-sizing: border-box;
    outline: none;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    border-color: #3b82f6;
    background: #1e293b;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.form-input::placeholder, .form-textarea::placeholder {
    color: #94a3b8;
}

.form-select {
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23f1f5f9" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 12px;
    padding-left: 32px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
    line-height: 1.5;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #475569;
}

.save-btn, .cancel-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    outline: none;
}

.save-btn {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.save-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.cancel-btn {
    background: transparent;
    color: #94a3b8;
    border-color: #475569;
}

.cancel-btn:hover {
    background: #374151;
    border-color: #6b7280;
}

    /* Responsive Design for My Activities */
    @media (max-width: 768px) {
        .view-activities {
            padding: 0 4px;
        }
        
        .view-header {
            flex-direction: column;
            gap: 16px;
            padding: 24px 16px;
            margin-bottom: 24px;
        }
        
        .view-header-content {
            order: 1;
        }
        
        .refresh-btn {
            order: 2;
            align-self: center;
            min-width: 120px;
            padding: 10px 16px;
            font-size: 13px;
        }
        
        .view-title {
            font-size: 24px;
        }
        
        .view-description {
            font-size: 14px;
        }
        
        .activities-grid {
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 0;
        }
        
        .activity-card {
            padding: 20px;
            margin: 0;
        }
        
        .activity-header {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }
        
        .activity-header h4 {
            font-size: 18px;
            margin-right: 0;
        }
        
        .activity-actions {
            justify-content: flex-end;
            gap: 8px;
        }
        
        .edit-btn, .delete-btn {
            padding: 8px 12px;
            font-size: 13px;
            min-width: 96px;
            height: 40px;
        }
        
        .activity-details p {
            font-size: 14px;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }
        
        .activity-details strong {
            min-width: auto;
        }
        
        .no-activities-message, .loading-message {
            padding: 32px 16px;
            font-size: 16px;
        }
    }
    
    @media (max-width: 480px) {
        .activity-card {
            padding: 12px;
        }
        
        .activity-header h4 {
            font-size: 16px;
        }
        
        .activity-details p {
            font-size: 13px;
        }
        
        .edit-btn, .delete-btn {
            padding: 5px 10px;
            font-size: 11px;
        }
        
        .edit-modal {
            width: 95%;
            max-width: none;
            margin: 20px;
            border-radius: 16px;
        }
        
        .edit-modal-header {
            padding: 20px 24px;
        }
        
        .edit-form {
            padding: 24px;
            gap: 20px;
        }
        
        .form-row {
            grid-template-columns: 1fr;
            gap: 20px;
            padding: 12px;
            margin-bottom: 16px;
            border-radius: 16px;
        }
        
        .form-row:hover {
            transform: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .form-input, .form-textarea, .form-select {
            padding: 16px 20px;
            font-size: 16px;
            border-radius: 14px;
        }
        
        .form-textarea {
            min-height: 100px;
            padding-top: 18px;
            padding-bottom: 18px;
        }
        
        .form-group label {
            font-size: 13px;
            margin-right: 2px;
        }
        
        .form-select {
            padding-left: 35px;
            background-position: calc(100% - 18px) calc(1em + 2px), 
                                 calc(100% - 13px) calc(1em + 2px);
        }
        
        .form-actions {
            flex-direction: column-reverse;
            align-items: stretch;
            gap: 12px;
            padding: 20px 0 0 0;
        }
        
        .save-btn, .cancel-btn {
            width: 100%;
            padding: 14px;
            min-width: auto;
        }
    }

/* Modal Overlay Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #1a1a2e;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease;
    border: 2px solid #4facfe;
    position: relative;
    z-index: 1001;
    padding: 10px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal Header Styles */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Account Settings Modal Styles */
.account-settings-modal {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 10px;
}

.account-form {
    padding: 20px 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #f5f5f5;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    background: #2d3748;
    border: 2px solid #4a5568;
    border-radius: 8px;
    color: #f5f5f5;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    background: #374151;
}

.form-input::placeholder {
    color: #a0aec0;
}

.password-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
}

.password-section h4 {
    margin: 0 0 20px 0;
    color: #4facfe;
    font-size: 16px;
    font-weight: 600;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
}

.save-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 120px;
}

.save-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.save-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cancel-btn {
    background: transparent;
    color: #a0aec0;
    border: 2px solid #4a5568;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 120px;
}

.cancel-btn:hover {
    background: #374151;
    border-color: #6b7280;
    color: #f5f5f5;
}

/* Responsive styles for account modal */
@media (max-width: 768px) {
    .account-settings-modal {
        width: 95%;
        margin: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .save-btn,
    .cancel-btn {
        width: 100%;
    }
}

/* PIN Confirmation Modal Styles */
.pin-modal {
    max-width: 450px;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #4a5568;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.pin-modal .modal-header {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-bottom: 2px solid #4a5568;
    border-radius: 14px 14px 0 0;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pin-modal .modal-header h3 {
    color: #4fc3f7;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.pin-modal .modal-body {
    padding: 25px;
}

.pin-message {
    color: #e2e8f0;
    font-size: 1.1rem;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.6;
}

.pin-modal .form-group {
    margin-bottom: 0;
}

.pin-modal .form-group label {
    color: #4fc3f7;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.pin-modal .form-control {
    background: #2d3748;
    border: 2px solid #4a5568;
    color: #e2e8f0;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.pin-modal .form-control:focus {
    border-color: #4fc3f7;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
    outline: none;
}

.pin-modal .modal-footer {
    padding: 20px 25px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid #4a5568;
}

.confirm-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.confirm-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #c53030, #9c2626);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
}

.confirm-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.pin-modal .cancel-btn {
    background: transparent;
    color: #a0aec0;
    border: 2px solid #4a5568;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.pin-modal .cancel-btn:hover {
    background: #4a5568;
    color: #e2e8f0;
    transform: translateY(-2px);
}
</style>