{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, createTextVNode as _createTextVNode, withModifiers as _withModifiers, renderList as _renderList, Fragment as _Fragment, withKeys as _withKeys, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"navbar\"\n};\nconst _hoisted_2 = {\n  class: \"navbar-content\"\n};\nconst _hoisted_3 = {\n  class: \"nav-actions\"\n};\nconst _hoisted_4 = {\n  class: \"user-section\"\n};\nconst _hoisted_5 = {\n  class: \"user-info\"\n};\nconst _hoisted_6 = {\n  class: \"user-name\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"user-menu\"\n};\nconst _hoisted_8 = {\n  class: \"main-content\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"submit-view\"\n};\nconst _hoisted_10 = {\n  class: \"base-info-container\"\n};\nconst _hoisted_11 = {\n  class: \"activities-info-container\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"view-activities\"\n};\nconst _hoisted_13 = {\n  class: \"view-header\"\n};\nconst _hoisted_14 = {\n  class: \"header-actions\"\n};\nconst _hoisted_15 = [\"disabled\"];\nconst _hoisted_16 = [\"disabled\"];\nconst _hoisted_17 = {\n  class: \"activities-container\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"loading-message\"\n};\nconst _hoisted_19 = {\n  key: 1,\n  class: \"no-activities-message\"\n};\nconst _hoisted_20 = {\n  class: \"edit-modal-header\"\n};\nconst _hoisted_21 = {\n  class: \"edit-form\"\n};\nconst _hoisted_22 = {\n  class: \"form-row\"\n};\nconst _hoisted_23 = {\n  class: \"form-group\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = {\n  class: \"form-row\"\n};\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"form-row\"\n};\nconst _hoisted_29 = {\n  class: \"form-group full-width\"\n};\nconst _hoisted_30 = {\n  class: \"form-actions\"\n};\nconst _hoisted_31 = {\n  class: \"activities-table-container\"\n};\nconst _hoisted_32 = {\n  class: \"table-header\"\n};\nconst _hoisted_33 = {\n  class: \"table-stats\"\n};\nconst _hoisted_34 = {\n  class: \"stat-number\"\n};\nconst _hoisted_35 = {\n  class: \"stat-number\"\n};\nconst _hoisted_36 = {\n  class: \"stat-number\"\n};\nconst _hoisted_37 = {\n  class: \"stat-number\"\n};\nconst _hoisted_38 = {\n  class: \"stat-number\"\n};\nconst _hoisted_39 = {\n  class: \"stat-number\"\n};\nconst _hoisted_40 = {\n  class: \"stat-number\"\n};\nconst _hoisted_41 = {\n  class: \"stat-number\"\n};\nconst _hoisted_42 = {\n  class: \"stat-number\"\n};\nconst _hoisted_43 = {\n  class: \"table-wrapper\"\n};\nconst _hoisted_44 = {\n  class: \"activities-table\"\n};\nconst _hoisted_45 = {\n  class: \"col-title\"\n};\nconst _hoisted_46 = {\n  class: \"activity-title\"\n};\nconst _hoisted_47 = {\n  class: \"activity-description\"\n};\nconst _hoisted_48 = {\n  class: \"col-owner\"\n};\nconst _hoisted_49 = {\n  class: \"owner-info\"\n};\nconst _hoisted_50 = {\n  class: \"col-date\"\n};\nconst _hoisted_51 = {\n  class: \"date-info\"\n};\nconst _hoisted_52 = {\n  class: \"date-main\"\n};\nconst _hoisted_53 = {\n  class: \"date-year\"\n};\nconst _hoisted_54 = {\n  class: \"col-status\"\n};\nconst _hoisted_55 = {\n  class: \"col-files\"\n};\nconst _hoisted_56 = {\n  class: \"files-container\"\n};\nconst _hoisted_57 = {\n  key: 0,\n  class: \"file-buttons\"\n};\nconst _hoisted_58 = [\"onClick\", \"title\"];\nconst _hoisted_59 = {\n  class: \"file-name\"\n};\nconst _hoisted_60 = {\n  key: 1,\n  class: \"no-files\"\n};\nconst _hoisted_61 = {\n  class: \"col-governorate\"\n};\nconst _hoisted_62 = {\n  class: \"governorate-name\"\n};\nconst _hoisted_63 = {\n  class: \"col-coordinator\"\n};\nconst _hoisted_64 = {\n  class: \"coordinator-name\"\n};\nconst _hoisted_65 = {\n  class: \"col-actions\"\n};\nconst _hoisted_66 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_67 = [\"onClick\"];\nconst _hoisted_68 = [\"onClick\"];\nconst _hoisted_69 = {\n  class: \"modal-header\"\n};\nconst _hoisted_70 = {\n  class: \"form-group\"\n};\nconst _hoisted_71 = {\n  class: \"form-group\"\n};\nconst _hoisted_72 = {\n  class: \"password-section\"\n};\nconst _hoisted_73 = {\n  class: \"form-group\"\n};\nconst _hoisted_74 = {\n  class: \"form-group\"\n};\nconst _hoisted_75 = {\n  class: \"form-group\"\n};\nconst _hoisted_76 = {\n  class: \"form-actions\"\n};\nconst _hoisted_77 = [\"disabled\"];\nconst _hoisted_78 = {\n  key: 0\n};\nconst _hoisted_79 = {\n  key: 1\n};\nconst _hoisted_80 = {\n  class: \"modal-header\"\n};\nconst _hoisted_81 = {\n  class: \"modal-body\"\n};\nconst _hoisted_82 = {\n  class: \"pin-message\"\n};\nconst _hoisted_83 = {\n  class: \"form-group\"\n};\nconst _hoisted_84 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_85 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[54] || (_cache[54] = _createElementVNode(\"div\", {\n    class: \"navbar-brand\"\n  }, [_createElementVNode(\"span\", {\n    class: \"navbar-text\"\n  }, \"المجلس الأعلى للشباب\"), _createElementVNode(\"img\", {\n    class: \"navbar-logo\",\n    src: _imports_0,\n    alt: \"شعار الفريق\"\n  }), _createElementVNode(\"span\", {\n    class: \"navbar-text\"\n  }, \"الفريق الوطني للشباب الرقمي\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"btn nav-btn\", {\n      'btn-primary': $data.currentView === 'submit',\n      'btn-secondary': $data.currentView !== 'submit'\n    }]),\n    onClick: _cache[0] || (_cache[0] = $event => $options.setCurrentView('submit'))\n  }, [...(_cache[47] || (_cache[47] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"إرسال النشاطات\", -1 /* CACHED */)]))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"btn nav-btn\", {\n      'btn-primary': $data.currentView === 'view',\n      'btn-secondary': $data.currentView !== 'view'\n    }]),\n    onClick: _cache[1] || (_cache[1] = $event => $options.setCurrentView('view'))\n  }, [...(_cache[48] || (_cache[48] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"النشاطات المرسلة\", -1 /* CACHED */)]))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n    class: \"user-button\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleUserMenu && $options.toggleUserMenu(...args))\n  }, [_cache[50] || (_cache[50] = _createElementVNode(\"div\", {\n    class: \"user-avatar\"\n  }, [_createElementVNode(\"svg\", {\n    width: \"24\",\n    height: \"24\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n  })])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, _toDisplayString($data.user?.full_name || $data.user?.username || 'المستخدم'), 1 /* TEXT */), _cache[49] || (_cache[49] = _createElementVNode(\"svg\", {\n    class: \"dropdown-arrow\",\n    width: \"16\",\n    height: \"16\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M7 10l5 5 5-5z\"\n  })], -1 /* CACHED */))])]), $data.showUserMenu ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n    class: \"user-menu-item\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.openAccountSettings && $options.openAccountSettings(...args))\n  }, [...(_cache[51] || (_cache[51] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"إعدادات الحساب\", -1 /* CACHED */)]))]), $data.user && $data.user.rank === 'admin' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"user-menu-item\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.goToAdmin && $options.goToAdmin(...args))\n  }, [...(_cache[52] || (_cache[52] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"لوحة الإدارة\", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"user-menu-item\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.logout && $options.logout(...args))\n  }, [...(_cache[53] || (_cache[53] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"تسجيل الخروج\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true)])])]), _createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" Submit Activities View \"), $data.currentView === 'submit' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_cache[61] || (_cache[61] = _createElementVNode(\"div\", {\n    class: \"view-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"view-title\"\n  }, \"إرسال النشاطات الجديدة\"), _createElementVNode(\"p\", {\n    class: \"view-description\"\n  }, \"قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [_cache[55] || (_cache[55] = _createElementVNode(\"span\", {\n    class: \"base-info-label\"\n  }, \"المعلومات الأساسية\", -1 /* CACHED */)), _cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n    for: \"coordinator-name\",\n    class: \"field-label\"\n  }, \"منسق المحافظة\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"coordinator-name\",\n    placeholder: \"اسم منسق المحافظة\",\n    class: \"base-info-input\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.coordinatorName = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.coordinatorName]])]), _cache[62] || (_cache[62] = _createElementVNode(\"div\", {\n    class: \"splitter\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_cache[58] || (_cache[58] = _createElementVNode(\"span\", {\n    class: \"base-info-label\"\n  }, \"إضافة نشاطات جديدة\", -1 /* CACHED */)), _cache[59] || (_cache[59] = _createElementVNode(\"div\", {\n    class: \"activities-list\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"button\", {\n    style: {\n      \"margin\": \"10px\",\n      \"max-width\": \"250px\"\n    },\n    onClick: _cache[7] || (_cache[7] = (...args) => $options.AddActivityItem && $options.AddActivityItem(...args))\n  }, [...(_cache[57] || (_cache[57] = [_createElementVNode(\"span\", null, \"إضافة نشاط جديد\", -1 /* CACHED */)]))])]), _cache[63] || (_cache[63] = _createElementVNode(\"div\", {\n    class: \"splitter\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"button\", {\n    style: {\n      \"margin\": \"0 50px\",\n      \"background-color\": \"orange\",\n      \"max-width\": \"180px\"\n    },\n    onClick: _cache[8] || (_cache[8] = (...args) => $options.submitCV && $options.submitCV(...args))\n  }, [...(_cache[60] || (_cache[60] = [_createElementVNode(\"span\", null, \"إرسال النشاطات\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" View Activities Section \"), $data.currentView === 'view' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[67] || (_cache[67] = _createElementVNode(\"div\", {\n    class: \"view-header-content\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"view-title\"\n  }, \"النشاطات\"), _createElementVNode(\"p\", {\n    class: \"view-description\"\n  }, \"عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"button\", {\n    onClick: _cache[9] || (_cache[9] = (...args) => $options.exportToCSV && $options.exportToCSV(...args)),\n    class: \"export-btn\",\n    disabled: $data.loadingActivities || $options.filteredActivities.length === 0,\n    title: \"تصدير إلى Excel\"\n  }, [...(_cache[64] || (_cache[64] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"تصدير CSV\", -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_15), _createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = (...args) => $options.refreshActivities && $options.refreshActivities(...args)),\n    class: \"refresh-btn\",\n    disabled: $data.loadingActivities\n  }, [(_openBlock(), _createElementBlock(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    class: _normalizeClass({\n      'spinning': $data.loadingActivities\n    })\n  }, [...(_cache[65] || (_cache[65] = [_createElementVNode(\"path\", {\n    d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"\n  }, null, -1 /* CACHED */)]))], 2 /* CLASS */)), _cache[66] || (_cache[66] = _createElementVNode(\"span\", null, \"تحديث\", -1 /* CACHED */))], 8 /* PROPS */, _hoisted_16)])]), _createElementVNode(\"div\", _hoisted_17, [$data.loadingActivities ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [...(_cache[68] || (_cache[68] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"جاري تحميل النشاطات...\", -1 /* CACHED */)]))])) : $data.myActivities.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [...(_cache[69] || (_cache[69] = [_createElementVNode(\"svg\", {\n    width: \"64\",\n    height: \"64\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    class: \"empty-icon\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n  })], -1 /* CACHED */), _createElementVNode(\"h3\", null, \"لا توجد نشاطات\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"لا توجد نشاطات متاحة حسب صلاحياتك حالياً\", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Edit Form Modal \"), $data.editingActivity ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"edit-modal-overlay\",\n    onClick: _cache[20] || (_cache[20] = (...args) => $options.cancelEdit && $options.cancelEdit(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"edit-modal\",\n    onClick: _cache[19] || (_cache[19] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_20, [_cache[71] || (_cache[71] = _createElementVNode(\"h3\", null, \"تعديل النشاط\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.cancelEdit && $options.cancelEdit(...args)),\n    class: \"close-btn\"\n  }, [...(_cache[70] || (_cache[70] = [_createElementVNode(\"svg\", {\n    width: \"24\",\n    height: \"24\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n  })], -1 /* CACHED */)]))])]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", null, \"اسم صاحب النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.editingActivity.owner_name = $event),\n    type: \"text\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.owner_name]])]), _createElementVNode(\"div\", _hoisted_24, [_cache[73] || (_cache[73] = _createElementVNode(\"label\", null, \"عنوان النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.editingActivity.title = $event),\n    type: \"text\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.title]])])]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", null, \"تاريخ النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.editingActivity.activity_date = $event),\n    type: \"date\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.activity_date]])]), _createElementVNode(\"div\", _hoisted_27, [_cache[76] || (_cache[76] = _createElementVNode(\"label\", null, \"حالة النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.editingActivity.state = $event),\n    class: \"form-select\"\n  }, [...(_cache[75] || (_cache[75] = [_createStaticVNode(\"<option value=\\\"مرسل\\\">مرسل</option><option value=\\\"منفذ بصرف\\\">منفذ بصرف</option><option value=\\\"منفذ بدون صرف\\\">منفذ بدون صرف</option><option value=\\\"مقبول\\\">مقبول</option><option value=\\\"مرفوض\\\">مرفوض</option><option value=\\\"يحتاج تعديل\\\">يحتاج تعديل</option><option value=\\\"صرف و لم ينفذ\\\">صرف و لم ينفذ</option><option value=\\\"مقبول دون صرف\\\">مقبول دون صرف</option>\", 8)]))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.editingActivity.state]])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[77] || (_cache[77] = _createElementVNode(\"label\", null, \"وصف مختصر:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.editingActivity.short_description = $event),\n    class: \"form-textarea\",\n    rows: \"3\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.short_description]])])]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"button\", {\n    onClick: _cache[17] || (_cache[17] = (...args) => $options.saveActivity && $options.saveActivity(...args)),\n    class: \"save-btn\"\n  }, [...(_cache[78] || (_cache[78] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"\n  })], -1 /* CACHED */), _createTextVNode(\" حفظ التغييرات \", -1 /* CACHED */)]))]), _createElementVNode(\"button\", {\n    onClick: _cache[18] || (_cache[18] = (...args) => $options.cancelEdit && $options.cancelEdit(...args)),\n    class: \"cancel-btn\"\n  }, [...(_cache[79] || (_cache[79] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n  })], -1 /* CACHED */), _createTextVNode(\" إلغاء \", -1 /* CACHED */)]))])])])])])) : $data.myActivities.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" Activities Table \"), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === null\n    }]),\n    onClick: _cache[21] || (_cache[21] = $event => $options.selectFilter(null))\n  }, [_createElementVNode(\"span\", _hoisted_34, _toDisplayString($data.myActivities.length), 1 /* TEXT */), _cache[80] || (_cache[80] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"إجمالي النشاطات\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'منفذ بصرف'\n    }]),\n    onClick: _cache[22] || (_cache[22] = $event => $options.selectFilter('منفذ بصرف'))\n  }, [_createElementVNode(\"span\", _hoisted_35, _toDisplayString($data.myActivities.filter(a => a.state === 'منفذ بصرف').length), 1 /* TEXT */), _cache[81] || (_cache[81] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"منفذ بصرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'منفذ بدون صرف'\n    }]),\n    onClick: _cache[23] || (_cache[23] = $event => $options.selectFilter('منفذ بدون صرف'))\n  }, [_createElementVNode(\"span\", _hoisted_36, _toDisplayString($data.myActivities.filter(a => a.state === 'منفذ بدون صرف').length), 1 /* TEXT */), _cache[82] || (_cache[82] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"منفذ بدون صرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مقبول'\n    }]),\n    onClick: _cache[24] || (_cache[24] = $event => $options.selectFilter('مقبول'))\n  }, [_createElementVNode(\"span\", _hoisted_37, _toDisplayString($data.myActivities.filter(a => a.state === 'مقبول').length), 1 /* TEXT */), _cache[83] || (_cache[83] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مقبول\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مرفوض'\n    }]),\n    onClick: _cache[25] || (_cache[25] = $event => $options.selectFilter('مرفوض'))\n  }, [_createElementVNode(\"span\", _hoisted_38, _toDisplayString($data.myActivities.filter(a => a.state === 'مرفوض').length), 1 /* TEXT */), _cache[84] || (_cache[84] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مرفوض\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'يحتاج تعديل'\n    }]),\n    onClick: _cache[26] || (_cache[26] = $event => $options.selectFilter('يحتاج تعديل'))\n  }, [_createElementVNode(\"span\", _hoisted_39, _toDisplayString($data.myActivities.filter(a => a.state === 'يحتاج تعديل').length), 1 /* TEXT */), _cache[85] || (_cache[85] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"يحتاج تعديل\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'صرف و لم ينفذ'\n    }]),\n    onClick: _cache[27] || (_cache[27] = $event => $options.selectFilter('صرف و لم ينفذ'))\n  }, [_createElementVNode(\"span\", _hoisted_40, _toDisplayString($data.myActivities.filter(a => a.state === 'صرف و لم ينفذ').length), 1 /* TEXT */), _cache[86] || (_cache[86] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"صرف و لم ينفذ\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مقبول دون صرف'\n    }]),\n    onClick: _cache[28] || (_cache[28] = $event => $options.selectFilter('مقبول دون صرف'))\n  }, [_createElementVNode(\"span\", _hoisted_41, _toDisplayString($data.myActivities.filter(a => a.state === 'مقبول دون صرف').length), 1 /* TEXT */), _cache[87] || (_cache[87] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مقبول دون صرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مرسل'\n    }]),\n    onClick: _cache[29] || (_cache[29] = $event => $options.selectFilter('مرسل'))\n  }, [_createElementVNode(\"span\", _hoisted_42, _toDisplayString($data.myActivities.filter(a => a.state === 'مرسل').length), 1 /* TEXT */), _cache[88] || (_cache[88] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مرسل\", -1 /* CACHED */))], 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"table\", _hoisted_44, [_cache[93] || (_cache[93] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", {\n    class: \"col-title\"\n  }, \"عنوان النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-owner\"\n  }, \"صاحب النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-date\"\n  }, \"تاريخ النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-status\"\n  }, \"الحالة\"), _createElementVNode(\"th\", {\n    class: \"col-files\"\n  }, \"الملفات\"), _createElementVNode(\"th\", {\n    class: \"col-governorate\"\n  }, \"المحافظة\"), _createElementVNode(\"th\", {\n    class: \"col-coordinator\"\n  }, \"المنسق\"), _createElementVNode(\"th\", {\n    class: \"col-actions\"\n  }, \"الإجراءات\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredActivities, activity => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: activity.id,\n      class: \"activity-row\"\n    }, [_createElementVNode(\"td\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"h4\", null, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_47, _toDisplayString(activity.short_description || 'لا يوجد وصف'), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"span\", null, _toDisplayString(activity.owner_name), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"span\", _hoisted_52, _toDisplayString(new Date(activity.activity_date).toLocaleDateString('ar-EG', {\n      day: 'numeric',\n      month: 'short'\n    })), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_53, _toDisplayString(new Date(activity.activity_date).getFullYear()), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_54, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"status-badge\", $options.getStatusClass(activity.state)])\n    }, [_cache[89] || (_cache[89] = _createElementVNode(\"div\", {\n      class: \"status-indicator\"\n    }, null, -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString(activity.state), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_55, [_createElementVNode(\"div\", _hoisted_56, [activity.files && activity.files.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(activity.files, file => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: file.id,\n        onClick: $event => $options.downloadFile(file),\n        class: \"file-download-btn\",\n        title: `تحميل ${file.file_name}`\n      }, [_cache[90] || (_cache[90] = _createElementVNode(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n      })], -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_59, _toDisplayString($options.getShortFileName(file.file_name)), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_58);\n    }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_60, \"لا توجد ملفات\"))])]), _createElementVNode(\"td\", _hoisted_61, [_createElementVNode(\"span\", _hoisted_62, _toDisplayString(activity.submission_info.governorate), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_63, [_createElementVNode(\"span\", _hoisted_64, _toDisplayString(activity.submission_info.coordinator_name), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_65, [_createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"button\", {\n      onClick: $event => $options.editActivity(activity),\n      class: \"action-btn edit-btn\",\n      title: \"تعديل\"\n    }, [...(_cache[91] || (_cache[91] = [_createElementVNode(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"\n    })], -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_67), _createElementVNode(\"button\", {\n      onClick: $event => $options.deleteActivity(activity.id),\n      class: \"action-btn delete-btn\",\n      title: \"حذف\"\n    }, [...(_cache[92] || (_cache[92] = [_createElementVNode(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"\n    })], -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_68)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Account Settings Modal \"), $data.showAccountSettings ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"modal-overlay\",\n    onClick: _cache[39] || (_cache[39] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content account-settings-modal\",\n    onClick: _cache[38] || (_cache[38] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_69, [_cache[94] || (_cache[94] = _createElementVNode(\"h3\", null, \"إعدادات الحساب\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[30] || (_cache[30] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"form\", {\n    onSubmit: _cache[37] || (_cache[37] = _withModifiers((...args) => $options.updateAccountSettings && $options.updateAccountSettings(...args), [\"prevent\"])),\n    class: \"account-form\"\n  }, [_createElementVNode(\"div\", _hoisted_70, [_cache[95] || (_cache[95] = _createElementVNode(\"label\", {\n    for: \"fullName\"\n  }, \"الاسم الكامل:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"fullName\",\n    \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.accountForm.fullName = $event),\n    required: \"\",\n    class: \"form-input\",\n    placeholder: \"أدخل اسمك الكامل\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.fullName]])]), _createElementVNode(\"div\", _hoisted_71, [_cache[96] || (_cache[96] = _createElementVNode(\"label\", {\n    for: \"teamPin\"\n  }, \"رمز الفريق الرقمي:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"teamPin\",\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.accountForm.teamPin = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل رمز الفريق الرقمي\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.teamPin]])]), _createElementVNode(\"div\", _hoisted_72, [_cache[100] || (_cache[100] = _createElementVNode(\"h4\", null, \"تغيير كلمة المرور (اختياري)\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_73, [_cache[97] || (_cache[97] = _createElementVNode(\"label\", {\n    for: \"currentPassword\"\n  }, \"كلمة المرور الحالية:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"currentPassword\",\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.accountForm.currentPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل كلمة المرور الحالية\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.currentPassword]])]), _createElementVNode(\"div\", _hoisted_74, [_cache[98] || (_cache[98] = _createElementVNode(\"label\", {\n    for: \"newPassword\"\n  }, \"كلمة المرور الجديدة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"newPassword\",\n    \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.accountForm.newPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.newPassword]])]), _createElementVNode(\"div\", _hoisted_75, [_cache[99] || (_cache[99] = _createElementVNode(\"label\", {\n    for: \"confirmPassword\"\n  }, \"تأكيد كلمة المرور الجديدة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"confirmPassword\",\n    \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.accountForm.confirmPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أعد إدخال كلمة المرور الجديدة\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.confirmPassword]])])]), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[36] || (_cache[36] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args)),\n    class: \"cancel-btn\"\n  }, \"إلغاء\"), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.updatingAccount,\n    class: \"save-btn\"\n  }, [$data.updatingAccount ? (_openBlock(), _createElementBlock(\"span\", _hoisted_78, \"جاري الحفظ...\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_79, \"حفظ التغييرات\"))], 8 /* PROPS */, _hoisted_77)])], 32 /* NEED_HYDRATION */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" PIN Confirmation Modal \"), $data.showPinConfirmation ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"modal-overlay\",\n    onClick: _cache[46] || (_cache[46] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content pin-modal\",\n    onClick: _cache[45] || (_cache[45] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_80, [_cache[101] || (_cache[101] = _createElementVNode(\"h3\", null, \"تأكيد العملية\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[40] || (_cache[40] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"p\", _hoisted_82, _toDisplayString($data.pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_83, [_cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n    for: \"confirmPin\"\n  }, \"رمز الفريق الرقمي:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"confirmPin\",\n    \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.pinConfirmationData.pin = $event),\n    placeholder: \"أدخل رمز الفريق الرقمي\",\n    onKeyup: _cache[42] || (_cache[42] = _withKeys((...args) => $options.confirmPinAction && $options.confirmPinAction(...args), [\"enter\"])),\n    class: \"form-control\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.pinConfirmationData.pin]])])]), _createElementVNode(\"div\", _hoisted_84, [_createElementVNode(\"button\", {\n    onClick: _cache[43] || (_cache[43] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args)),\n    class: \"cancel-btn\"\n  }, \"إلغاء\"), _createElementVNode(\"button\", {\n    onClick: _cache[44] || (_cache[44] = (...args) => $options.confirmPinAction && $options.confirmPinAction(...args)),\n    class: \"confirm-btn\",\n    disabled: !$data.pinConfirmationData.pin\n  }, _toDisplayString($data.pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل'), 9 /* TEXT, PROPS */, _hoisted_85)])])])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementVNode", "_hoisted_1", "_hoisted_2", "src", "alt", "_hoisted_3", "_normalizeClass", "$data", "current<PERSON>iew", "onClick", "_cache", "$event", "$options", "set<PERSON><PERSON><PERSON>View", "width", "height", "viewBox", "fill", "d", "_hoisted_4", "args", "toggleUserMenu", "_hoisted_5", "_hoisted_6", "_toDisplayString", "user", "full_name", "username", "showUserMenu", "_createElementBlock", "_hoisted_7", "openAccountSettings", "rank", "goToAdmin", "logout", "_hoisted_8", "_createCommentVNode", "_hoisted_9", "_hoisted_10", "for", "type", "id", "placeholder", "<PERSON><PERSON><PERSON>", "required", "_hoisted_11", "style", "AddActivityItem", "submitCV", "_hoisted_12", "_hoisted_13", "_hoisted_14", "exportToCSV", "disabled", "loadingActivities", "filteredActivities", "length", "title", "refreshActivities", "_hoisted_17", "_hoisted_18", "myActivities", "_hoisted_19", "editingActivity", "cancelEdit", "_withModifiers", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "owner_name", "_hoisted_24", "_hoisted_25", "_hoisted_26", "activity_date", "_hoisted_27", "state", "_hoisted_28", "_hoisted_29", "short_description", "rows", "_hoisted_30", "saveActivity", "_Fragment", "key", "_hoisted_31", "_hoisted_32", "_hoisted_33", "selected", "<PERSON><PERSON><PERSON><PERSON>", "selectFilter", "_hoisted_34", "_hoisted_35", "filter", "a", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_renderList", "activity", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "Date", "toLocaleDateString", "day", "month", "_hoisted_53", "getFullYear", "_hoisted_54", "getStatusClass", "_hoisted_55", "_hoisted_56", "files", "_hoisted_57", "file", "downloadFile", "file_name", "_hoisted_59", "getShortFileName", "_hoisted_60", "_hoisted_61", "_hoisted_62", "submission_info", "governorate", "_hoisted_63", "_hoisted_64", "coordinator_name", "_hoisted_65", "_hoisted_66", "editActivity", "deleteActivity", "showAccountSettings", "closeAccountSettings", "_hoisted_69", "onSubmit", "updateAccountSettings", "_hoisted_70", "accountForm", "fullName", "_hoisted_71", "teamPin", "_hoisted_72", "_hoisted_73", "currentPassword", "_hoisted_74", "newPassword", "_hoisted_75", "confirmPassword", "_hoisted_76", "updatingAccount", "_hoisted_78", "_hoisted_79", "showPinConfirmation", "closePinConfirmation", "_hoisted_80", "_hoisted_81", "_hoisted_82", "pinConfirmationData", "action", "_hoisted_83", "pin", "onKeyup", "_with<PERSON><PERSON><PERSON>", "confirmPinAction", "_hoisted_84", "_hoisted_85"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\ActivityView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n    name: 'ActivityView',\r\n    data() {\r\n        return {\r\n            activities: [],\r\n            user: null,\r\n            showUserMenu: false,\r\n            tokenCheckInterval: null,\r\n            myActivities: [],\r\n            loadingActivities: false,\r\n            editingActivity: null,\r\n            showMyActivities: false,\r\n            currentView: 'submit',\r\n            coordinatorName: '',\r\n            selectedFilter: null, // null means show all, otherwise filter by state\r\n            showAccountSettings: false,\r\n            accountForm: {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            },\r\n            updatingAccount: false,\r\n            showPinConfirmation: false,\r\n            pinConfirmationData: {\r\n                pin: '',\r\n                action: '', // 'edit' or 'delete'\r\n                activity: null,\r\n                callback: null\r\n            }\r\n        };\r\n    },\r\n    computed: {\r\n        filteredActivities() {\r\n            if (!this.selectedFilter) {\r\n                return this.myActivities;\r\n            }\r\n            return this.myActivities.filter(activity => activity.state === this.selectedFilter);\r\n        }\r\n    },\r\n    methods: {\r\n        selectFilter(state) {\r\n            // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\r\n            this.selectedFilter = this.selectedFilter === state ? null : state;\r\n        },\r\n        toggleUserMenu() {\r\n            this.showUserMenu = !this.showUserMenu;\r\n        },\r\n        logout() {\r\n            try {\r\n                // Clear all stored data\r\n                localStorage.removeItem('ndyt_token');\r\n                localStorage.removeItem('ndyt_user');\r\n                localStorage.removeItem('ndyt_team_pin');\r\n\r\n                // Clear any intervals\r\n                if (this.tokenCheckInterval) {\r\n                    clearInterval(this.tokenCheckInterval);\r\n                    this.tokenCheckInterval = null;\r\n                }\r\n\r\n                // Close user menu\r\n                this.showUserMenu = false;\r\n\r\n                // Show success message\r\n                this.$toast.success('تم تسجيل الخروج بنجاح');\r\n\r\n                // Force navigation to login page with proper base path\r\n                this.$router.replace({ path: '/login' }).catch(err => {\r\n                    console.error('Navigation error:', err);\r\n                    // Fallback: force page reload to login with correct base path\r\n                    window.location.href = '/ndyt/login';\r\n                });\r\n            } catch (error) {\r\n                console.error('Logout error:', error);\r\n                // Fallback: force page reload to login with correct base path\r\n                window.location.href = '/ndyt/login';\r\n            }\r\n        },\r\n\r\n        async checkTokenValidity() {\r\n            const token = localStorage.getItem('ndyt_token');\r\n            if (!token) {\r\n                this.$toast.error('لم يتم العثور على رمز الجلسة. يرجى تسجيل الدخول');\r\n                this.logout();\r\n                return false;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch('http://localhost:3000/api/auth/validate', {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n\r\n                if (response.status === 401 || response.status === 403) {\r\n                    this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\r\n                    this.logout();\r\n                    return false;\r\n                }\r\n\r\n                if (!response.ok) {\r\n                    console.error('Token validation failed:', response.status);\r\n                    return false;\r\n                }\r\n\r\n                return true;\r\n            } catch (error) {\r\n                console.error('Error validating token:', error);\r\n                // Don't logout on network errors, but return false\r\n                return false;\r\n            }\r\n        },\r\n        goToAdmin() {\r\n            this.showUserMenu = false;\r\n            this.$router.push('/admin');\r\n        },\r\n        openAccountSettings() {\r\n            this.showUserMenu = false;\r\n            this.accountForm.fullName = this.user?.full_name || '';\r\n            this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\r\n            this.accountForm.currentPassword = '';\r\n            this.accountForm.newPassword = '';\r\n            this.accountForm.confirmPassword = '';\r\n            this.showAccountSettings = true;\r\n        },\r\n        closeAccountSettings() {\r\n            this.showAccountSettings = false;\r\n            this.accountForm = {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            };\r\n        },\r\n        async updateAccountSettings() {\r\n            // Validate form\r\n            if (!this.accountForm.fullName.trim()) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'يرجى إدخال الاسم الكامل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            this.updatingAccount = true;\r\n\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const updateData = {\r\n                    full_name: this.accountForm.fullName.trim(),\r\n                    team_pin: this.accountForm.teamPin.trim()\r\n                };\r\n\r\n                // Only include password if user wants to change it\r\n                if (this.accountForm.newPassword) {\r\n                    updateData.current_password = this.accountForm.currentPassword;\r\n                    updateData.new_password = this.accountForm.newPassword;\r\n                }\r\n\r\n                const response = await fetch('/api/v1/ndyt-activities/user/update-profile', {\r\n                    method: 'PUT',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`\r\n                    },\r\n                    body: JSON.stringify(updateData)\r\n                });\r\n\r\n                if (response.ok) {\r\n                    await response.json();\r\n                    \r\n                    // Update local user data\r\n                    this.user.full_name = this.accountForm.fullName.trim();\r\n                    localStorage.setItem('ndyt_user', JSON.stringify(this.user));\r\n                    localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\r\n\r\n                    await this.$swal.fire({\r\n                        title: 'تم التحديث بنجاح',\r\n                        text: 'تم تحديث معلومات الحساب بنجاح',\r\n                        icon: 'success'\r\n                    });\r\n\r\n                    this.closeAccountSettings();\r\n                } else {\r\n                    const errorData = await response.json();\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في التحديث',\r\n                        text: errorData.error || 'فشل في تحديث معلومات الحساب',\r\n                        icon: 'error'\r\n                    });\r\n                }\r\n            } catch (error) {\r\n                console.error('Error updating account:', error);\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في الاتصال',\r\n                    text: 'حدث خطأ أثناء تحديث معلومات الحساب',\r\n                    icon: 'error'\r\n                });\r\n            } finally {\r\n                this.updatingAccount = false;\r\n            }\r\n        },\r\n        async submitCV() {\r\n            try {\r\n                // Validate coordinator name\r\n                if (!this.coordinatorName.trim()) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إدخال اسم منسق المحافظة',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                // Collect all activity items\r\n                const activityItems = document.querySelectorAll('.activity-item');\r\n                \r\n                if (activityItems.length === 0) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إضافة نشاط واحد على الأقل',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const activities = [];\r\n                let hasErrors = false;\r\n                \r\n                // Process each activity item\r\n                for (let index = 0; index < activityItems.length; index++) {\r\n                    const item = activityItems[index];\r\n                    const inputs = item.querySelectorAll('.activity-input');\r\n                    const ownerName = inputs[0]?.value?.trim();\r\n                    const title = inputs[1]?.value?.trim();\r\n                    const shortDescription = inputs[2]?.value?.trim();\r\n                    const activityDate = inputs[3]?.value;\r\n                    const state = inputs[4]?.value;\r\n                    \r\n                    // Validate required fields\r\n                    if (!ownerName || !title || !activityDate || !state) {\r\n                        this.$swal.fire({\r\n                            title: 'خطأ في البيانات',\r\n                            text: `يرجى ملء جميع الحقول المطلوبة للنشاط رقم ${index + 1}`,\r\n                            icon: 'error'\r\n                        });\r\n                        hasErrors = true;\r\n                        break;\r\n                    }\r\n                    \r\n                    let fileId = null;\r\n                    \r\n                    // Handle file upload if a file is selected\r\n                    if (item.selectedFile) {\r\n                        try {\r\n                            const formData = new FormData();\r\n                            formData.append('file', item.selectedFile);\r\n                            \r\n                            const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {\r\n                                method: 'POST',\r\n                                headers: {\r\n                                    'Authorization': `Bearer ${token}`\r\n                                },\r\n                                body: formData\r\n                            });\r\n                            \r\n                            if (uploadResponse.ok) {\r\n                                const uploadResult = await uploadResponse.json();\r\n                                if (uploadResult.file && uploadResult.file.id) {\r\n                                    fileId = uploadResult.file.id;\r\n                                }\r\n                            } else {\r\n                                const errorData = await uploadResponse.json();\r\n                                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\r\n                                hasErrors = true;\r\n                                break;\r\n                            }\r\n                        } catch (uploadError) {\r\n                            console.error('File upload error:', uploadError);\r\n                            this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\r\n                            hasErrors = true;\r\n                            break;\r\n                        }\r\n                    }\r\n                    \r\n                    activities.push({\r\n                        owner_name: ownerName,\r\n                        title: title,\r\n                        short_description: shortDescription || '',\r\n                        activity_date: activityDate,\r\n                        state: state,\r\n                        file_id: fileId\r\n                    });\r\n                }\r\n                \r\n                if (hasErrors) return;\r\n                \r\n                // Prepare submission data\r\n                const submissionData = {\r\n                    coordinator_name: this.coordinatorName.trim(),\r\n                    activities: activities\r\n                };\r\n                \r\n                // Submit to backend\r\n                const response = await fetch('/api/v1/ndyt-activities/submissions', {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    },\r\n                    body: JSON.stringify(submissionData)\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    await response.json();\r\n                    this.$toast.success('تم إرسال النشاطات بنجاح!');\r\n                    \r\n                    // Clear the form\r\n                    this.coordinatorName = '';\r\n                    \r\n                    // Remove all activity items\r\n                    activityItems.forEach(item => item.remove());\r\n                    \r\n                    // Refresh my activities if they're currently shown\r\n                    if (this.showMyActivities) {\r\n                        this.fetchMyActivities();\r\n                    }\r\n                } else {\r\n                    let errorData = {};\r\n                    try {\r\n                        errorData = await response.json();\r\n                    } catch (jsonError) {\r\n                        console.error('Error parsing response JSON:', jsonError);\r\n                    }\r\n                    this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\r\n                }\r\n                \r\n            } catch (error) {\r\n                console.error('Error submitting activities:', error);\r\n                this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\r\n            }\r\n        },\r\n        async fetchMyActivities() {\r\n            this.loadingActivities = true;\r\n            this.myActivities = []; // Clear existing activities\r\n            \r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                \r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const response = await fetch('/api/v1/ndyt-activities/activities', {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    \r\n                    // The new endpoint returns activities directly with role-based filtering\r\n                    if (data.activities && Array.isArray(data.activities)) {\r\n                        this.myActivities = data.activities.map(activity => ({\r\n                            ...activity,\r\n                            submission_info: {\r\n                                id: activity.submission_id,\r\n                                governorate: activity.governorate,\r\n                                coordinator_name: activity.coordinator_name,\r\n                                created_at: activity.created_at\r\n                            }\r\n                        }));\r\n                    }\r\n                } else if (response.status === 401) {\r\n                    this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\r\n                    localStorage.removeItem('ndyt_token');\r\n                    localStorage.removeItem('ndyt_user');\r\n                    this.$router.push('/login');\r\n                } else if (response.status === 404) {\r\n                    // No activities found - this is normal, not an error\r\n                    this.myActivities = [];\r\n                } else {\r\n                    const errorData = await response.json().catch(() => ({}));\r\n                    console.error('Failed to fetch activities:', errorData);\r\n                    this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\r\n                }\r\n            } catch (error) {\r\n                console.error('Error fetching activities:', error);\r\n                if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n                    this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\r\n                } else {\r\n                    this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\r\n                }\r\n            } finally {\r\n                this.loadingActivities = false;\r\n            }\r\n        },\r\n        toggleMyActivities() {\r\n            this.showMyActivities = !this.showMyActivities;\r\n            if (this.showMyActivities && this.myActivities.length === 0) {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        editActivity(activity) {\r\n            this.showPinConfirmationModal('edit', activity, () => {\r\n                this.openEditModal(activity);\r\n            });\r\n        },\r\n        openEditModal(activity) {\r\n            this.editingActivity = { ...activity };\r\n            // Ensure date is properly formatted for date input (YYYY-MM-DD)\r\n            if (this.editingActivity.activity_date) {\r\n                const date = new Date(this.editingActivity.activity_date);\r\n                // Use timezone-safe formatting to avoid date shifting\r\n                const year = date.getFullYear();\r\n                const month = String(date.getMonth() + 1).padStart(2, '0');\r\n                const day = String(date.getDate()).padStart(2, '0');\r\n                this.editingActivity.activity_date = `${year}-${month}-${day}`;\r\n            }\r\n        },\r\n        async saveActivity() {\r\n            if (!this.editingActivity) return;\r\n            \r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                const response = await fetch(`/api/v1/ndyt-activities/activities/${this.editingActivity.id}`, {\r\n                    method: 'PUT',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    },\r\n                    body: JSON.stringify({\r\n                        owner_name: this.editingActivity.owner_name,\r\n                        title: this.editingActivity.title,\r\n                        short_description: this.editingActivity.short_description,\r\n                        activity_date: this.editingActivity.activity_date,\r\n                        state: this.editingActivity.state\r\n                    })\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    const updatedActivity = await response.json();\r\n                    // Update the activity in the list\r\n                    const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\r\n                    if (index !== -1) {\r\n                        this.myActivities[index] = { ...updatedActivity, submission_info: this.myActivities[index].submission_info };\r\n                    }\r\n                    this.editingActivity = null;\r\n                    this.$toast.success('تم تحديث النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في تحديث النشاط');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error updating activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء تحديث النشاط');\r\n            }\r\n        },\r\n        cancelEdit() {\r\n            this.editingActivity = null;\r\n        },\r\n        deleteActivity(activityId) {\r\n            const activity = this.myActivities.find(a => a.id === activityId);\r\n            this.showPinConfirmationModal('delete', activity, async () => {\r\n                await this.performDeleteActivity(activityId);\r\n            });\r\n        },\r\n        async performDeleteActivity(activityId) {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {\r\n                    method: 'DELETE',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    }\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    this.myActivities = this.myActivities.filter(a => a.id !== activityId);\r\n                    this.$toast.success('تم حذف النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في حذف النشاط');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error deleting activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء حذف النشاط');\r\n            }\r\n        },\r\n        getStatusClass(status) {\r\n            const statusMap = {\r\n                'منفذ بصرف': 'status-executed-paid',\r\n                'منفذ بدون صرف': 'status-executed-unpaid',\r\n                'مقبول': 'status-accepted',\r\n                'مرفوض': 'status-rejected',\r\n                'يحتاج تعديل': 'status-needs-edit',\r\n                'صرف و لم ينفذ': 'status-paid-not-executed',\r\n                'مقبول دون صرف': 'status-accepted-unpaid',\r\n                'مرسل': 'status-sent'\r\n            };\r\n            return statusMap[status] || 'status-default';\r\n        },\r\n        AddActivityItem() {\r\n            const activityDiv = document.createElement('div');\r\n            activityDiv.className = 'activity-item';\r\n            const activityOwner = document.createElement('input');\r\n            activityOwner.type = 'text';\r\n            activityOwner.placeholder = 'اسم صاحب االنشاط';\r\n            activityOwner.className = 'activity-input';\r\n            activityDiv.appendChild(activityOwner);\r\n            const activityTitle = document.createElement('input');\r\n            activityTitle.type = 'text';\r\n            activityTitle.placeholder = 'عنوان النشاط';\r\n            activityTitle.className = 'activity-input';\r\n            activityDiv.appendChild(activityTitle);\r\n            const activityShortDescription = document.createElement('input');\r\n            activityShortDescription.type = 'text';\r\n            activityShortDescription.placeholder = 'وصف قصير للنشاط';\r\n            activityShortDescription.className = 'activity-input';\r\n            activityDiv.appendChild(activityShortDescription);\r\n            const activityDateLabel = document.createElement('label');\r\n            activityDateLabel.textContent = 'تاريخ النشاط';\r\n            activityDiv.appendChild(activityDateLabel);\r\n            const activityDate = document.createElement('input');\r\n            activityDate.type = 'date';\r\n            activityDate.className = 'activity-input';\r\n            activityDiv.appendChild(activityDate);\r\n            const activityStateLabel = document.createElement('label');\r\n            activityStateLabel.textContent = 'حالة النشاط';\r\n            activityDiv.appendChild(activityStateLabel);\r\n            const activityApplyState = document.createElement('select');\r\n            activityApplyState.className = 'activity-input';\r\n            const states = \r\n            ['منفذ بصرف',  'منفذ بدون صرف', 'مقبول', 'مرفوض',\r\n            'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'\r\n            ];\r\n            states.forEach(state => {\r\n                const option = document.createElement('option');\r\n                option.value = state;\r\n                option.textContent = state;\r\n                activityApplyState.appendChild(option);\r\n            });\r\n            const activityFileLabel = document.createElement('label');\r\n            activityFileLabel.textContent = 'ملف النشاط';\r\n            const activityFileInput = document.createElement('label');\r\n            activityFileInput.className = 'activity-file-input';\r\n            activityFileInput.textContent = 'اختر ملف';\r\n            \r\n            // Create hidden file input\r\n            const hiddenFileInput = document.createElement('input');\r\n            hiddenFileInput.type = 'file';\r\n            hiddenFileInput.accept = '.pdf,.doc,.docx,.jpg,.png';\r\n            hiddenFileInput.style.display = 'none';\r\n            hiddenFileInput.onchange = (event) => {\r\n                const file = event.target.files[0];\r\n                if (file) {\r\n                    const fileName = file.name;\r\n                    activityFileInput.textContent = fileName;\r\n                    // Store the file object in the activity div for later use\r\n                    activityDiv.selectedFile = file;\r\n                } else {\r\n                    activityFileInput.textContent = 'اختر ملف';\r\n                    activityDiv.selectedFile = null;\r\n                }\r\n            };\r\n            \r\n            activityFileInput.onclick = () => {\r\n                hiddenFileInput.click();\r\n            };\r\n            \r\n            // Append hidden input to activity div\r\n            activityDiv.appendChild(hiddenFileInput);\r\n            const activityDeleteButton = document.createElement('button');\r\n            activityDeleteButton.className = 'activity-delete-button';\r\n            activityDeleteButton.textContent = 'حذف النشاط';\r\n            activityDeleteButton.onclick = () => {\r\n                activityDiv.remove();\r\n            };\r\n            activityDiv.appendChild(activityApplyState);\r\n            activityDiv.appendChild(activityFileLabel);\r\n            activityDiv.appendChild(activityFileInput);\r\n            activityDiv.appendChild(activityDeleteButton);\r\n            this.activities.push(activityDiv);\r\n            document.querySelector('.activities-list').appendChild(activityDiv);\r\n        },\r\n        handleClickOutside(event) {\r\n            const userSection = event.target.closest('.user-section');\r\n            if (!userSection) {\r\n                this.showUserMenu = false;\r\n            }\r\n        },\r\n        setCurrentView(view) {\r\n            this.currentView = view;\r\n            // Automatically fetch activities when switching to view tab\r\n            if (view === 'view') {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        refreshActivities() {\r\n            this.fetchMyActivities();\r\n        },\r\n        async loadCoordinatorName() {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                if (!token) return;\r\n\r\n                const response = await fetch('/api/v1/ndyt-activities/coordinator', {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    this.coordinatorName = data.coordinator_name || '';\r\n                } else if (response.status === 404) {\r\n                    // No coordinator found for user's governorate\r\n                    this.coordinatorName = '';\r\n                }\r\n            } catch (error) {\r\n                console.error('Error loading coordinator name:', error);\r\n                // Don't show alert for this error as it's not critical\r\n            }\r\n        },\r\n        exportToCSV() {\r\n            if (this.filteredActivities.length === 0) {\r\n                this.$toast.error('لا توجد نشاطات للتصدير');\r\n                return;\r\n            }\r\n\r\n            // Define CSV headers in Arabic\r\n            const headers = [\r\n                'عنوان النشاط',\r\n                'صاحب النشاط', \r\n                'وصف النشاط',\r\n                'تاريخ النشاط',\r\n                'حالة النشاط',\r\n                'المحافظة',\r\n                'منسق المحافظة',\r\n                'تاريخ الإرسال'\r\n            ];\r\n\r\n            // Convert activities data to CSV format\r\n            const csvData = this.filteredActivities.map(activity => {\r\n                return [\r\n                    `\"${activity.title || ''}\"`,\r\n                    `\"${activity.owner_name || ''}\"`,\r\n                    `\"${activity.short_description || ''}\"`,\r\n                    activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '',\r\n                    `\"${activity.state || ''}\"`,\r\n                    `\"${activity.submission_info?.governorate || ''}\"`,\r\n                    `\"${activity.submission_info?.coordinator_name || ''}\"`,\r\n                    activity.submission_info?.created_at ? new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''\r\n                ].join(',');\r\n            });\r\n\r\n            // Combine headers and data\r\n            const csvContent = [headers.join(','), ...csvData].join('\\n');\r\n\r\n            // Add BOM for proper Arabic text encoding in Excel\r\n            const BOM = '\\uFEFF';\r\n            const csvWithBOM = BOM + csvContent;\r\n\r\n            // Create and download the file\r\n            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });\r\n            const link = document.createElement('a');\r\n            \r\n            if (link.download !== undefined) {\r\n                const url = URL.createObjectURL(blob);\r\n                link.setAttribute('href', url);\r\n                \r\n                // Generate filename with current date and filter info\r\n                const currentDate = new Date().toISOString().split('T')[0];\r\n                const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\r\n                const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\r\n                \r\n                link.setAttribute('download', filename);\r\n                link.style.visibility = 'hidden';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\r\n            } else {\r\n                this.$toast.error('المتصفح لا يدعم تحميل الملفات');\r\n            }\r\n        },\r\n        // PIN Confirmation Methods\r\n        showPinConfirmationModal(action, activity, callback) {\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: action,\r\n                activity: activity,\r\n                callback: callback\r\n            };\r\n            this.showPinConfirmation = true;\r\n            // Focus on PIN input after modal opens\r\n            this.$nextTick(() => {\r\n                const pinInput = document.getElementById('confirmPin');\r\n                if (pinInput) {\r\n                    pinInput.focus();\r\n                }\r\n            });\r\n        },\r\n        closePinConfirmation() {\r\n            this.showPinConfirmation = false;\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: '',\r\n                activity: null,\r\n                callback: null\r\n            };\r\n        },\r\n        async confirmPinAction() {\r\n            const enteredPin = this.pinConfirmationData.pin;\r\n            const storedPin = localStorage.getItem('ndyt_team_pin');\r\n            \r\n            if (enteredPin !== storedPin) {\r\n                this.$toast.error('رمز الفريق الرقمي غير صحيح');\r\n                return;\r\n            }\r\n            \r\n            // Store callback before closing modal\r\n            const callback = this.pinConfirmationData.callback;\r\n            \r\n            // Close modal first\r\n            this.closePinConfirmation();\r\n            \r\n            // Execute the callback function after DOM update\r\n            if (callback) {\r\n                this.$nextTick(async () => {\r\n                    await callback();\r\n                });\r\n            }\r\n        },\r\n        downloadFile(file) {\r\n            try {\r\n                // Create a direct download link\r\n                const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\r\n                \r\n                // Create a temporary anchor element to trigger download\r\n                const link = document.createElement('a');\r\n                link.href = fileUrl;\r\n                link.download = file.file_name;\r\n                link.target = '_blank'; // Open in new tab as fallback\r\n                link.style.display = 'none';\r\n                \r\n                // Append to body, click, and remove\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\r\n            } catch (error) {\r\n                console.error('Download error:', error);\r\n                this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\r\n            }\r\n        },\r\n        getShortFileName(fileName) {\r\n            // Truncate long file names for display\r\n            if (fileName.length > 20) {\r\n                const extension = fileName.split('.').pop();\r\n                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\r\n                return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\r\n            }\r\n            return fileName;\r\n        }\r\n    },\r\n    async mounted() {\r\n        // Check token validity first\r\n        const isValidToken = await this.checkTokenValidity();\r\n        if (!isValidToken) {\r\n            return; // Will redirect to login\r\n        }\r\n\r\n        // Get user info from localStorage\r\n        const userStr = localStorage.getItem('ndyt_user');\r\n        if (userStr) {\r\n            this.user = JSON.parse(userStr);\r\n        }\r\n\r\n        // Add click outside listener\r\n        document.addEventListener('click', this.handleClickOutside);\r\n\r\n        // Load coordinator name from previous submissions\r\n        this.loadCoordinatorName();\r\n\r\n        // Set up periodic token validation (every 5 minutes)\r\n        this.tokenCheckInterval = setInterval(() => {\r\n            this.checkTokenValidity();\r\n        }, 5 * 60 * 1000);\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n        if (this.tokenCheckInterval) {\r\n            clearInterval(this.tokenCheckInterval);\r\n        }\r\n    }\r\n}\r\n</script>\r\n<template>\r\n    \r\n        <div class=\"navbar\">\r\n            <div class=\"navbar-content\">\r\n                <div class=\"navbar-brand\">\r\n                    <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n                    <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n                    <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n                </div>\r\n                <div class=\"nav-actions\">\r\n                    <button class=\"btn nav-btn\" :class=\"{ 'btn-primary': currentView === 'submit', 'btn-secondary': currentView !== 'submit' }\" @click=\"setCurrentView('submit')\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/>\r\n                        </svg>\r\n                        <span>إرسال النشاطات</span>\r\n                    </button>\r\n                    <button class=\"btn nav-btn\" :class=\"{ 'btn-primary': currentView === 'view', 'btn-secondary': currentView !== 'view' }\" @click=\"setCurrentView('view')\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                        </svg>\r\n                        <span>النشاطات المرسلة</span>\r\n                    </button>\r\n                </div>\r\n                <div class=\"user-section\">\r\n                    <div class=\"user-button\" @click=\"toggleUserMenu\">\r\n                        <div class=\"user-avatar\">\r\n                            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\r\n                            </svg>\r\n                        </div>\r\n                        <div class=\"user-info\">\r\n                            <span class=\"user-name\">{{ user?.full_name || user?.username || 'المستخدم' }}</span>\r\n                            <svg class=\"dropdown-arrow\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M7 10l5 5 5-5z\"/>\r\n                            </svg>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"showUserMenu\" class=\"user-menu\">\r\n                        <div class=\"user-menu-item\" @click=\"openAccountSettings\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"/>\r\n                            </svg>\r\n                            <span>إعدادات الحساب</span>\r\n                        </div>\r\n                        <div v-if=\"user && user.rank === 'admin'\" class=\"user-menu-item\" @click=\"goToAdmin\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"/>\r\n                            </svg>\r\n                            <span>لوحة الإدارة</span>\r\n                        </div>\r\n                        <div class=\"user-menu-item\" @click=\"logout\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"/>\r\n                            </svg>\r\n                            <span>تسجيل الخروج</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"main-content\">\r\n            <!-- Submit Activities View -->\r\n            <div v-if=\"currentView === 'submit'\" class=\"submit-view\">\r\n                <div class=\"view-header\">\r\n                    <h2 class=\"view-title\">إرسال النشاطات الجديدة</h2>\r\n                    <p class=\"view-description\">قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة</p>\r\n                </div>\r\n                <div class=\"base-info-container\">\r\n                    <span class=\"base-info-label\">المعلومات الأساسية</span>\r\n                    <label for=\"coordinator-name\" class=\"field-label\">منسق المحافظة</label>\r\n                    <input type=\"text\" id=\"coordinator-name\" placeholder=\"اسم منسق المحافظة\" class=\"base-info-input\" v-model=\"coordinatorName\" required>\r\n                </div>\r\n                \r\n                <div class=\"splitter\"></div>\r\n                \r\n                <div class=\"activities-info-container\">\r\n                    <span class=\"base-info-label\">إضافة نشاطات جديدة</span>\r\n                    <div class=\"activities-list\">\r\n                    </div>\r\n                    <button style=\"margin: 10px; max-width: 250px;\" @click=\"AddActivityItem\">\r\n                        <span>إضافة نشاط جديد</span>\r\n                    </button>\r\n                </div>\r\n                <div class=\"splitter\"></div>\r\n                <button style=\"margin: 0 50px; background-color: orange; max-width: 180px;\" @click=\"submitCV\">\r\n                    <span>إرسال النشاطات</span>\r\n                </button>\r\n            </div>\r\n            \r\n            <!-- View Activities Section -->\r\n            <div v-if=\"currentView === 'view'\" class=\"view-activities\">\r\n                <div class=\"view-header\">\r\n                    <div class=\"view-header-content\">\r\n                        <h2 class=\"view-title\">النشاطات</h2>\r\n                        <p class=\"view-description\">عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)</p>\r\n                    </div>\r\n                    <div class=\"header-actions\">\r\n                        <button @click=\"exportToCSV\" class=\"export-btn\" :disabled=\"loadingActivities || filteredActivities.length === 0\" title=\"تصدير إلى Excel\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                            </svg>\r\n                            <span>تصدير CSV</span>\r\n                        </button>\r\n                        <button @click=\"refreshActivities\" class=\"refresh-btn\" :disabled=\"loadingActivities\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\" :class=\"{ 'spinning': loadingActivities }\">\r\n                                <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\r\n                            </svg>\r\n                            <span>تحديث</span>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"activities-container\">\r\n                    <div v-if=\"loadingActivities\" class=\"loading-message\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <span>جاري تحميل النشاطات...</span>\r\n                    </div>\r\n                    \r\n                    <div v-else-if=\"myActivities.length === 0\" class=\"no-activities-message\">\r\n                        <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"empty-icon\">\r\n                            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n                        </svg>\r\n                        <h3>لا توجد نشاطات</h3>\r\n                        <p>لا توجد نشاطات متاحة حسب صلاحياتك حالياً</p>\r\n                    </div>\r\n                    \r\n                    <!-- Edit Form Modal -->\r\n                    <div v-if=\"editingActivity\" class=\"edit-modal-overlay\" @click=\"cancelEdit\">\r\n                        <div class=\"edit-modal\" @click.stop>\r\n                            <div class=\"edit-modal-header\">\r\n                                <h3>تعديل النشاط</h3>\r\n                                <button @click=\"cancelEdit\" class=\"close-btn\">\r\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                    </svg>\r\n                                </button>\r\n                            </div>\r\n                            <div class=\"edit-form\">\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group\">\r\n                                        <label>اسم صاحب النشاط:</label>\r\n                                        <input v-model=\"editingActivity.owner_name\" type=\"text\" class=\"form-input\">\r\n                                    </div>\r\n                                    <div class=\"form-group\">\r\n                                        <label>عنوان النشاط:</label>\r\n                                        <input v-model=\"editingActivity.title\" type=\"text\" class=\"form-input\">\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group\">\r\n                                        <label>تاريخ النشاط:</label>\r\n                                        <input v-model=\"editingActivity.activity_date\" type=\"date\" class=\"form-input\">\r\n                                    </div>\r\n                                    <div class=\"form-group\">\r\n                                        <label>حالة النشاط:</label>\r\n                                        <select v-model=\"editingActivity.state\" class=\"form-select\">\r\n                                            <option value=\"مرسل\">مرسل</option>\r\n                                            <option value=\"منفذ بصرف\">منفذ بصرف</option>\r\n                                            <option value=\"منفذ بدون صرف\">منفذ بدون صرف</option>\r\n                                            <option value=\"مقبول\">مقبول</option>\r\n                                            <option value=\"مرفوض\">مرفوض</option>\r\n                                            <option value=\"يحتاج تعديل\">يحتاج تعديل</option>\r\n                                            <option value=\"صرف و لم ينفذ\">صرف و لم ينفذ</option>\r\n                                            <option value=\"مقبول دون صرف\">مقبول دون صرف</option>\r\n                                        </select>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group full-width\">\r\n                                        <label>وصف مختصر:</label>\r\n                                        <textarea v-model=\"editingActivity.short_description\" class=\"form-textarea\" rows=\"3\"></textarea>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-actions\">\r\n                                    <button @click=\"saveActivity\" class=\"save-btn\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                            <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\r\n                                        </svg>\r\n                                        حفظ التغييرات\r\n                                    </button>\r\n                                    <button @click=\"cancelEdit\" class=\"cancel-btn\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                            <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                        </svg>\r\n                                        إلغاء\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <!-- Activities Table -->\r\n                    <div v-else-if=\"myActivities.length > 0\" class=\"activities-table-container\">\r\n                        <div class=\"table-header\">\r\n                            <div class=\"table-stats\">\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === null }\" @click=\"selectFilter(null)\">\r\n                                    <span class=\"stat-number\">{{ myActivities.length }}</span>\r\n                                    <span class=\"stat-label\">إجمالي النشاطات</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بصرف' }\" @click=\"selectFilter('منفذ بصرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بصرف').length }}</span>\r\n                                    <span class=\"stat-label\">منفذ بصرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بدون صرف' }\" @click=\"selectFilter('منفذ بدون صرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بدون صرف').length }}</span>\r\n                                    <span class=\"stat-label\">منفذ بدون صرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول' }\" @click=\"selectFilter('مقبول')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول').length }}</span>\r\n                                    <span class=\"stat-label\">مقبول</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرفوض' }\" @click=\"selectFilter('مرفوض')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرفوض').length }}</span>\r\n                                    <span class=\"stat-label\">مرفوض</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'يحتاج تعديل' }\" @click=\"selectFilter('يحتاج تعديل')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'يحتاج تعديل').length }}</span>\r\n                                    <span class=\"stat-label\">يحتاج تعديل</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'صرف و لم ينفذ' }\" @click=\"selectFilter('صرف و لم ينفذ')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'صرف و لم ينفذ').length }}</span>\r\n                                    <span class=\"stat-label\">صرف و لم ينفذ</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول دون صرف' }\" @click=\"selectFilter('مقبول دون صرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول دون صرف').length }}</span>\r\n                                    <span class=\"stat-label\">مقبول دون صرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرسل' }\" @click=\"selectFilter('مرسل')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرسل').length }}</span>\r\n                                    <span class=\"stat-label\">مرسل</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        \r\n                        <div class=\"table-wrapper\">\r\n                            <table class=\"activities-table\">\r\n                                <thead>\r\n                                    <tr>\r\n                                        <th class=\"col-title\">عنوان النشاط</th>\r\n                                        <th class=\"col-owner\">صاحب النشاط</th>\r\n                                        <th class=\"col-date\">تاريخ النشاط</th>\r\n                                        <th class=\"col-status\">الحالة</th>\r\n                                        <th class=\"col-files\">الملفات</th>\r\n                                        <th class=\"col-governorate\">المحافظة</th>\r\n                                        <th class=\"col-coordinator\">المنسق</th>\r\n                                        <th class=\"col-actions\">الإجراءات</th>\r\n                                    </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                    <tr v-for=\"activity in filteredActivities\" :key=\"activity.id\" class=\"activity-row\">\r\n                                        <td class=\"col-title\">\r\n                                            <div class=\"activity-title\">\r\n                                                <h4>{{ activity.title }}</h4>\r\n                                                <p class=\"activity-description\">{{ activity.short_description || 'لا يوجد وصف' }}</p>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-owner\">\r\n                                            <div class=\"owner-info\">\r\n                                                <span>{{ activity.owner_name }}</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-date\">\r\n                                            <div class=\"date-info\">\r\n                                                <span class=\"date-main\">{{ new Date(activity.activity_date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }) }}</span>\r\n                                                <span class=\"date-year\">{{ new Date(activity.activity_date).getFullYear() }}</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-status\">\r\n                                            <span class=\"status-badge\" :class=\"getStatusClass(activity.state)\">\r\n                                                <div class=\"status-indicator\"></div>\r\n                                                {{ activity.state }}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td class=\"col-files\">\r\n                                            <div class=\"files-container\">\r\n                                                <div v-if=\"activity.files && activity.files.length > 0\" class=\"file-buttons\">\r\n                                                    <button \r\n                                                        v-for=\"file in activity.files\" \r\n                                                        :key=\"file.id\"\r\n                                                        @click=\"downloadFile(file)\"\r\n                                                        class=\"file-download-btn\"\r\n                                                        :title=\"`تحميل ${file.file_name}`\"\r\n                                                    >\r\n                                                        <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                            <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                                                        </svg>\r\n                                                        <span class=\"file-name\">{{ getShortFileName(file.file_name) }}</span>\r\n                                                    </button>\r\n                                                </div>\r\n                                                <span v-else class=\"no-files\">لا توجد ملفات</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-governorate\">\r\n                                            <span class=\"governorate-name\">{{ activity.submission_info.governorate }}</span>\r\n                                        </td>\r\n                                        <td class=\"col-coordinator\">\r\n                                            <span class=\"coordinator-name\">{{ activity.submission_info.coordinator_name }}</span>\r\n                                        </td>\r\n                                        <td class=\"col-actions\">\r\n                                            <div class=\"action-buttons\">\r\n                                                <button @click=\"editActivity(activity)\" class=\"action-btn edit-btn\" title=\"تعديل\">\r\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                        <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\r\n                                                    </svg>\r\n                                                </button>\r\n                                                <button @click=\"deleteActivity(activity.id)\" class=\"action-btn delete-btn\" title=\"حذف\">\r\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                        <path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/>\r\n                                                    </svg>\r\n                                                </button>\r\n                                            </div>\r\n                                        </td>\r\n                                    </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Account Settings Modal -->\r\n        <div v-if=\"showAccountSettings\" class=\"modal-overlay\" @click=\"closeAccountSettings\">\r\n            <div class=\"modal-content account-settings-modal\" @click.stop>\r\n                <div class=\"modal-header\">\r\n                    <h3>إعدادات الحساب</h3>\r\n                    <button @click=\"closeAccountSettings\" class=\"close-btn\">&times;</button>\r\n                </div>\r\n                \r\n                <form @submit.prevent=\"updateAccountSettings\" class=\"account-form\">\r\n                    <div class=\"form-group\">\r\n                        <label for=\"fullName\">الاسم الكامل:</label>\r\n                        <input \r\n                            type=\"text\" \r\n                            id=\"fullName\" \r\n                            v-model=\"accountForm.fullName\" \r\n                            required \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل اسمك الكامل\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"teamPin\">رمز الفريق الرقمي:</label>\r\n                        <input \r\n                            type=\"text\" \r\n                            id=\"teamPin\" \r\n                            v-model=\"accountForm.teamPin\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"password-section\">\r\n                        <h4>تغيير كلمة المرور (اختياري)</h4>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"currentPassword\">كلمة المرور الحالية:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"currentPassword\" \r\n                                v-model=\"accountForm.currentPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أدخل كلمة المرور الحالية\"\r\n                            >\r\n                        </div>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"newPassword\">كلمة المرور الجديدة:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"newPassword\" \r\n                                v-model=\"accountForm.newPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\r\n                            >\r\n                        </div>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"confirmPassword\">تأكيد كلمة المرور الجديدة:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"confirmPassword\" \r\n                                v-model=\"accountForm.confirmPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أعد إدخال كلمة المرور الجديدة\"\r\n                            >\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"form-actions\">\r\n                        <button type=\"button\" @click=\"closeAccountSettings\" class=\"cancel-btn\">إلغاء</button>\r\n                        <button type=\"submit\" :disabled=\"updatingAccount\" class=\"save-btn\">\r\n                            <span v-if=\"updatingAccount\">جاري الحفظ...</span>\r\n                            <span v-else>حفظ التغييرات</span>\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- PIN Confirmation Modal -->\r\n        <div v-if=\"showPinConfirmation\" class=\"modal-overlay\" @click=\"closePinConfirmation\">\r\n            <div class=\"modal-content pin-modal\" @click.stop>\r\n                <div class=\"modal-header\">\r\n                    <h3>تأكيد العملية</h3>\r\n                    <button @click=\"closePinConfirmation\" class=\"close-btn\">&times;</button>\r\n                </div>\r\n                <div class=\"modal-body\">\r\n                    <p class=\"pin-message\">\r\n                        {{ pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' }}\r\n                    </p>\r\n                    <div class=\"form-group\">\r\n                        <label for=\"confirmPin\">رمز الفريق الرقمي:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"confirmPin\"\r\n                            v-model=\"pinConfirmationData.pin\" \r\n                            placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                            @keyup.enter=\"confirmPinAction\"\r\n                            class=\"form-control\"\r\n                        >\r\n                    </div>\r\n                </div>\r\n                <div class=\"modal-footer\">\r\n                    <button @click=\"closePinConfirmation\" class=\"cancel-btn\">إلغاء</button>\r\n                    <button @click=\"confirmPinAction\" class=\"confirm-btn\" :disabled=\"!pinConfirmationData.pin\">\r\n                        {{ pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل' }}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n</template>\r\n<style>\r\n/* Enhanced Navigation Styling */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: var(--space-3);\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n    padding: var(--space-3) var(--space-5);\r\n    border-radius: var(--radius-xl);\r\n    font-weight: var(--font-weight-semibold);\r\n    font-size: var(--font-size-sm);\r\n    transition: all var(--transition-base);\r\n    min-width: auto;\r\n    white-space: nowrap;\r\n}\r\n\r\n.nav-btn svg {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.nav-btn span {\r\n    display: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .nav-btn span {\r\n        display: inline;\r\n    }\r\n\r\n    .nav-btn {\r\n        padding: var(--space-3) var(--space-6);\r\n    }\r\n}\r\n\r\n/* Enhanced User Section */\r\n.user-section {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n    padding: var(--space-3) var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    cursor: pointer;\r\n    transition: all var(--transition-base);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: var(--border-secondary);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.user-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));\r\n    border-radius: var(--radius-full);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n}\r\n\r\n.user-name {\r\n    font-weight: var(--font-weight-semibold);\r\n    font-size: var(--font-size-sm);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform var(--transition-fast);\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n/* Enhanced User Menu */\r\n.user-menu {\r\n    position: absolute;\r\n    top: 100%;\r\n    right: 0;\r\n    margin-top: var(--space-2);\r\n    background: rgba(30, 30, 46, 0.95);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    box-shadow: var(--shadow-dark-2xl);\r\n    backdrop-filter: blur(20px);\r\n    min-width: 200px;\r\n    z-index: 999999;\r\n    animation: slideDown 0.2s ease-out;\r\n    pointer-events: auto;\r\n}\r\n\r\n.user-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n    padding: var(--space-4);\r\n    cursor: pointer;\r\n    transition: all var(--transition-fast);\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n    font-weight: var(--font-weight-medium);\r\n    pointer-events: auto;\r\n    user-select: none;\r\n}\r\n\r\n.user-menu-item:hover {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.user-menu-item:first-child {\r\n    border-radius: var(--radius-xl) var(--radius-xl) 0 0;\r\n}\r\n\r\n.user-menu-item:last-child {\r\n    border-radius: 0 0 var(--radius-xl) var(--radius-xl);\r\n}\r\n\r\n.user-menu-item svg {\r\n    flex-shrink: 0;\r\n    opacity: 0.7;\r\n}\r\n\r\n.user-menu-item:hover svg {\r\n    opacity: 1;\r\n}\r\n\r\n/* Enhanced Content Container */\r\n.content-container {\r\n    max-width: var(--container-xl);\r\n    margin: 0 auto;\r\n    padding: var(--space-6) var(--space-4);\r\n}\r\n\r\n/* Enhanced View Header */\r\n.view-header {\r\n    text-align: center;\r\n    margin-bottom: var(--space-8);\r\n    padding: var(--space-6);\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: var(--radius-2xl);\r\n    border: 1px solid var(--border-primary);\r\n}\r\n\r\n.view-title {\r\n    font-size: var(--font-size-3xl);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin-bottom: var(--space-3);\r\n    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.view-description {\r\n    font-size: var(--font-size-lg);\r\n    color: var(--text-secondary);\r\n    line-height: var(--line-height-relaxed);\r\n    margin: 0;\r\n}\r\n\r\n/* Enhanced Form Styling */\r\n.form-container {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    backdrop-filter: blur(20px);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-3xl);\r\n    padding: var(--space-8);\r\n    box-shadow: var(--shadow-dark-lg);\r\n    margin-bottom: var(--space-8);\r\n}\r\n\r\n.form-row {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: var(--space-6);\r\n    margin-bottom: var(--space-6);\r\n    padding: var(--space-4);\r\n    background: rgba(255, 255, 255, 0.03);\r\n    border-radius: var(--radius-xl);\r\n    border: 1px solid var(--border-primary);\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: var(--space-2);\r\n}\r\n\r\n.form-group label {\r\n    font-size: var(--font-size-sm);\r\n    font-weight: var(--font-weight-semibold);\r\n    color: var(--text-secondary);\r\n    margin-bottom: var(--space-1);\r\n}\r\n\r\n.form-group input,\r\n.form-group select,\r\n.form-group textarea {\r\n    padding: var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    color: var(--text-primary);\r\n    font-family: inherit;\r\n    font-size: var(--font-size-base);\r\n    transition: all var(--transition-base);\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.form-group input:focus,\r\n.form-group select:focus,\r\n.form-group textarea:focus {\r\n    outline: none;\r\n    border-color: var(--accent-primary);\r\n    background: rgba(255, 255, 255, 0.12);\r\n    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);\r\n}\r\n\r\n.form-group textarea {\r\n    min-height: 120px;\r\n    resize: vertical;\r\n}\r\n\r\n/* Enhanced Activity Cards */\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n    gap: var(--space-6);\r\n    margin-top: var(--space-6);\r\n    padding: 0 var(--space-2);\r\n}\r\n\r\n.activity-card {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    backdrop-filter: blur(20px);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-3xl);\r\n    padding: var(--space-6);\r\n    box-shadow: var(--shadow-dark-lg);\r\n    transition: all var(--transition-base);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));\r\n    opacity: 0;\r\n    transition: opacity var(--transition-base);\r\n}\r\n\r\n.activity-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: var(--shadow-dark-xl);\r\n    border-color: var(--border-secondary);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    opacity: 1;\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: var(--space-4);\r\n    gap: var(--space-3);\r\n}\r\n\r\n.activity-title {\r\n    font-size: var(--font-size-lg);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin: 0;\r\n    line-height: var(--line-height-tight);\r\n}\r\n\r\n.activity-status {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.activity-details {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: var(--space-3);\r\n    margin-bottom: var(--space-5);\r\n}\r\n\r\n.activity-detail {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n    font-size: var(--font-size-sm);\r\n    color: var(--text-secondary);\r\n}\r\n\r\n.activity-detail svg {\r\n    flex-shrink: 0;\r\n    opacity: 0.7;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: var(--space-2);\r\n    margin-top: auto;\r\n    padding-top: var(--space-4);\r\n    border-top: 1px solid var(--border-primary);\r\n}\r\n\r\n/* Enhanced Table Styling */\r\n.table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: var(--radius-2xl);\r\n    border: 2px solid var(--border-primary);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n    box-shadow: var(--shadow-dark-lg);\r\n    margin-top: var(--space-6);\r\n}\r\n\r\n.table-header {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: var(--space-4) var(--space-6);\r\n    border-bottom: 2px solid var(--border-primary);\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: var(--space-4);\r\n}\r\n\r\n.table-title {\r\n    font-size: var(--font-size-xl);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin: 0;\r\n}\r\n\r\n.table-filters {\r\n    display: flex;\r\n    gap: var(--space-2);\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.filter-btn {\r\n    padding: var(--space-2) var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid var(--border-primary);\r\n    border-radius: var(--radius-lg);\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n    cursor: pointer;\r\n    transition: all var(--transition-fast);\r\n}\r\n\r\n.filter-btn:hover,\r\n.filter-btn.active {\r\n    background: var(--accent-primary);\r\n    color: white;\r\n    border-color: var(--accent-primary);\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: var(--space-4) var(--space-3);\r\n    text-align: right;\r\n    font-weight: var(--font-weight-semibold);\r\n    color: var(--text-primary);\r\n    font-size: var(--font-size-sm);\r\n    border-bottom: 2px solid var(--border-primary);\r\n    white-space: nowrap;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 10;\r\n}\r\n\r\n.activities-table td {\r\n    padding: var(--space-4) var(--space-3);\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n}\r\n\r\n.activities-table tr:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.activities-table tr:last-child td {\r\n    border-bottom: none;\r\n}\r\n\r\n/* Enhanced Modal Styling */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    backdrop-filter: blur(8px);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: var(--z-modal-backdrop);\r\n    padding: var(--space-4);\r\n    animation: fadeIn 0.3s ease-out;\r\n}\r\n\r\n.modal-content {\r\n    background: var(--bg-secondary);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-3xl);\r\n    width: 100%;\r\n    max-width: 800px;\r\n    max-height: 90vh;\r\n    overflow: hidden;\r\n    box-shadow: var(--shadow-dark-2xl);\r\n    animation: slideUp 0.3s ease-out;\r\n    z-index: var(--z-modal);\r\n}\r\n\r\n.modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: var(--space-6);\r\n    border-bottom: 2px solid var(--border-primary);\r\n    background: var(--bg-tertiary);\r\n}\r\n\r\n.modal-title {\r\n    font-size: var(--font-size-xl);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin: 0;\r\n}\r\n\r\n.modal-close {\r\n    background: none;\r\n    border: none;\r\n    color: var(--text-tertiary);\r\n    font-size: var(--font-size-2xl);\r\n    cursor: pointer;\r\n    padding: var(--space-2);\r\n    border-radius: var(--radius-lg);\r\n    transition: all var(--transition-fast);\r\n    min-width: auto;\r\n    min-height: auto;\r\n}\r\n\r\n.modal-close:hover {\r\n    background: var(--border-primary);\r\n    color: var(--text-primary);\r\n    transform: none;\r\n}\r\n\r\n.modal-body {\r\n    padding: var(--space-6);\r\n    overflow-y: auto;\r\n    max-height: calc(90vh - 140px);\r\n}\r\n\r\n/* Enhanced Loading States */\r\n.loading-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--space-4);\r\n    padding: var(--space-16) var(--space-4);\r\n    color: var(--text-tertiary);\r\n}\r\n\r\n.loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 3px solid rgba(255, 255, 255, 0.1);\r\n    border-top: 3px solid var(--accent-primary);\r\n    border-radius: var(--radius-full);\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n.loading-text {\r\n    font-size: var(--font-size-lg);\r\n    font-weight: var(--font-weight-medium);\r\n    text-align: center;\r\n}\r\n\r\n/* Enhanced Empty States */\r\n.empty-state {\r\n    text-align: center;\r\n    padding: var(--space-16) var(--space-4);\r\n    color: var(--text-tertiary);\r\n}\r\n\r\n.empty-state-icon {\r\n    width: 80px;\r\n    height: 80px;\r\n    margin: 0 auto var(--space-6);\r\n    opacity: 0.5;\r\n}\r\n\r\n.empty-state-title {\r\n    font-size: var(--font-size-xl);\r\n    font-weight: var(--font-weight-semibold);\r\n    color: var(--text-secondary);\r\n    margin-bottom: var(--space-3);\r\n}\r\n\r\n.empty-state-description {\r\n    font-size: var(--font-size-base);\r\n    line-height: var(--line-height-relaxed);\r\n    margin-bottom: var(--space-6);\r\n}\r\n\r\n/* Responsive Design Improvements */\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: var(--space-2);\r\n    }\r\n\r\n    .navbar-content {\r\n        padding: var(--space-3) var(--space-4);\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: var(--space-3);\r\n    }\r\n\r\n    .navbar-brand {\r\n        flex-direction: column;\r\n        gap: var(--space-3);\r\n    }\r\n\r\n    .nav-actions {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n\r\n    .user-section {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n\r\n    .content-container {\r\n        padding: var(--space-4) var(--space-2);\r\n    }\r\n\r\n    .view-header {\r\n        padding: var(--space-4);\r\n    }\r\n\r\n    .view-title {\r\n        font-size: var(--font-size-2xl);\r\n    }\r\n\r\n    .view-description {\r\n        font-size: var(--font-size-base);\r\n    }\r\n\r\n    .activities-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: var(--space-4);\r\n        padding: 0;\r\n    }\r\n\r\n    .activity-card {\r\n        padding: var(--space-4);\r\n    }\r\n\r\n    .form-container {\r\n        padding: var(--space-4);\r\n    }\r\n\r\n    .form-row {\r\n        grid-template-columns: 1fr;\r\n        gap: var(--space-4);\r\n        padding: var(--space-3);\r\n    }\r\n\r\n    .table-header {\r\n        padding: var(--space-3) var(--space-4);\r\n        flex-direction: column;\r\n        align-items: stretch;\r\n        gap: var(--space-3);\r\n    }\r\n\r\n    .table-filters {\r\n        justify-content: center;\r\n    }\r\n\r\n    .activities-table th,\r\n    .activities-table td {\r\n        padding: var(--space-2);\r\n        font-size: var(--font-size-xs);\r\n    }\r\n\r\n    .modal-content {\r\n        margin: var(--space-2);\r\n        max-width: calc(100vw - var(--space-4));\r\n    }\r\n\r\n    .modal-header,\r\n    .modal-body {\r\n        padding: var(--space-4);\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        gap: var(--space-4);\r\n    }\r\n\r\n    .navbar-brand {\r\n        flex-direction: column;\r\n        gap: var(--space-4);\r\n    }\r\n\r\n    .activities-grid {\r\n        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n        gap: var(--space-5);\r\n    }\r\n\r\n    .form-row {\r\n        grid-template-columns: 1fr;\r\n        gap: var(--space-5);\r\n    }\r\n\r\n    .table-header {\r\n        flex-direction: column;\r\n        align-items: stretch;\r\n    }\r\n\r\n    .activities-table {\r\n        font-size: var(--font-size-sm);\r\n    }\r\n\r\n    .activities-table th,\r\n    .activities-table td {\r\n        padding: var(--space-3) var(--space-2);\r\n    }\r\n}\r\n\r\n/* Enhanced Focus States for Accessibility */\r\n.nav-btn:focus-visible,\r\n.user-button:focus-visible,\r\n.filter-btn:focus-visible,\r\n.modal-close:focus-visible,\r\n.form-group input:focus-visible,\r\n.form-group select:focus-visible,\r\n.form-group textarea:focus-visible {\r\n    outline: 2px solid var(--accent-primary);\r\n    outline-offset: 2px;\r\n}\r\n\r\n/* Enhanced Animation Performance */\r\n.activity-card,\r\n.user-button,\r\n.nav-btn,\r\n.filter-btn,\r\n.modal-content {\r\n    will-change: transform;\r\n}\r\n\r\n/* Performance Optimizations */\r\n@media (prefers-reduced-motion: reduce) {\r\n    .activity-card,\r\n    .user-button,\r\n    .nav-btn,\r\n    .filter-btn,\r\n    .modal-content,\r\n    .loading-spinner,\r\n    .dropdown-arrow {\r\n        animation: none !important;\r\n        transition: none !important;\r\n    }\r\n\r\n    .activity-card:hover,\r\n    .user-button:hover,\r\n    .nav-btn:hover {\r\n        transform: none !important;\r\n    }\r\n}\r\n\r\n/* Print Styles */\r\n@media print {\r\n    .navbar,\r\n    .nav-actions,\r\n    .user-section,\r\n    .activity-actions,\r\n    .table-filters,\r\n    .modal-overlay {\r\n        display: none;\r\n    }\r\n\r\n    .activity-card,\r\n    .table-container,\r\n    .form-container {\r\n        box-shadow: none;\r\n        border: 1px solid #000;\r\n        background: white;\r\n        color: black;\r\n    }\r\n\r\n    .activities-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: var(--space-4);\r\n    }\r\n}\r\n\r\n/* High Contrast Mode */\r\n@media (prefers-contrast: high) {\r\n    .activity-card,\r\n    .table-container,\r\n    .form-container,\r\n    .modal-content {\r\n        border: 3px solid;\r\n        background: var(--bg-primary);\r\n    }\r\n\r\n    .nav-btn,\r\n    .filter-btn,\r\n    .user-button {\r\n        border: 2px solid;\r\n    }\r\n\r\n    .form-group input,\r\n    .form-group select,\r\n    .form-group textarea {\r\n        border: 2px solid;\r\n    }\r\n}\r\n\r\n/* Dark Mode Enhancements */\r\n@media (prefers-color-scheme: dark) {\r\n    .activity-card,\r\n    .table-container,\r\n    .form-container {\r\n        background: rgba(255, 255, 255, 0.06);\r\n        border-color: var(--border-primary);\r\n    }\r\n\r\n    .form-group input,\r\n    .form-group select,\r\n    .form-group textarea {\r\n        background: rgba(255, 255, 255, 0.06);\r\n    }\r\n}\r\n\r\n/* Enhanced Scrollbar Styling */\r\n.table-wrapper::-webkit-scrollbar,\r\n.modal-body::-webkit-scrollbar {\r\n    height: 8px;\r\n    width: 8px;\r\n}\r\n\r\n.table-wrapper::-webkit-scrollbar-track,\r\n.modal-body::-webkit-scrollbar-track {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: var(--radius-lg);\r\n}\r\n\r\n.table-wrapper::-webkit-scrollbar-thumb,\r\n.modal-body::-webkit-scrollbar-thumb {\r\n    background: var(--accent-primary);\r\n    border-radius: var(--radius-lg);\r\n}\r\n\r\n.table-wrapper::-webkit-scrollbar-thumb:hover,\r\n.modal-body::-webkit-scrollbar-thumb:hover {\r\n    background: var(--color-primary-400);\r\n}\r\n\r\n.user-section {\r\n    position: relative;\r\n    z-index: 100;\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    border-radius: 8px;\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.user-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    background: linear-gradient(135deg, #667eea, #764ba2);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n    color: #f5f5f5;\r\n    white-space: nowrap;\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform 0.3s ease;\r\n    color: #a0aec0;\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n\r\n\r\n/* Navigation Buttons */\r\n.nav-buttons {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: center;\r\n}\r\n\r\n/* Arabic RTL alignment for navbar actions */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    min-width: 160px; /* unified width with global buttons */\r\n    justify-content: center;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    font-size: 14px;\r\n    letter-spacing: 0.2px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n}\r\n\r\n/* Compact button utility for small actions */\r\n.btn-compact {\r\n    min-width: 96px;\r\n    height: 40px;\r\n    padding: 8px 14px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.nav-btn:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: rgba(255, 255, 255, 0.3);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.nav-btn.active {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    border-color: #4f46e5;\r\n    color: #ffffff;\r\n    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.nav-btn.active:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n    margin: 24px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.submit-view {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n}\r\n\r\n.submit-header {\r\n    text-align: center;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n}\r\n\r\n.submit-header h2 {\r\n    color: #ffffff;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n}\r\n\r\n/* View Activities */\r\n.view-activities {\r\n    margin-top: 32px;\r\n    padding: 0 8px;\r\n}\r\n\r\n.view-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 32px 24px;\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));\r\n    border: 2px solid rgba(79, 70, 229, 0.3);\r\n    border-radius: 16px;\r\n    margin-bottom: 32px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.view-header::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);\r\n}\r\n\r\n.view-header-content {\r\n    text-align: center;\r\n    flex: 1;\r\n}\r\n\r\n.view-title {\r\n    color: #ffffff;\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    margin: 0 0 12px 0;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.view-description {\r\n    color: #e2e8f0;\r\n    font-size: 16px;\r\n    margin: 0;\r\n    opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.export-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #f59e0b, #d97706);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\r\n    min-width: 120px;\r\n    height: auto;\r\n}\r\n\r\n.export-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d97706, #b45309);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);\r\n}\r\n\r\n.export-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n    background: linear-gradient(135deg, #6b7280, #4b5563);\r\n}\r\n\r\n.refresh-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #10b981, #059669);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\r\n    min-width: 100px;\r\n    height: auto;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.refresh-btn svg.spinning {\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    from { transform: rotate(0deg); }\r\n    to { transform: rotate(360deg); }\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 24px;\r\n}\r\n\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n    padding: 0 4px;\r\n}\r\n\r\n.activity-card {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed);\r\n    transform: scaleX(0);\r\n    transition: transform 0.4s ease;\r\n}\r\n\r\n.activity-card:hover {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);\r\n    border-color: rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 2px solid rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.activity-header h4 {\r\n    color: #ffffff;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n    line-height: 1.3;\r\n    flex: 1;\r\n    margin-right: 16px;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-btn, .delete-btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 40px;\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: none;\r\n    text-align: center;\r\n}\r\n\r\n.edit-btn {\r\n    background: linear-gradient(135deg, #3182ce, #2c5aa0);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);\r\n}\r\n\r\n.edit-btn:hover {\r\n    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);\r\n}\r\n\r\n.delete-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);\r\n}\r\n\r\n.delete-btn:hover {\r\n    background: linear-gradient(135deg, #c53030, #a02626);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.activity-details {\r\n    margin-top: 16px;\r\n}\r\n\r\n.activity-details p {\r\n    color: #e2e8f0;\r\n    font-size: 15px;\r\n    margin: 12px 0;\r\n    line-height: 1.6;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.activity-details strong {\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    min-width: 100px;\r\n    display: inline-block;\r\n}\r\n\r\n.status-badge {\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    text-align: center;\r\n    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);\r\n    border: 1px solid rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.no-activities-message, .loading-message {\r\n    text-align: center;\r\n    padding: 48px 24px;\r\n    color: #cbd5e0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    border-radius: 16px;\r\n    margin: 24px 0;\r\n}\r\n\r\n.loading-message {\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n    border-color: rgba(79, 70, 229, 0.2);\r\n    animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes slideDown {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n\r\n\r\nspan {\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n}\r\n\r\n.view {\r\n    padding: 16px;\r\n    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);\r\n    border: 1px solid #3a3a4e;\r\n    border-radius: 20px;\r\n    max-width: 90%;\r\n    margin: 16px auto;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\nbutton {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    padding: 12px 24px;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 15px;\r\n    letter-spacing: 0.2px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n/* Make icon+label buttons look consistent */\r\nbutton svg { flex-shrink: 0; }\r\nbutton span { display: inline-block; }\r\n\r\n/* Inputs: improve placeholder visibility */\r\ninput::placeholder,\r\nselect::placeholder,\r\ntextarea::placeholder {\r\n    color: #cbd5e1; /* brighter placeholder */\r\n    opacity: 1;\r\n}\r\n\r\n/* Edge/Firefox vendor prefixes */\r\ninput::-ms-input-placeholder { color: #cbd5e1; }\r\ninput::-webkit-input-placeholder { color: #cbd5e1; }\r\ntextarea::-webkit-input-placeholder { color: #cbd5e1; }\r\nselect::-ms-input-placeholder { color: #cbd5e1; }\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton span {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.info {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n}\r\n\r\n.base-info-container,\r\n.activities-info-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    direction: rtl;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n    padding: 24px;\r\n}\r\n\r\n.base-info-label {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #f1f5f9;\r\n    margin-bottom: 16px;\r\n    text-align: right;\r\n    border-bottom: 2px solid #4f46e5;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.field-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 8px;\r\n    text-align: right;\r\n    display: block;\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect {\r\n    direction: rtl;\r\n    width: 100%;\r\n    max-width: 400px;\r\n    margin: 8px 0;\r\n    padding: 12px 16px;\r\n    background: rgba(255, 255, 255, 0.12);\r\n    color: #ffffff;\r\n    border: 2px solid rgba(255, 255, 255, 0.25);\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\ninput[type=\"text\"]:focus,\r\ninput[type=\"date\"]:focus,\r\nselect:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);\r\n    background: rgba(255, 255, 255, 0.18);\r\n    color: #ffffff;\r\n}\r\n\r\nselect {\r\n    cursor: pointer;\r\n}\r\n\r\nselect option {\r\n    background: #1a1a2e;\r\n    color: #f0f0f0;\r\n    padding: 8px;\r\n}\r\n.activity-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 8px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-item:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.activity-file-input {\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 12px;\r\n    width: fit-content;\r\n    max-width: 200px;\r\n    text-align: center;\r\n    padding: 12px 16px;\r\n    font-size: 14px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    color: #f0f0f0;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n    align-self: flex-start;\r\n}\r\n\r\n.activity-file-input:hover {\r\n    background: rgba(255, 255, 255, 0.15);\r\n    border-color: #4f46e5;\r\n}\r\n\r\n.activity-delete-button {\r\n    background: linear-gradient(135deg, #ef4444, #dc2626);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    margin: 8px 0;\r\n    padding: 10px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-weight: 600;\r\n    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.activity-delete-button:hover {\r\n    background: linear-gradient(135deg, #f87171, #ef4444);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .user-button {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n    \r\n    .info {\r\n        grid-template-columns: 1fr;\r\n        gap: 16px;\r\n    }\r\n    \r\n    .base-info-container,\r\n    .activities-info-container {\r\n        padding: 16px;\r\n    }\r\n    \r\n    .view {\r\n        margin: 8px;\r\n        padding: 12px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .user-name {\r\n        font-size: 12px;\r\n    }\r\n    \r\n    input[type=\"text\"],\r\n    input[type=\"date\"],\r\n    select {\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n}\r\n\r\n/* RTL Improvements */\r\n.view {\r\n    direction: rtl;\r\n}\r\n\r\n.navbar-content {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.navbar-text {\r\n    flex-direction: row-reverse;\r\n    text-align: right;\r\n}\r\n\r\n.nav-actions .nav-btn {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.view-header-content,\r\n.view-title,\r\n.view-description {\r\n    text-align: right;\r\n}\r\n\r\n.activities-list {\r\n    direction: rtl;\r\n}\r\n\r\nlabel {\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 4px;\r\n    display: block;\r\n}\r\n\r\n/* Inputs RTL */\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect,\r\ntextarea {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n/* Fine color and spacing tweaks for Arabic */\r\n.navbar {\r\n    border-color: #4b5563;\r\n}\r\n.nav-btn {\r\n    letter-spacing: 0.2px; /* tighter Arabic rhythm */\r\n}\r\n.view-header {\r\n    border-color: rgba(79, 70, 229, 0.35);\r\n}\r\n.activity-card {\r\n    padding-inline: 24px;\r\n}\r\n.activity-header h4 {\r\n    margin-left: 0;\r\n    margin-right: 15px; /* move spacing to the right for RTL */\r\n}\r\n.activity-details,\r\n.activity-details p {\r\n    text-align: right;\r\n}\r\n\r\n.splitter {\r\n    height: 2px;\r\n    background: linear-gradient(90deg, transparent, #4f46e5, transparent);\r\n    margin: 24px 0;\r\n    border-radius: 1px;\r\n}\r\n\r\n/* My Activities Section Styles */\r\n.my-activities-section {\r\n    margin: 30px 0;\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.toggle-activities-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.toggle-activities-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.toggle-icon {\r\n    font-size: 14px;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 15px;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.loading-message, .no-activities-message {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n}\r\n\r\n/* Activities Container */\r\n.activities-container {\r\n    margin-top: 20px;\r\n    padding: 0;\r\n}\r\n\r\n/* Loading and Empty States */\r\n.loading-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    gap: 16px;\r\n}\r\n\r\n.loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 3px solid rgba(255, 255, 255, 0.1);\r\n    border-top: 3px solid #4f46e5;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n}\r\n\r\n.no-activities-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n    color: #8892b0;\r\n    gap: 16px;\r\n}\r\n\r\n.empty-icon {\r\n    opacity: 0.6;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.no-activities-message h3 {\r\n    margin: 0;\r\n    color: #e6f1ff;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.no-activities-message p {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    opacity: 0.8;\r\n}\r\n\r\n/* Edit Modal - Clean Flexbox Design */\r\n.edit-modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.75);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.edit-modal {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 600px;\r\n    max-height: calc(100vh - 40px);\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.edit-modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 24px;\r\n    border-bottom: 1px solid #334155;\r\n    background: #0f172a;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-modal-header h3 {\r\n    margin: 0;\r\n    color: #f1f5f9;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: #64748b;\r\n    cursor: pointer;\r\n    padding: 8px;\r\n    border-radius: 6px;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.edit-form {\r\n    padding: 24px;\r\n    direction: rtl;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    overflow-y: auto;\r\n    flex: 1;\r\n    min-height: 0;\r\n}\r\n\r\n.form-row {\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-row.single {\r\n    flex-direction: column;\r\n}\r\n\r\n.form-row.double {\r\n    flex-direction: row;\r\n}\r\n\r\n.form-row.double .form-group {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* Table Styles */\r\n.activities-table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 16px;\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n    padding: 24px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.table-stats {\r\n    display: flex;\r\n    gap: 32px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.stat-item.clickable {\r\n    cursor: pointer;\r\n    padding: 12px 16px;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.stat-item.clickable:hover {\r\n    background: rgba(79, 70, 229, 0.1);\r\n    border-color: rgba(79, 70, 229, 0.3);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.stat-item.selected {\r\n    background: rgba(79, 70, 229, 0.2);\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.stat-item.selected .stat-number {\r\n    color: #6366f1;\r\n}\r\n\r\n.stat-item.selected .stat-label {\r\n    color: #c7d2fe;\r\n}\r\n\r\n.stat-number {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #4f46e5;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n    font-size: 14px;\r\n    color: #8892b0;\r\n    font-weight: 500;\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: 16px 12px;\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #e6f1ff;\r\n    font-size: 14px;\r\n    border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n    white-space: nowrap;\r\n}\r\n\r\n.activities-table td {\r\n    padding: 16px 12px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n}\r\n\r\n.activity-row {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-row:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n/* Column Specific Styles */\r\n.col-title {\r\n    min-width: 250px;\r\n}\r\n\r\n.activity-title h4 {\r\n    margin: 0 0 4px 0;\r\n    color: #e6f1ff;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 1.3;\r\n}\r\n\r\n.activity-description {\r\n    margin: 0;\r\n    color: #8892b0;\r\n    font-size: 13px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.owner-info {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.date-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.date-main {\r\n    color: #e6f1ff;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n}\r\n\r\n.date-year {\r\n    color: #8892b0;\r\n    font-size: 12px;\r\n}\r\n\r\n.status-badge {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    white-space: nowrap;\r\n}\r\n\r\n.status-indicator {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    background: currentColor;\r\n}\r\n\r\n/* Status Colors */\r\n.status-executed-paid {\r\n    background: rgba(34, 197, 94, 0.2);\r\n    color: #22c55e;\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n}\r\n\r\n.status-executed-unpaid {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.status-accepted {\r\n    background: rgba(16, 185, 129, 0.2);\r\n    color: #10b981;\r\n    border: 1px solid rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-rejected {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.status-needs-edit {\r\n    background: rgba(245, 158, 11, 0.2);\r\n    color: #f59e0b;\r\n    border: 1px solid rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.status-paid-not-executed {\r\n    background: rgba(168, 85, 247, 0.2);\r\n    color: #a855f7;\r\n    border: 1px solid rgba(168, 85, 247, 0.3);\r\n}\r\n\r\n.status-accepted-unpaid {\r\n    background: rgba(6, 182, 212, 0.2);\r\n    color: #06b6d4;\r\n    border: 1px solid rgba(6, 182, 212, 0.3);\r\n}\r\n\r\n.status-sent {\r\n    background: rgba(139, 92, 246, 0.2);\r\n    color: #8b5cf6;\r\n    border: 1px solid rgba(139, 92, 246, 0.3);\r\n}\r\n\r\n.governorate-name, .coordinator-name {\r\n    color: #ccd6f6;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 36px;\r\n    height: 36px;\r\n    border: none;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.edit-btn {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.action-btn.edit-btn:hover {\r\n    background: rgba(59, 130, 246, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n.action-btn.delete-btn {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.action-btn.delete-btn:hover {\r\n    background: rgba(239, 68, 68, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n/* File Download Styles */\r\n.files-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n    min-width: 120px;\r\n}\r\n\r\n.file-buttons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.file-download-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 10px;\r\n    background: rgba(34, 197, 94, 0.15);\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n    border-radius: 6px;\r\n    color: #22c55e;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n    min-height: 28px;\r\n    max-width: 140px;\r\n}\r\n\r\n.file-download-btn:hover {\r\n    background: rgba(34, 197, 94, 0.25);\r\n    border-color: rgba(34, 197, 94, 0.5);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);\r\n}\r\n\r\n.file-download-btn svg {\r\n    flex-shrink: 0;\r\n    color: #22c55e;\r\n}\r\n\r\n.file-name {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.no-files {\r\n    color: #64748b;\r\n    font-size: 12px;\r\n    font-style: italic;\r\n    text-align: center;\r\n    padding: 8px;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n}\r\n\r\n@keyframes slideUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Edit Form Styles */\r\n.edit-form {\r\n    direction: rtl;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-group label {\r\n    color: #cbd5e1;\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    margin-bottom: 0;\r\n    text-align: right;\r\n}\r\n\r\n.form-input, .form-textarea, .form-select {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    border: 1px solid #475569;\r\n    border-radius: 6px;\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n    font-size: 14px;\r\n    font-family: inherit;\r\n    transition: all 0.2s ease;\r\n    direction: rtl;\r\n    text-align: right;\r\n    box-sizing: border-box;\r\n    outline: none;\r\n}\r\n\r\n.form-input:focus, .form-textarea:focus, .form-select:focus {\r\n    border-color: #3b82f6;\r\n    background: #1e293b;\r\n    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.form-input::placeholder, .form-textarea::placeholder {\r\n    color: #94a3b8;\r\n}\r\n\r\n.form-select {\r\n    cursor: pointer;\r\n    appearance: none;\r\n    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23f1f5f9\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>');\r\n    background-repeat: no-repeat;\r\n    background-position: left 12px center;\r\n    background-size: 12px;\r\n    padding-left: 32px;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n.form-textarea {\r\n    min-height: 100px;\r\n    resize: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 24px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #475569;\r\n}\r\n\r\n.save-btn, .cancel-btn {\r\n    padding: 10px 20px;\r\n    border-radius: 6px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    border: 1px solid transparent;\r\n    outline: none;\r\n}\r\n\r\n.save-btn {\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.save-btn:hover {\r\n    background: #2563eb;\r\n    border-color: #2563eb;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #94a3b8;\r\n    border-color: #475569;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n}\r\n\r\n    /* Responsive Design for My Activities */\r\n    @media (max-width: 768px) {\r\n        .view-activities {\r\n            padding: 0 4px;\r\n        }\r\n        \r\n        .view-header {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            padding: 24px 16px;\r\n            margin-bottom: 24px;\r\n        }\r\n        \r\n        .view-header-content {\r\n            order: 1;\r\n        }\r\n        \r\n        .refresh-btn {\r\n            order: 2;\r\n            align-self: center;\r\n            min-width: 120px;\r\n            padding: 10px 16px;\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .view-title {\r\n            font-size: 24px;\r\n        }\r\n        \r\n        .view-description {\r\n            font-size: 14px;\r\n        }\r\n        \r\n        .activities-grid {\r\n            grid-template-columns: 1fr;\r\n            gap: 16px;\r\n            padding: 0;\r\n        }\r\n        \r\n        .activity-card {\r\n            padding: 20px;\r\n            margin: 0;\r\n        }\r\n        \r\n        .activity-header {\r\n            flex-direction: column;\r\n            gap: 12px;\r\n            align-items: stretch;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 18px;\r\n            margin-right: 0;\r\n        }\r\n        \r\n        .activity-actions {\r\n            justify-content: flex-end;\r\n            gap: 8px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 8px 12px;\r\n            font-size: 13px;\r\n            min-width: 96px;\r\n            height: 40px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 14px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 4px;\r\n        }\r\n        \r\n        .activity-details strong {\r\n            min-width: auto;\r\n        }\r\n        \r\n        .no-activities-message, .loading-message {\r\n            padding: 32px 16px;\r\n            font-size: 16px;\r\n        }\r\n    }\r\n    \r\n    @media (max-width: 480px) {\r\n        .activity-card {\r\n            padding: 12px;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 16px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 5px 10px;\r\n            font-size: 11px;\r\n        }\r\n        \r\n        .edit-modal {\r\n            width: 95%;\r\n            max-width: none;\r\n            margin: 20px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .edit-modal-header {\r\n            padding: 20px 24px;\r\n        }\r\n        \r\n        .edit-form {\r\n            padding: 24px;\r\n            gap: 20px;\r\n        }\r\n        \r\n        .form-row {\r\n            grid-template-columns: 1fr;\r\n            gap: 20px;\r\n            padding: 12px;\r\n            margin-bottom: 16px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .form-row:hover {\r\n            transform: none;\r\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        }\r\n        \r\n        .form-input, .form-textarea, .form-select {\r\n            padding: 16px 20px;\r\n            font-size: 16px;\r\n            border-radius: 14px;\r\n        }\r\n        \r\n        .form-textarea {\r\n            min-height: 100px;\r\n            padding-top: 18px;\r\n            padding-bottom: 18px;\r\n        }\r\n        \r\n        .form-group label {\r\n            font-size: 13px;\r\n            margin-right: 2px;\r\n        }\r\n        \r\n        .form-select {\r\n            padding-left: 35px;\r\n            background-position: calc(100% - 18px) calc(1em + 2px), \r\n                                 calc(100% - 13px) calc(1em + 2px);\r\n        }\r\n        \r\n        .form-actions {\r\n            flex-direction: column-reverse;\r\n            align-items: stretch;\r\n            gap: 12px;\r\n            padding: 20px 0 0 0;\r\n        }\r\n        \r\n        .save-btn, .cancel-btn {\r\n            width: 100%;\r\n            padding: 14px;\r\n            min-width: auto;\r\n        }\r\n    }\r\n\r\n/* Modal Overlay Styles */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    backdrop-filter: blur(5px);\r\n}\r\n\r\n.modal-content {\r\n    background: #1a1a2e;\r\n    border-radius: 20px;\r\n    width: 90%;\r\n    max-width: 500px;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n    animation: modalSlideIn 0.3s ease;\r\n    border: 2px solid #4facfe;\r\n    position: relative;\r\n    z-index: 1001;\r\n    padding: 10px;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-50px) scale(0.9);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0) scale(1);\r\n    }\r\n}\r\n\r\n/* Modal Header Styles */\r\n.modal-header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    border-radius: 20px 20px 0 0;\r\n}\r\n\r\n.modal-header h3 {\r\n    margin: 0;\r\n    font-size: 1.3rem;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: white;\r\n    font-size: 1.5rem;\r\n    cursor: pointer;\r\n    padding: 0;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: background 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Account Settings Modal Styles */\r\n.account-settings-modal {\r\n    max-width: 600px;\r\n    width: 90%;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n}\r\n\r\n.account-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 8px;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n    transition: all 0.3s ease;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #4facfe;\r\n    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);\r\n    background: #374151;\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #a0aec0;\r\n}\r\n\r\n.password-section {\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.password-section h4 {\r\n    margin: 0 0 20px 0;\r\n    color: #4facfe;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.save-btn {\r\n    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.save-btn:hover:not(:disabled) {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);\r\n}\r\n\r\n.save-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n    color: #f5f5f5;\r\n}\r\n\r\n/* Responsive styles for account modal */\r\n@media (max-width: 768px) {\r\n    .account-settings-modal {\r\n        width: 95%;\r\n        margin: 20px;\r\n    }\r\n    \r\n    .form-actions {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .save-btn,\r\n    .cancel-btn {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n/* PIN Confirmation Modal Styles */\r\n.pin-modal {\r\n    max-width: 450px;\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #4a5568;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.pin-modal .modal-header {\r\n    background: linear-gradient(135deg, #2d3748, #4a5568);\r\n    border-bottom: 2px solid #4a5568;\r\n    border-radius: 14px 14px 0 0;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.pin-modal .modal-header h3 {\r\n    color: #4fc3f7;\r\n    margin: 0;\r\n    font-size: 1.4rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.pin-modal .modal-body {\r\n    padding: 25px;\r\n}\r\n\r\n.pin-message {\r\n    color: #e2e8f0;\r\n    font-size: 1.1rem;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n    line-height: 1.6;\r\n}\r\n\r\n.pin-modal .form-group {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.pin-modal .form-group label {\r\n    color: #4fc3f7;\r\n    font-weight: 600;\r\n    margin-bottom: 8px;\r\n    display: block;\r\n}\r\n\r\n.pin-modal .form-control {\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    color: #e2e8f0;\r\n    padding: 12px 16px;\r\n    border-radius: 8px;\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.pin-modal .form-control:focus {\r\n    border-color: #4fc3f7;\r\n    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);\r\n    outline: none;\r\n}\r\n\r\n.pin-modal .modal-footer {\r\n    padding: 20px 25px;\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.confirm-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #c53030, #9c2626);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.confirm-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.pin-modal .cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 10px 20px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.pin-modal .cancel-btn:hover {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    transform: translateY(-2px);\r\n}\r\n</style>"], "mappings": ";;;OAk1B6CA,UAA6B;;EAJ7DC,KAAK,EAAC;AAAQ;;EACVA,KAAK,EAAC;AAAgB;;EAMlBA,KAAK,EAAC;AAAa;;EAcnBA,KAAK,EAAC;AAAc;;EAOZA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAW;;;EAMNA,KAAK,EAAC;;;EAuBtCA,KAAK,EAAC;AAAc;;;EAEgBA,KAAK,EAAC;;;EAKlCA,KAAK,EAAC;AAAqB;;EAQ3BA,KAAK,EAAC;AAA2B;;;EAePA,KAAK,EAAC;;;EAChCA,KAAK,EAAC;AAAa;;EAKfA,KAAK,EAAC;AAAgB;;;;EAgB1BA,KAAK,EAAC;AAAsB;;;EACCA,KAAK,EAAC;;;;EAKOA,KAAK,EAAC;;;EAWpCA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAW;;EACbA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAKtBA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EActBA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAuB;;EAKjCA,KAAK,EAAC;AAAc;;EAmBIA,KAAK,EAAC;AAA4B;;EAClEA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAa;;EAEVA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAMhCA,KAAK,EAAC;AAAe;;EACfA,KAAK,EAAC;AAAkB;;EAefA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAsB;;EAGnCA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAY;;EAIvBA,KAAK,EAAC;AAAU;;EACXA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAG3BA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAiB;;;EACgCA,KAAK,EAAC;;;;EAWhDA,KAAK,EAAC;AAAW;;;EAGlBA,KAAK,EAAC;;;EAGvBA,KAAK,EAAC;AAAiB;;EACjBA,KAAK,EAAC;AAAkB;;EAE9BA,KAAK,EAAC;AAAiB;;EACjBA,KAAK,EAAC;AAAkB;;EAE9BA,KAAK,EAAC;AAAa;;EACdA,KAAK,EAAC;AAAgB;;;;EAyBlDA,KAAK,EAAC;AAAc;;EAMhBA,KAAK,EAAC;AAAY;;EAYlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAYtBA,KAAK,EAAC;AAAc;;;;;;;;;EAcxBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EAGjBA,KAAK,EAAC;AAAY;;EAYtBA,KAAK,EAAC;AAAc;;;6DAtajCC,mBAAA,CAyDM,OAzDNC,UAyDM,GAxDFD,mBAAA,CAuDM,OAvDNE,UAuDM,G,4BAtDFF,mBAAA,CAIM;IAJDD,KAAK,EAAC;EAAc,IACrBC,mBAAA,CAAqD;IAA/CD,KAAK,EAAC;EAAa,GAAC,sBAAoB,GAC9CC,mBAAA,CAAyE;IAApED,KAAK,EAAC,aAAa;IAACI,GAA6B,EAA7BL,UAA6B;IAACM,GAAG,EAAC;MAC3DJ,mBAAA,CAA4D;IAAtDD,KAAK,EAAC;EAAa,GAAC,6BAA2B,E,qBAEzDC,mBAAA,CAaM,OAbNK,UAaM,GAZFL,mBAAA,CAKS;IALDD,KAAK,EAAAO,eAAA,EAAC,aAAa;MAAA,eAA0BC,KAAA,CAAAC,WAAW;MAAA,iBAAgCD,KAAA,CAAAC,WAAW;IAAA;IAAkBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;uCAC9Ib,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAA+C;IAAzCkB,CAAC,EAAC;EAAqC,G,oBAEjDlB,mBAAA,CAA2B,cAArB,gBAAc,mB,qBAExBA,mBAAA,CAKS;IALDD,KAAK,EAAAO,eAAA,EAAC,aAAa;MAAA,eAA0BC,KAAA,CAAAC,WAAW;MAAA,iBAA8BD,KAAA,CAAAC,WAAW;IAAA;IAAgBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;uCAC1Ib,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAwG;IAAlGkB,CAAC,EAAC;EAA8F,G,oBAE1GlB,mBAAA,CAA6B,cAAvB,kBAAgB,mB,uBAG9BA,mBAAA,CAkCM,OAlCNmB,UAkCM,GAjCFnB,mBAAA,CAYM;IAZDD,KAAK,EAAC,aAAa;IAAEU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAS,cAAA,IAAAT,QAAA,CAAAS,cAAA,IAAAD,IAAA,CAAc;kCAC3CpB,mBAAA,CAIM;IAJDD,KAAK,EAAC;EAAa,IACpBC,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAyH;IAAnHkB,CAAC,EAAC;EAA+G,G,uBAG/HlB,mBAAA,CAKM,OALNsB,UAKM,GAJFtB,mBAAA,CAAoF,QAApFuB,UAAoF,EAAAC,gBAAA,CAAzDjB,KAAA,CAAAkB,IAAI,EAAEC,SAAS,IAAInB,KAAA,CAAAkB,IAAI,EAAEE,QAAQ,gC,4BAC5D3B,mBAAA,CAEM;IAFDD,KAAK,EAAC,gBAAgB;IAACe,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACxEjB,mBAAA,CAA0B;IAApBkB,CAAC,EAAC;EAAgB,G,yBAIzBX,KAAA,CAAAqB,YAAY,I,cAAvBC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBF9B,mBAAA,CAKM;IALDD,KAAK,EAAC,gBAAgB;IAAEU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAmB,mBAAA,IAAAnB,QAAA,CAAAmB,mBAAA,IAAAX,IAAA,CAAmB;uCACnDpB,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAyrB;IAAnrBkB,CAAC,EAAC;EAA+qB,G,oBAE3rBlB,mBAAA,CAA2B,cAArB,gBAAc,mB,MAEbO,KAAA,CAAAkB,IAAI,IAAIlB,KAAA,CAAAkB,IAAI,CAACO,IAAI,gB,cAA5BH,mBAAA,CAKM;;IALoC9B,KAAK,EAAC,gBAAgB;IAAEU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAqB,SAAA,IAAArB,QAAA,CAAAqB,SAAA,IAAAb,IAAA,CAAS;uCAC9EpB,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAgM;IAA1LkB,CAAC,EAAC;EAAsL,G,oBAElMlB,mBAAA,CAAyB,cAAnB,cAAY,mB,2CAEtBA,mBAAA,CAKM;IALDD,KAAK,EAAC,gBAAgB;IAAEU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAsB,MAAA,IAAAtB,QAAA,CAAAsB,MAAA,IAAAd,IAAA,CAAM;uCACtCpB,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAA0H;IAApHkB,CAAC,EAAC;EAAgH,G,oBAE5HlB,mBAAA,CAAyB,cAAnB,cAAY,mB,mDAMtCA,mBAAA,CAmQM,OAnQNmC,UAmQM,GAlQFC,mBAAA,4BAA+B,EACpB7B,KAAA,CAAAC,WAAW,iB,cAAtBqB,mBAAA,CAyBM,OAzBNQ,UAyBM,G,4BAxBFrC,mBAAA,CAGM;IAHDD,KAAK,EAAC;EAAa,IACpBC,mBAAA,CAAkD;IAA9CD,KAAK,EAAC;EAAY,GAAC,wBAAsB,GAC7CC,mBAAA,CAAuF;IAApFD,KAAK,EAAC;EAAkB,GAAC,yDAAuD,E,qBAEvFC,mBAAA,CAIM,OAJNsC,WAIM,G,4BAHFtC,mBAAA,CAAuD;IAAjDD,KAAK,EAAC;EAAiB,GAAC,oBAAkB,qB,4BAChDC,mBAAA,CAAuE;IAAhEuC,GAAG,EAAC,kBAAkB;IAACxC,KAAK,EAAC;KAAc,eAAa,qB,gBAC/DC,mBAAA,CAAoI;IAA7HwC,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,kBAAkB;IAACC,WAAW,EAAC,mBAAmB;IAAC3C,KAAK,EAAC,iBAAiB;+DAAUQ,KAAA,CAAAoC,eAAe,GAAAhC,MAAA;IAAEiC,QAAQ,EAAR;iDAAjBrC,KAAA,CAAAoC,eAAe,E,iCAG7H3C,mBAAA,CAA4B;IAAvBD,KAAK,EAAC;EAAU,4BAErBC,mBAAA,CAOM,OAPN6C,WAOM,G,4BANF7C,mBAAA,CAAuD;IAAjDD,KAAK,EAAC;EAAiB,GAAC,oBAAkB,qB,4BAChDC,mBAAA,CACM;IADDD,KAAK,EAAC;EAAiB,4BAE5BC,mBAAA,CAES;IAFD8C,KAAuC,EAAvC;MAAA;MAAA;IAAA,CAAuC;IAAErC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAmC,eAAA,IAAAnC,QAAA,CAAAmC,eAAA,IAAA3B,IAAA,CAAe;uCACnEpB,mBAAA,CAA4B,cAAtB,iBAAe,mB,oCAG7BA,mBAAA,CAA4B;IAAvBD,KAAK,EAAC;EAAU,4BACrBC,mBAAA,CAES;IAFD8C,KAAmE,EAAnE;MAAA;MAAA;MAAA;IAAA,CAAmE;IAAErC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAoC,QAAA,IAAApC,QAAA,CAAAoC,QAAA,IAAA5B,IAAA,CAAQ;uCACxFpB,mBAAA,CAA2B,cAArB,gBAAc,mB,6CAI5BoC,mBAAA,6BAAgC,EACrB7B,KAAA,CAAAC,WAAW,e,cAAtBqB,mBAAA,CAoOM,OApONoB,WAoOM,GAnOFjD,mBAAA,CAmBM,OAnBNkD,WAmBM,G,4BAlBFlD,mBAAA,CAGM;IAHDD,KAAK,EAAC;EAAqB,IAC5BC,mBAAA,CAAoC;IAAhCD,KAAK,EAAC;EAAY,GAAC,UAAQ,GAC/BC,mBAAA,CAAyI;IAAtID,KAAK,EAAC;EAAkB,GAAC,2GAAyG,E,qBAEzIC,mBAAA,CAaM,OAbNmD,WAaM,GAZFnD,mBAAA,CAKS;IALAS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAER,QAAA,CAAAwC,WAAA,IAAAxC,QAAA,CAAAwC,WAAA,IAAAhC,IAAA,CAAW;IAAErB,KAAK,EAAC,YAAY;IAAEsD,QAAQ,EAAE9C,KAAA,CAAA+C,iBAAiB,IAAI1C,QAAA,CAAA2C,kBAAkB,CAACC,MAAM;IAAQC,KAAK,EAAC;uCACnHzD,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAmG;IAA7FkB,CAAC,EAAC;EAAyF,G,oBAErGlB,mBAAA,CAAsB,cAAhB,WAAS,mB,kCAEnBA,mBAAA,CAKS;IALAS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAA8C,iBAAA,IAAA9C,QAAA,CAAA8C,iBAAA,IAAAtC,IAAA,CAAiB;IAAErB,KAAK,EAAC,aAAa;IAAEsD,QAAQ,EAAE9C,KAAA,CAAA+C;qBAC9DzB,mBAAA,CAEM;IAFDf,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAAElB,KAAK,EAAAO,eAAA;MAAA,YAAgBC,KAAA,CAAA+C;IAAiB;uCACvGtD,mBAAA,CAAsN;IAAhNkB,CAAC,EAAC;EAA4M,0B,kDAExNlB,mBAAA,CAAkB,cAAZ,OAAK,oB,mCAKvBA,mBAAA,CA6MM,OA7MN2D,WA6MM,GA5MSpD,KAAA,CAAA+C,iBAAiB,I,cAA5BzB,mBAAA,CAGM,OAHN+B,WAGM,OAAAlD,MAAA,SAAAA,MAAA,QAFFV,mBAAA,CAAmC;IAA9BD,KAAK,EAAC;EAAiB,2BAC5BC,mBAAA,CAAmC,cAA7B,wBAAsB,mB,QAGhBO,KAAA,CAAAsD,YAAY,CAACL,MAAM,U,cAAnC3B,mBAAA,CAMM,OANNiC,WAMM,OAAApD,MAAA,SAAAA,MAAA,QALFV,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAAClB,KAAK,EAAC;MACtEC,mBAAA,CAAiI;IAA3HkB,CAAC,EAAC;EAAuH,G,oBAEnIlB,mBAAA,CAAuB,YAAnB,gBAAc,oBAClBA,mBAAA,CAA+C,WAA5C,0CAAwC,mB,2CAG/CoC,mBAAA,qBAAwB,EACb7B,KAAA,CAAAwD,eAAe,I,cAA1BlC,mBAAA,CA8DM;;IA9DsB9B,KAAK,EAAC,oBAAoB;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoD,UAAA,IAAApD,QAAA,CAAAoD,UAAA,IAAA5C,IAAA,CAAU;MACrEpB,mBAAA,CA4DM;IA5DDD,KAAK,EAAC,YAAY;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAuD,cAAA,CAAN,QAAW;MAC/BjE,mBAAA,CAOM,OAPNkE,WAOM,G,4BANFlE,mBAAA,CAAqB,YAAjB,cAAY,qBAChBA,mBAAA,CAIS;IAJAS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoD,UAAA,IAAApD,QAAA,CAAAoD,UAAA,IAAA5C,IAAA,CAAU;IAAErB,KAAK,EAAC;uCAC9BC,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAiH;IAA3GkB,CAAC,EAAC;EAAuG,G,2BAI3HlB,mBAAA,CAkDM,OAlDNmE,WAkDM,GAjDFnE,mBAAA,CASM,OATNoE,WASM,GARFpE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFFrE,mBAAA,CAA+B,eAAxB,kBAAgB,qB,gBACvBA,mBAAA,CAA2E;iEAA3DO,KAAA,CAAAwD,eAAe,CAACO,UAAU,GAAA3D,MAAA;IAAE6B,IAAI,EAAC,MAAM;IAACzC,KAAK,EAAC;iDAA9CQ,KAAA,CAAAwD,eAAe,CAACO,UAAU,E,KAE9CtE,mBAAA,CAGM,OAHNuE,WAGM,G,4BAFFvE,mBAAA,CAA4B,eAArB,eAAa,qB,gBACpBA,mBAAA,CAAsE;iEAAtDO,KAAA,CAAAwD,eAAe,CAACN,KAAK,GAAA9C,MAAA;IAAE6B,IAAI,EAAC,MAAM;IAACzC,KAAK,EAAC;iDAAzCQ,KAAA,CAAAwD,eAAe,CAACN,KAAK,E,OAG7CzD,mBAAA,CAkBM,OAlBNwE,WAkBM,GAjBFxE,mBAAA,CAGM,OAHNyE,WAGM,G,4BAFFzE,mBAAA,CAA4B,eAArB,eAAa,qB,gBACpBA,mBAAA,CAA8E;iEAA9DO,KAAA,CAAAwD,eAAe,CAACW,aAAa,GAAA/D,MAAA;IAAE6B,IAAI,EAAC,MAAM;IAACzC,KAAK,EAAC;iDAAjDQ,KAAA,CAAAwD,eAAe,CAACW,aAAa,E,KAEjD1E,mBAAA,CAYM,OAZN2E,WAYM,G,4BAXF3E,mBAAA,CAA2B,eAApB,cAAY,qB,gBACnBA,mBAAA,CASS;iEATQO,KAAA,CAAAwD,eAAe,CAACa,KAAK,GAAAjE,MAAA;IAAEZ,KAAK,EAAC;geAA7BQ,KAAA,CAAAwD,eAAe,CAACa,KAAK,E,OAY9C5E,mBAAA,CAKM,OALN6E,WAKM,GAJF7E,mBAAA,CAGM,OAHN8E,WAGM,G,4BAFF9E,mBAAA,CAAyB,eAAlB,YAAU,qB,gBACjBA,mBAAA,CAAgG;iEAA7EO,KAAA,CAAAwD,eAAe,CAACgB,iBAAiB,GAAApE,MAAA;IAAEZ,KAAK,EAAC,eAAe;IAACiF,IAAI,EAAC;iDAA9DzE,KAAA,CAAAwD,eAAe,CAACgB,iBAAiB,E,OAG5D/E,mBAAA,CAaM,OAbNiF,WAaM,GAZFjF,mBAAA,CAKS;IALAS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAsE,YAAA,IAAAtE,QAAA,CAAAsE,YAAA,IAAA9D,IAAA,CAAY;IAAErB,KAAK,EAAC;uCAChCC,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAA6D;IAAvDkB,CAAC,EAAC;EAAmD,G,qCACzD,iBAEV,mB,MACAlB,mBAAA,CAKS;IALAS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoD,UAAA,IAAApD,QAAA,CAAAoD,UAAA,IAAA5C,IAAA,CAAU;IAAErB,KAAK,EAAC;uCAC9BC,mBAAA,CAEM;IAFDc,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDjB,mBAAA,CAAiH;IAA3GkB,CAAC,EAAC;EAAuG,G,qCAC7G,SAEV,mB,gBAOAX,KAAA,CAAAsD,YAAY,CAACL,MAAM,Q,cAAnC3B,mBAAA,CA4HMsD,SAAA;IAAAC,GAAA;EAAA,IA7HNhD,mBAAA,sBAAyB,EACzBpC,mBAAA,CA4HM,OA5HNqF,WA4HM,GA3HFrF,mBAAA,CAuCM,OAvCNsF,WAuCM,GAtCFtF,mBAAA,CAqCM,OArCNuF,WAqCM,GApCFvF,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAchF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAChG1F,mBAAA,CAA0D,QAA1D2F,WAA0D,EAAAnE,gBAAA,CAA7BjB,KAAA,CAAAsD,YAAY,CAACL,MAAM,kB,4BAChDxD,mBAAA,CAA+C;IAAzCD,KAAK,EAAC;EAAY,GAAC,iBAAe,oB,kBAE5CC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAqBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACvG1F,mBAAA,CAA+F,QAA/F4F,WAA+F,EAAApE,gBAAA,CAAlEjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,kBAAkBpB,MAAM,kB,4BACrFxD,mBAAA,CAAyC;IAAnCD,KAAK,EAAC;EAAY,GAAC,WAAS,oB,kBAEtCC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G1F,mBAAA,CAAmG,QAAnG+F,WAAmG,EAAAvE,gBAAA,CAAtEjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,sBAAsBpB,MAAM,kB,4BACzFxD,mBAAA,CAA6C;IAAvCD,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAiBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACnG1F,mBAAA,CAA2F,QAA3FgG,WAA2F,EAAAxE,gBAAA,CAA9DjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,cAAcpB,MAAM,kB,4BACjFxD,mBAAA,CAAqC;IAA/BD,KAAK,EAAC;EAAY,GAAC,OAAK,oB,kBAElCC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAiBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACnG1F,mBAAA,CAA2F,QAA3FiG,WAA2F,EAAAzE,gBAAA,CAA9DjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,cAAcpB,MAAM,kB,4BACjFxD,mBAAA,CAAqC;IAA/BD,KAAK,EAAC;EAAY,GAAC,OAAK,oB,kBAElCC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAuBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACzG1F,mBAAA,CAAiG,QAAjGkG,WAAiG,EAAA1E,gBAAA,CAApEjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,oBAAoBpB,MAAM,kB,4BACvFxD,mBAAA,CAA2C;IAArCD,KAAK,EAAC;EAAY,GAAC,aAAW,oB,kBAExCC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G1F,mBAAA,CAAmG,QAAnGmG,WAAmG,EAAA3E,gBAAA,CAAtEjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,sBAAsBpB,MAAM,kB,4BACzFxD,mBAAA,CAA6C;IAAvCD,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G1F,mBAAA,CAAmG,QAAnGoG,WAAmG,EAAA5E,gBAAA,CAAtEjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,sBAAsBpB,MAAM,kB,4BACzFxD,mBAAA,CAA6C;IAAvCD,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CC,mBAAA,CAGM;IAHDD,KAAK,EAAAO,eAAA,EAAC,qBAAqB;MAAAkF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAgBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAClG1F,mBAAA,CAA0F,QAA1FqG,WAA0F,EAAA7E,gBAAA,CAA7DjB,KAAA,CAAAsD,YAAY,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,KAAK,aAAapB,MAAM,kB,4BAChFxD,mBAAA,CAAoC;IAA9BD,KAAK,EAAC;EAAY,GAAC,MAAI,oB,sBAKzCC,mBAAA,CAiFM,OAjFNsG,WAiFM,GAhFFtG,mBAAA,CA+EQ,SA/ERuG,WA+EQ,G,4BA9EJvG,mBAAA,CAWQ,gBAVJA,mBAAA,CASK,aARDA,mBAAA,CAAuC;IAAnCD,KAAK,EAAC;EAAW,GAAC,cAAY,GAClCC,mBAAA,CAAsC;IAAlCD,KAAK,EAAC;EAAW,GAAC,aAAW,GACjCC,mBAAA,CAAsC;IAAlCD,KAAK,EAAC;EAAU,GAAC,cAAY,GACjCC,mBAAA,CAAkC;IAA9BD,KAAK,EAAC;EAAY,GAAC,QAAM,GAC7BC,mBAAA,CAAkC;IAA9BD,KAAK,EAAC;EAAW,GAAC,SAAO,GAC7BC,mBAAA,CAAyC;IAArCD,KAAK,EAAC;EAAiB,GAAC,UAAQ,GACpCC,mBAAA,CAAuC;IAAnCD,KAAK,EAAC;EAAiB,GAAC,QAAM,GAClCC,mBAAA,CAAsC;IAAlCD,KAAK,EAAC;EAAa,GAAC,WAAS,E,uBAGzCC,mBAAA,CAiEQ,iB,kBAhEJ6B,mBAAA,CA+DKsD,SAAA,QAAAqB,WAAA,CA/DkB5F,QAAA,CAAA2C,kBAAkB,EAA9BkD,QAAQ;yBAAnB5E,mBAAA,CA+DK;MA/DuCuD,GAAG,EAAEqB,QAAQ,CAAChE,EAAE;MAAE1C,KAAK,EAAC;QAChEC,mBAAA,CAKK,MALL0G,WAKK,GAJD1G,mBAAA,CAGM,OAHN2G,WAGM,GAFF3G,mBAAA,CAA6B,YAAAwB,gBAAA,CAAtBiF,QAAQ,CAAChD,KAAK,kBACrBzD,mBAAA,CAAqF,KAArF4G,WAAqF,EAAApF,gBAAA,CAAlDiF,QAAQ,CAAC1B,iBAAiB,kC,KAGrE/E,mBAAA,CAIK,MAJL6G,WAIK,GAHD7G,mBAAA,CAEM,OAFN8G,WAEM,GADF9G,mBAAA,CAAsC,cAAAwB,gBAAA,CAA7BiF,QAAQ,CAACnC,UAAU,iB,KAGpCtE,mBAAA,CAKK,MALL+G,WAKK,GAJD/G,mBAAA,CAGM,OAHNgH,WAGM,GAFFhH,mBAAA,CAAqI,QAArIiH,WAAqI,EAAAzF,gBAAA,KAAtG0F,IAAI,CAACT,QAAQ,CAAC/B,aAAa,EAAEyC,kBAAkB;MAAAC,GAAA;MAAAC,KAAA;IAAA,oBAC9ErH,mBAAA,CAAmF,QAAnFsH,WAAmF,EAAA9F,gBAAA,KAApD0F,IAAI,CAACT,QAAQ,CAAC/B,aAAa,EAAE6C,WAAW,mB,KAG/EvH,mBAAA,CAKK,MALLwH,WAKK,GAJDxH,mBAAA,CAGO;MAHDD,KAAK,EAAAO,eAAA,EAAC,cAAc,EAASM,QAAA,CAAA6G,cAAc,CAAChB,QAAQ,CAAC7B,KAAK;oCAC5D5E,mBAAA,CAAoC;MAA/BD,KAAK,EAAC;IAAkB,4B,iBAAO,GACpC,GAAAyB,gBAAA,CAAGiF,QAAQ,CAAC7B,KAAK,iB,oBAGzB5E,mBAAA,CAkBK,MAlBL0H,WAkBK,GAjBD1H,mBAAA,CAgBM,OAhBN2H,WAgBM,GAfSlB,QAAQ,CAACmB,KAAK,IAAInB,QAAQ,CAACmB,KAAK,CAACpE,MAAM,Q,cAAlD3B,mBAAA,CAaM,OAbNgG,WAaM,I,kBAZFhG,mBAAA,CAWSsD,SAAA,QAAAqB,WAAA,CAVUC,QAAQ,CAACmB,KAAK,EAAtBE,IAAI;2BADfjG,mBAAA,CAWS;QATJuD,GAAG,EAAE0C,IAAI,CAACrF,EAAE;QACZhC,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAmH,YAAY,CAACD,IAAI;QACzB/H,KAAK,EAAC,mBAAmB;QACxB0D,KAAK,WAAWqE,IAAI,CAACE,SAAS;sCAE/BhI,mBAAA,CAEM;QAFDc,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC;UACjDjB,mBAAA,CAAmG;QAA7FkB,CAAC,EAAC;MAAyF,G,qBAErGlB,mBAAA,CAAqE,QAArEiI,WAAqE,EAAAzG,gBAAA,CAA1CZ,QAAA,CAAAsH,gBAAgB,CAACJ,IAAI,CAACE,SAAS,kB;uDAGlEnG,mBAAA,CAAkD,QAAlDsG,WAAkD,EAApB,eAAa,G,KAGnDnI,mBAAA,CAEK,MAFLoI,WAEK,GADDpI,mBAAA,CAAgF,QAAhFqI,WAAgF,EAAA7G,gBAAA,CAA9CiF,QAAQ,CAAC6B,eAAe,CAACC,WAAW,iB,GAE1EvI,mBAAA,CAEK,MAFLwI,WAEK,GADDxI,mBAAA,CAAqF,QAArFyI,WAAqF,EAAAjH,gBAAA,CAAnDiF,QAAQ,CAAC6B,eAAe,CAACI,gBAAgB,iB,GAE/E1I,mBAAA,CAaK,MAbL2I,WAaK,GAZD3I,mBAAA,CAWM,OAXN4I,WAWM,GAVF5I,mBAAA,CAIS;MAJAS,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAiI,YAAY,CAACpC,QAAQ;MAAG1G,KAAK,EAAC,qBAAqB;MAAC0D,KAAK,EAAC;yCACtEzD,mBAAA,CAEM;MAFDc,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACjDjB,mBAAA,CAAiK;MAA3JkB,CAAC,EAAC;IAAuJ,G,qDAGvKlB,mBAAA,CAIS;MAJAS,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAkI,cAAc,CAACrC,QAAQ,CAAChE,EAAE;MAAG1C,KAAK,EAAC,uBAAuB;MAAC0D,KAAK,EAAC;yCAC7EzD,mBAAA,CAEM;MAFDc,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACjDjB,mBAAA,CAAyF;MAAnFkB,CAAC,EAAC;IAA+E,G;2KAcvIkB,mBAAA,4BAA+B,EACpB7B,KAAA,CAAAwI,mBAAmB,I,cAA9BlH,mBAAA,CA6EM;;IA7E0B9B,KAAK,EAAC,eAAe;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoI,oBAAA,IAAApI,QAAA,CAAAoI,oBAAA,IAAA5H,IAAA,CAAoB;MAC9EpB,mBAAA,CA2EM;IA3EDD,KAAK,EAAC,sCAAsC;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAuD,cAAA,CAAN,QAAW;MACzDjE,mBAAA,CAGM,OAHNiJ,WAGM,G,4BAFFjJ,mBAAA,CAAuB,YAAnB,gBAAc,qBAClBA,mBAAA,CAAwE;IAA/DS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoI,oBAAA,IAAApI,QAAA,CAAAoI,oBAAA,IAAA5H,IAAA,CAAoB;IAAErB,KAAK,EAAC;KAAY,GAAO,E,GAGnEC,mBAAA,CAoEO;IApEAkJ,QAAM,EAAAxI,MAAA,SAAAA,MAAA,OAAAuD,cAAA,KAAA7C,IAAA,KAAUR,QAAA,CAAAuI,qBAAA,IAAAvI,QAAA,CAAAuI,qBAAA,IAAA/H,IAAA,CAAqB;IAAErB,KAAK,EAAC;MAChDC,mBAAA,CAUM,OAVNoJ,WAUM,G,4BATFpJ,mBAAA,CAA2C;IAApCuC,GAAG,EAAC;EAAU,GAAC,eAAa,qB,gBACnCvC,mBAAA,CAOC;IANGwC,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,UAAU;iEACJlC,KAAA,CAAA8I,WAAW,CAACC,QAAQ,GAAA3I,MAAA;IAC7BiC,QAAQ,EAAR,EAAQ;IACR7C,KAAK,EAAC,YAAY;IAClB2C,WAAW,EAAC;iDAHHnC,KAAA,CAAA8I,WAAW,CAACC,QAAQ,E,KAOrCtJ,mBAAA,CASM,OATNuJ,WASM,G,4BARFvJ,mBAAA,CAA+C;IAAxCuC,GAAG,EAAC;EAAS,GAAC,oBAAkB,qB,gBACvCvC,mBAAA,CAMC;IALGwC,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,SAAS;iEACHlC,KAAA,CAAA8I,WAAW,CAACG,OAAO,GAAA7I,MAAA;IAC5BZ,KAAK,EAAC,YAAY;IAClB2C,WAAW,EAAC;iDAFHnC,KAAA,CAAA8I,WAAW,CAACG,OAAO,E,KAMpCxJ,mBAAA,CAmCM,OAnCNyJ,WAmCM,G,8BAlCFzJ,mBAAA,CAAoC,YAAhC,6BAA2B,qBAE/BA,mBAAA,CASM,OATN0J,WASM,G,4BARF1J,mBAAA,CAAyD;IAAlDuC,GAAG,EAAC;EAAiB,GAAC,sBAAoB,qB,gBACjDvC,mBAAA,CAMC;IALGwC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,iBAAiB;iEACXlC,KAAA,CAAA8I,WAAW,CAACM,eAAe,GAAAhJ,MAAA;IACpCZ,KAAK,EAAC,YAAY;IAClB2C,WAAW,EAAC;iDAFHnC,KAAA,CAAA8I,WAAW,CAACM,eAAe,E,KAM5C3J,mBAAA,CASM,OATN4J,WASM,G,4BARF5J,mBAAA,CAAqD;IAA9CuC,GAAG,EAAC;EAAa,GAAC,sBAAoB,qB,gBAC7CvC,mBAAA,CAMC;IALGwC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,aAAa;iEACPlC,KAAA,CAAA8I,WAAW,CAACQ,WAAW,GAAAlJ,MAAA;IAChCZ,KAAK,EAAC,YAAY;IAClB2C,WAAW,EAAC;iDAFHnC,KAAA,CAAA8I,WAAW,CAACQ,WAAW,E,KAMxC7J,mBAAA,CASM,OATN8J,WASM,G,4BARF9J,mBAAA,CAA+D;IAAxDuC,GAAG,EAAC;EAAiB,GAAC,4BAA0B,qB,gBACvDvC,mBAAA,CAMC;IALGwC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,iBAAiB;iEACXlC,KAAA,CAAA8I,WAAW,CAACU,eAAe,GAAApJ,MAAA;IACpCZ,KAAK,EAAC,YAAY;IAClB2C,WAAW,EAAC;iDAFHnC,KAAA,CAAA8I,WAAW,CAACU,eAAe,E,OAOhD/J,mBAAA,CAMM,OANNgK,WAMM,GALFhK,mBAAA,CAAqF;IAA7EwC,IAAI,EAAC,QAAQ;IAAE/B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAoI,oBAAA,IAAApI,QAAA,CAAAoI,oBAAA,IAAA5H,IAAA,CAAoB;IAAErB,KAAK,EAAC;KAAa,OAAK,GAC5EC,mBAAA,CAGS;IAHDwC,IAAI,EAAC,QAAQ;IAAEa,QAAQ,EAAE9C,KAAA,CAAA0J,eAAe;IAAElK,KAAK,EAAC;MACxCQ,KAAA,CAAA0J,eAAe,I,cAA3BpI,mBAAA,CAAiD,QAAAqI,WAAA,EAApB,eAAa,M,cAC1CrI,mBAAA,CAAiC,QAAAsI,WAAA,EAApB,eAAa,G,qGAO9C/H,mBAAA,4BAA+B,EACpB7B,KAAA,CAAA6J,mBAAmB,I,cAA9BvI,mBAAA,CA6BM;;IA7B0B9B,KAAK,EAAC,eAAe;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAyJ,oBAAA,IAAAzJ,QAAA,CAAAyJ,oBAAA,IAAAjJ,IAAA,CAAoB;MAC9EpB,mBAAA,CA2BM;IA3BDD,KAAK,EAAC,yBAAyB;IAAEU,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAuD,cAAA,CAAN,QAAW;MAC5CjE,mBAAA,CAGM,OAHNsK,WAGM,G,8BAFFtK,mBAAA,CAAsB,YAAlB,eAAa,qBACjBA,mBAAA,CAAwE;IAA/DS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAyJ,oBAAA,IAAAzJ,QAAA,CAAAyJ,oBAAA,IAAAjJ,IAAA,CAAoB;IAAErB,KAAK,EAAC;KAAY,GAAO,E,GAEnEC,mBAAA,CAeM,OAfNuK,WAeM,GAdFvK,mBAAA,CAEI,KAFJwK,WAEI,EAAAhJ,gBAAA,CADGjB,KAAA,CAAAkK,mBAAmB,CAACC,MAAM,uIAEjC1K,mBAAA,CAUM,OAVN2K,WAUM,G,8BATF3K,mBAAA,CAAkD;IAA3CuC,GAAG,EAAC;EAAY,GAAC,oBAAkB,qB,gBAC1CvC,mBAAA,CAOC;IANGwC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,YAAY;iEACNlC,KAAA,CAAAkK,mBAAmB,CAACG,GAAG,GAAAjK,MAAA;IAChC+B,WAAW,EAAC,wBAAwB;IACnCmI,OAAK,EAAAnK,MAAA,SAAAA,MAAA,OAAAoK,SAAA,KAAA1J,IAAA,KAAQR,QAAA,CAAAmK,gBAAA,IAAAnK,QAAA,CAAAmK,gBAAA,IAAA3J,IAAA,CAAgB;IAC9BrB,KAAK,EAAC;iEAHGQ,KAAA,CAAAkK,mBAAmB,CAACG,GAAG,E,OAO5C5K,mBAAA,CAKM,OALNgL,WAKM,GAJFhL,mBAAA,CAAuE;IAA9DS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAyJ,oBAAA,IAAAzJ,QAAA,CAAAyJ,oBAAA,IAAAjJ,IAAA,CAAoB;IAAErB,KAAK,EAAC;KAAa,OAAK,GAC9DC,mBAAA,CAES;IAFAS,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAU,IAAA,KAAER,QAAA,CAAAmK,gBAAA,IAAAnK,QAAA,CAAAmK,gBAAA,IAAA3J,IAAA,CAAgB;IAAErB,KAAK,EAAC,aAAa;IAAEsD,QAAQ,GAAG9C,KAAA,CAAAkK,mBAAmB,CAACG;sBAC/ErK,KAAA,CAAAkK,mBAAmB,CAACC,MAAM,uDAAAO,WAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}