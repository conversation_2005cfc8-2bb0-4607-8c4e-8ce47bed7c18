{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode, Fragment as _Fragment } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"content-wrapper\"\n};\nconst _hoisted_2 = {\n  class: \"auth-container\"\n};\nconst _hoisted_3 = {\n  class: \"card\"\n};\nconst _hoisted_4 = {\n  class: \"card-body\"\n};\nconst _hoisted_5 = {\n  class: \"form-group\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = {\n  class: \"form-group\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  key: 0,\n  class: \"loading-spinner\",\n  width: \"20\",\n  height: \"20\",\n  viewBox: \"0 0 24 24\"\n};\nconst _hoisted_10 = {\n  key: 1\n};\nconst _hoisted_11 = {\n  key: 2\n};\nconst _hoisted_12 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_13 = {\n  class: \"auth-footer\"\n};\nconst _hoisted_14 = {\n  class: \"auth-hint\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_cache[12] || (_cache[12] = _createStaticVNode(\"<div class=\\\"navbar\\\" data-v-5c6101e4><div class=\\\"navbar-content\\\" data-v-5c6101e4><div class=\\\"navbar-brand\\\" data-v-5c6101e4><span class=\\\"navbar-text\\\" data-v-5c6101e4>المجلس الأعلى للشباب</span><img class=\\\"navbar-logo\\\" src=\\\"\" + _imports_0 + \"\\\" alt=\\\"شعار الفريق\\\" data-v-5c6101e4><span class=\\\"navbar-text\\\" data-v-5c6101e4>الفريق الوطني للشباب الرقمي</span></div></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"card-title\"\n  }, \"تسجيل الدخول\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"form\", {\n    onSubmit: _cache[3] || (_cache[3] = _withModifiers((...args) => $options.doLogin && $options.doLogin(...args), [\"prevent\"])),\n    class: \"auth-form\"\n  }, [_createElementVNode(\"div\", _hoisted_5, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"username\"\n  }, \"اسم المستخدم\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    type: \"text\",\n    class: \"form-input\",\n    placeholder: \"ادخل اسم المستخدم\",\n    required: \"\",\n    autocomplete: \"username\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]])]), _createElementVNode(\"div\", _hoisted_6, [_cache[5] || (_cache[5] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"password\"\n  }, \"كلمة المرور\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    type: \"password\",\n    class: \"form-input\",\n    placeholder: \"ادخل كلمة المرور\",\n    required: \"\",\n    autocomplete: \"current-password\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]])]), _createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"team_pin\"\n  }, \"رمز الفريق الرقمي\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"team_pin\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.team_pin = $event),\n    type: \"text\",\n    class: \"form-input\",\n    placeholder: \"ادخل رمز الفريق الرقمي\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.team_pin]])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.busy,\n    class: \"btn btn-primary btn-lg auth-submit\"\n  }, [$data.busy ? (_openBlock(), _createElementBlock(\"svg\", _hoisted_9, [...(_cache[7] || (_cache[7] = [_createElementVNode(\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    fill: \"none\",\n    \"stroke-dasharray\": \"31.416\",\n    \"stroke-dashoffset\": \"31.416\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"stroke-dasharray\",\n    dur: \"2s\",\n    values: \"0 31.416;15.708 15.708;0 31.416\",\n    repeatCount: \"indefinite\"\n  }), _createElementVNode(\"animate\", {\n    attributeName: \"stroke-dashoffset\",\n    dur: \"2s\",\n    values: \"0;-15.708;-31.416\",\n    repeatCount: \"indefinite\"\n  })], -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), !$data.busy ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, \"دخول\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_11, \"جاري الدخول...\"))], 8 /* PROPS */, _hoisted_8), $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_cache[8] || (_cache[8] = _createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n  })], -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.error), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"p\", _hoisted_14, [_cache[10] || (_cache[10] = _createTextVNode(\" ليس لديك حساب؟ \", -1 /* CACHED */)), _createVNode(_component_router_link, {\n    to: \"/register\",\n    class: \"auth-link\"\n  }, {\n    default: _withCtx(() => [...(_cache[9] || (_cache[9] = [_createTextVNode(\"سجل الآن\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  })])])], 32 /* NEED_HYDRATION */)])])])])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "width", "height", "viewBox", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onSubmit", "_cache", "_withModifiers", "args", "$options", "do<PERSON><PERSON><PERSON>", "_hoisted_5", "for", "id", "$data", "username", "$event", "type", "placeholder", "required", "autocomplete", "_hoisted_6", "password", "_hoisted_7", "team_pin", "disabled", "busy", "_createElementBlock", "_hoisted_9", "cx", "cy", "r", "stroke", "fill", "attributeName", "dur", "values", "repeatCount", "_hoisted_10", "_hoisted_11", "error", "_hoisted_12", "d", "_toDisplayString", "_hoisted_13", "_hoisted_14", "_createVNode", "_component_router_link", "to"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\LoginView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'LoginView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      team_pin: '',\r\n      busy: false,\r\n      error: ''\r\n    };\r\n  },\r\n  mounted() {\r\n    // Aggressive cache busting for login component\r\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\r\n    \r\n    // Add cache-busting meta tags specifically for login\r\n    const metaTags = [\r\n      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\r\n      { name: 'pragma', content: 'no-cache' },\r\n      { name: 'expires', content: '0' },\r\n      { name: 'last-modified', content: new Date().toUTCString() },\r\n      { name: 'etag', content: cacheBuster },\r\n      { name: 'login-cache-buster', content: cacheBuster }\r\n    ];\r\n    \r\n    metaTags.forEach(tag => {\r\n      // Remove existing meta tags with same name\r\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\r\n      if (existing) existing.remove();\r\n      \r\n      // Add new meta tag\r\n      const meta = document.createElement('meta');\r\n      meta.setAttribute('http-equiv', tag.name);\r\n      meta.setAttribute('content', tag.content);\r\n      document.head.appendChild(meta);\r\n    });\r\n    \r\n    // Force component refresh to prevent caching issues\r\n    this.$forceUpdate();\r\n    \r\n    // Clear any cached form data\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n    \r\n    // Clear only non-authentication browser storage\r\n    if (typeof Storage !== 'undefined') {\r\n      // Only clear sessionStorage, preserve localStorage auth tokens\r\n      sessionStorage.clear();\r\n    }\r\n    \r\n    // Force browser to not cache this page\r\n    if (window.history && window.history.replaceState) {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.set('_cb', cacheBuster);\r\n      url.searchParams.set('_nocache', '1');\r\n      window.history.replaceState(null, null, url.toString());\r\n    }\r\n    \r\n    // Add cache-busting attribute to component element\r\n    if (this.$el && this.$el.setAttribute) {\r\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\r\n      this.$el.setAttribute('data-no-cache', 'true');\r\n    }\r\n  },\r\n  beforeUnmount() {\r\n    // Clear form data when component is destroyed\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n  },\r\n  methods: {\r\n    async doLogin() {\r\n      this.error = '';\r\n      if (!this.username || !this.password || !this.team_pin) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        // optional: store user info\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        // store team pin for later submissions\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-brand\">\r\n        <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n        <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"content-wrapper\">\r\n    <div class=\"auth-container\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">تسجيل الدخول</h1>\r\n        </div>\r\n\r\n        <div class=\"card-body\">\r\n          <form @submit.prevent=\"doLogin\" class=\"auth-form\">\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"username\">اسم المستخدم</label>\r\n              <input\r\n                id=\"username\"\r\n                v-model=\"username\"\r\n                type=\"text\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل اسم المستخدم\"\r\n                required\r\n                autocomplete=\"username\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"password\">كلمة المرور</label>\r\n              <input\r\n                id=\"password\"\r\n                v-model=\"password\"\r\n                type=\"password\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل كلمة المرور\"\r\n                required\r\n                autocomplete=\"current-password\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"team_pin\">رمز الفريق الرقمي</label>\r\n              <input\r\n                id=\"team_pin\"\r\n                v-model=\"team_pin\"\r\n                type=\"text\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل رمز الفريق الرقمي\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            <button type=\"submit\" :disabled=\"busy\" class=\"btn btn-primary btn-lg auth-submit\">\r\n              <svg v-if=\"busy\" class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\r\n                  <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\r\n                  <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\r\n                </circle>\r\n              </svg>\r\n              <span v-if=\"!busy\">دخول</span>\r\n              <span v-else>جاري الدخول...</span>\r\n            </button>\r\n\r\n            <div v-if=\"error\" class=\"error-message\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n              </svg>\r\n              {{ error }}\r\n            </div>\r\n\r\n            <div class=\"auth-footer\">\r\n              <p class=\"auth-hint\">\r\n                ليس لديك حساب؟\r\n                <router-link to=\"/register\" class=\"auth-link\">سجل الآن</router-link>\r\n              </p>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Enhanced Auth Container */\r\n.auth-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 120px);\r\n  padding: var(--space-8) var(--space-4);\r\n}\r\n\r\n/* Enhanced Form Styling */\r\n.auth-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-6);\r\n  width: 100%;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-2);\r\n}\r\n\r\n.auth-submit {\r\n  margin-top: var(--space-4);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.auth-submit::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.auth-submit:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n/* Loading Spinner Animation */\r\n.loading-spinner {\r\n  animation: spin 1s linear infinite;\r\n  margin-right: var(--space-2);\r\n}\r\n\r\n/* Error Message Styling */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-3) var(--space-4);\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid var(--color-error-500);\r\n  border-radius: var(--radius-xl);\r\n  color: var(--color-error-500);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n  text-align: right;\r\n  animation: slideDown 0.3s ease-out;\r\n}\r\n\r\n/* Auth Footer */\r\n.auth-footer {\r\n  margin-top: var(--space-6);\r\n  padding-top: var(--space-4);\r\n  border-top: 1px solid var(--border-primary);\r\n  text-align: center;\r\n}\r\n\r\n.auth-hint {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin: 0;\r\n  line-height: var(--line-height-relaxed);\r\n}\r\n\r\n.auth-link {\r\n  color: var(--accent-primary);\r\n  text-decoration: none;\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  border-radius: var(--radius-base);\r\n  padding: var(--space-1) var(--space-2);\r\n  margin: 0 calc(-1 * var(--space-2));\r\n}\r\n\r\n.auth-link:hover {\r\n  color: var(--color-primary-400);\r\n  background: rgba(14, 165, 233, 0.1);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Enhanced Card Styling */\r\n.card {\r\n  width: 100%;\r\n  max-width: 480px;\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 2px solid var(--border-primary);\r\n  border-radius: var(--radius-3xl);\r\n  box-shadow: var(--shadow-dark-xl);\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));\r\n  background-size: 200% 100%;\r\n  animation: shimmer 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0%, 100% { background-position: 200% 0; }\r\n  50% { background-position: -200% 0; }\r\n}\r\n.card {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 20px;\r\n  padding: 32px;\r\n  width: 100%;\r\n  max-width: 420px;\r\n  color: #e5e7eb;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n  direction: rtl;\r\n}\r\n.card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n.card h1 {\r\n  font-size: 28px;\r\n  margin: 0 0 32px;\r\n  text-align: center;\r\n  font-weight: 700;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n.field {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  margin: 20px 0;\r\n}\r\n.field label {\r\n  font-size: 14px;\r\n  color: #cbd5e1;\r\n  font-weight: 600;\r\n  text-align: right;\r\n}\r\ninput {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  color: #e5e7eb;\r\n  border-radius: 12px;\r\n  padding: 14px 16px;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\ninput:focus {\r\n  outline: none;\r\n  border-color: #4f46e5;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\ninput::placeholder {\r\n  color: #cbd5e1;\r\n  text-align: right;\r\n  opacity: 1;\r\n}\r\nbutton {\r\n  width: 100%;\r\n  height: 48px;\r\n  min-width: 160px;\r\n  border: none;\r\n  border-radius: 12px;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\nbutton:hover {\r\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\nbutton:active {\r\n  transform: translateY(0);\r\n}\r\nbutton:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n.error {\r\n  color: #ef4444;\r\n  margin-top: 10px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n.hint {\r\n  margin-top: 24px;\r\n  color: #cbd5e1;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n.hint a {\r\n  color: #4f46e5;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: color 0.3s ease;\r\n}\r\n.hint a:hover {\r\n  color: #6366f1;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .card {\r\n    padding: 24px;\r\n    margin: 16px;\r\n  }\r\n  \r\n  .card h1 {\r\n    font-size: 24px;\r\n    margin-bottom: 24px;\r\n  }\r\n  \r\n  input {\r\n    padding: 12px 14px;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n  \r\n  button {\r\n    padding: 12px;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .navbar-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .navbar-text {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .org-name, .team-name {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .navbar {\r\n    margin: 8px;\r\n  }\r\n  \r\n  .navbar-content {\r\n    padding: 12px 16px;\r\n  }\r\n  \r\n  .org-name, .team-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .logo {\r\n    height: 50px;\r\n  }\r\n  \r\n  .auth-wrapper {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .card {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .card h1 {\r\n    font-size: 22px;\r\n  }\r\n  \r\n  .field {\r\n    margin: 16px 0;\r\n  }\r\n}\r\n</style>"], "mappings": ";OA+GiCA,UAA6B;;EAMvDC,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAM;;EAKVA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAY;;;;EAaJA,KAAK,EAAC,iBAAiB;EAACC,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;;;;;;;EAUxDH,KAAK,EAAC;;;EAOnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAW;;;sfAlEhCI,mBAAA,CA2EM,OA3ENC,UA2EM,GA1EJD,mBAAA,CAyEM,OAzENE,UAyEM,GAxEJF,mBAAA,CAuEM,OAvENG,UAuEM,G,4BAtEJH,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAwC;IAApCJ,KAAK,EAAC;EAAY,GAAC,cAAY,E,qBAGrCI,mBAAA,CAiEM,OAjENI,UAiEM,GAhEJJ,mBAAA,CA+DO;IA/DAK,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,OAAA,IAAAD,QAAA,CAAAC,OAAA,IAAAF,IAAA,CAAO;IAAEZ,KAAK,EAAC;MACpCI,mBAAA,CAWM,OAXNW,UAWM,G,0BAVJX,mBAAA,CAA6D;IAAtDJ,KAAK,EAAC,YAAY;IAACgB,GAAG,EAAC;KAAW,cAAY,qB,gBACrDZ,mBAAA,CAQE;IAPAa,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXrB,KAAK,EAAC,YAAY;IAClBsB,WAAW,EAAC,mBAAmB;IAC/BC,QAAQ,EAAR,EAAQ;IACRC,YAAY,EAAC;iDALJN,KAAA,CAAAC,QAAQ,E,KASrBf,mBAAA,CAWM,OAXNqB,UAWM,G,0BAVJrB,mBAAA,CAA4D;IAArDJ,KAAK,EAAC,YAAY;IAACgB,GAAG,EAAC;KAAW,aAAW,qB,gBACpDZ,mBAAA,CAQE;IAPAa,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAQ,QAAQ,GAAAN,MAAA;IACjBC,IAAI,EAAC,UAAU;IACfrB,KAAK,EAAC,YAAY;IAClBsB,WAAW,EAAC,kBAAkB;IAC9BC,QAAQ,EAAR,EAAQ;IACRC,YAAY,EAAC;iDALJN,KAAA,CAAAQ,QAAQ,E,KASrBtB,mBAAA,CAUM,OAVNuB,UAUM,G,0BATJvB,mBAAA,CAAkE;IAA3DJ,KAAK,EAAC,YAAY;IAACgB,GAAG,EAAC;KAAW,mBAAiB,qB,gBAC1DZ,mBAAA,CAOE;IANAa,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAU,QAAQ,GAAAR,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXrB,KAAK,EAAC,YAAY;IAClBsB,WAAW,EAAC,wBAAwB;IACpCC,QAAQ,EAAR;iDAJSL,KAAA,CAAAU,QAAQ,E,KAQrBxB,mBAAA,CASS;IATDiB,IAAI,EAAC,QAAQ;IAAEQ,QAAQ,EAAEX,KAAA,CAAAY,IAAI;IAAE9B,KAAK,EAAC;MAChCkB,KAAA,CAAAY,IAAI,I,cAAfC,mBAAA,CAKM,OALNC,UAKM,OAAAtB,MAAA,QAAAA,MAAA,OAJJN,mBAAA,CAGS;IAHD6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAACC,IAAI,EAAC,MAAM;IAAC,kBAAgB,EAAC,QAAQ;IAAC,mBAAiB,EAAC;MAC5HjC,mBAAA,CAAsH;IAA7GkC,aAAa,EAAC,kBAAkB;IAACC,GAAG,EAAC,IAAI;IAACC,MAAM,EAAC,iCAAiC;IAACC,WAAW,EAAC;MACxGrC,mBAAA,CAAyG;IAAhGkC,aAAa,EAAC,mBAAmB;IAACC,GAAG,EAAC,IAAI;IAACC,MAAM,EAAC,mBAAmB;IAACC,WAAW,EAAC;oEAGlFvB,KAAA,CAAAY,IAAI,I,cAAjBC,mBAAA,CAA8B,QAAAW,WAAA,EAAX,MAAI,M,cACvBX,mBAAA,CAAkC,QAAAY,WAAA,EAArB,gBAAc,G,8BAGlBzB,KAAA,CAAA0B,KAAK,I,cAAhBb,mBAAA,CAKM,OALNc,WAKM,G,0BAJJzC,mBAAA,CAEM;IAFDH,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACkC,IAAI,EAAC;MACnDjC,mBAAA,CAAiI;IAA3H0C,CAAC,EAAC;EAAuH,G,sCAC3H,GACN,GAAAC,gBAAA,CAAG7B,KAAA,CAAA0B,KAAK,iB,wCAGVxC,mBAAA,CAKM,OALN4C,WAKM,GAJJ5C,mBAAA,CAGI,KAHJ6C,WAGI,G,6CAHiB,kBAEnB,qBAAAC,YAAA,CAAoEC,sBAAA;IAAvDC,EAAE,EAAC,WAAW;IAACpD,KAAK,EAAC;;sBAAY,MAAQ,KAAAU,MAAA,QAAAA,MAAA,O,iBAAR,UAAQ,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}