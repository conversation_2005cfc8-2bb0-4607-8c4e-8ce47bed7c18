require('dotenv').config();
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const pool = require('./configcred/configcred');
const admin = require('firebase-admin');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const helmet = require('helmet');
const cors = require('cors');
const cookieParser = require('cookie-parser');
// axios import removed as per plan's import list

// Import todo module
const todoModule = require('./todo');
const { initializeMonitor } = require('./iqtp-monitor');

// Import new v1 routes
const visitsRoutesV1 = require('./routes/visits-v1'); // Moved and updated from original
const offlinegamesRoutesV1 = require('./routes/games-v1');   // New
const { router: chatRouterV1, initializeOfflineGamesChatWebSocket, getWebSocketServer } = require('./routes/chat-v1');     // New, destructured import
const hostyRoutes = require('./routes/hosty'); // Hosty static site hosting routes
const ndytActivitiesRoutesV1 = require('./routes/ndyt-activities-v1');

const app = express();
const port = process.env.PORT || 3000; // Updated: HTTP_PORT to port
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
const JWT_SECRET = process.env.JWT_SECRET; // New: JWT_SECRET

// Initialize Firebase Admin
const serviceAccount = require('./configcred/firebase-service-account.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Import FCM Notification module v1
const fcmNotificationsV1 = require('./routes/fcm-notifications-v1');

// Use the JWT_SECRET from environment
function generateToken(username) {
  // You can add more user information here, like roles or permissions if needed
  const payload = { username };
  return jwt.sign(payload, JWT_SECRET, {algorithm: 'HS256', expiresIn: '1h' }); // Updated: uses JWT_SECRET
}
// PostgreSQL config/pool initialization updated by direct import of 'pool'
const corsOptions = {
  origin: ['https://iqtp.tplinkdns.com', 
    'http://iqtp.tplinkdns.com',  // Add HTTP version
    'http://localhost:3000',
    'http://localhost:8080',
    'http://localhost:5173',
    'https://iqtp.top',
    'http://iqtp.top', // Add HTTP version
    'https://*.supabase.co',      // Supabase hosted projects
    'https://*.supabase.com',     // Supabase dashboard and services
    'https://supabase.com',       // Main Supabase domain
    'https://supabase.co',        // Alternative Supabase domain
  ], // Allowed domains
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  credentials: true
};

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  next();
});

// Security middleware - conditional Helmet (exclude hosted sites)
app.use((req, res, next) => {
  // Skip Helmet CSP for hosted sites to allow any external resources
  if (req.path.startsWith('/hosted/')) {
    return next();
  }
  
  // Apply Helmet CSP for all other routes
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: [
          "'self'",
          "'unsafe-inline'",
          "'unsafe-eval'",
          "'unsafe-hashes'",
          "blob:",                    // Allow blob URLs for hosted sites
          "data:",                    // Allow data URLs for inline scripts
          "https://unpkg.com",        // For Vue.js
          "https://cdn.tailwindcss.com", // For Tailwind CSS
          "https://cdn.jsdelivr.net",
          "https://cdnjs.cloudflare.com",
          "https://me.kis.v2.scr.kaspersky-labs.com",
          "https://www.gstatic.com",
          "https://firebase.googleapis.com",
          "https://firebaseinstallations.googleapis.com",
          "https://www.googletagmanager.com",
          "https://*.supabase.co",    // Supabase hosted projects
          "https://*.supabase.com",   // Supabase services
          "https://supabase.com",     // Main Supabase domain
          "https://supabase.co"       // Alternative Supabase domain
        ],
        scriptSrcAttr: ["'unsafe-inline'", "'unsafe-hashes'"],
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://cdn.jsdelivr.net",
          "https://cdnjs.cloudflare.com",
          "https://fonts.googleapis.com"
        ],
        fontSrc: [
          "'self'",
          "https://cdn.jsdelivr.net",
          "https://cdnjs.cloudflare.com",
          "https://fonts.gstatic.com"
        ],
        imgSrc: [
          "'self'",
          "data:",
          "https:",
          "blob:",
          "https://iqtp.top",
          "http://iqtp.top",
        ],
        connectSrc: [
          "'self'",
          "https://iqtp.top",
          "http://iqtp.top",
          "https://iqtp.tplinkdns.com",
          "http://iqtp.tplinkdns.com",
          "wss://iqtp.tplinkdns.com",
          "ws://iqtp.tplinkdns.com",
          "http://localhost:3000",
          "ws://localhost:3000",
          "ws://localhost:3000/monitor/ws",
          "wss://localhost:3000",
          
          'http://localhost:5173',
          'http://localhost:5174',
          "https://me.kis.v2.scr.kaspersky-labs.com",
          "https://firebase.googleapis.com",
          "https://firebaseinstallations.googleapis.com",
          "https://*.supabase.co",    // Supabase hosted projects API
          "https://*.supabase.com",   // Supabase services API
          "https://supabase.com",     // Main Supabase domain
          "https://supabase.co",      // Alternative Supabase domain
          "wss://*.supabase.co",      // Supabase WebSocket connections
          "wss://*.supabase.com"      // Supabase WebSocket services
        ],
        frameSrc: [
          "'self'",
          "https://www.youtube.com",           // YouTube regular embeds
          "https://www.youtube-nocookie.com",  // YouTube privacy-enhanced embeds
          "https://youtube.com",               // Alternative YouTube domain
          "https://youtu.be"                   // YouTube short links
        ],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: []
      }
    }
  })(req, res, next);
});

// Conditional CORS middleware (exclude hosted sites)
app.use((req, res, next) => {
  // Skip CORS restrictions for hosted sites to allow any origin
  if (req.path.startsWith('/hosted/')) {
    return next();
  }
  
  // Apply CORS for all other routes
  cors(corsOptions)(req, res, next);
});

// Trust proxy for nginx reverse proxy
app.set('trust proxy', true);

// Cookie parser middleware
app.use(cookieParser());

// Configure Express and body-parser with increased limits
// Only apply to non-multipart requests to avoid conflicts with Multer
app.use((req, res, next) => {
  const contentType = req.get('Content-Type') || '';
  if (!contentType.includes('multipart/form-data')) {
    express.json({limit: '10mb'})(req, res, next);
  } else {
    next();
  }
});

app.use((req, res, next) => {
  const contentType = req.get('Content-Type') || '';
  if (!contentType.includes('multipart/form-data')) {
    express.urlencoded({limit: '10mb', extended: true})(req, res, next);
  } else {
    next();
  }
});

// Configure body-parser with increased limit (backup) - only for non-multipart
app.use((req, res, next) => {
  const contentType = req.get('Content-Type') || '';
  if (!contentType.includes('multipart/form-data')) {
    bodyParser.json({limit: '10mb'})(req, res, next);
  } else {
    next();
  }
});

app.use((req, res, next) => {
  const contentType = req.get('Content-Type') || '';
  if (!contentType.includes('multipart/form-data')) {
    bodyParser.urlencoded({limit: '10mb', extended: true})(req, res, next);
  } else {
    next();
  }
});

// Setup todo routes
todoModule.setupRoutes(app);

// Mount new v1 routes
app.use('/api/v1/visits', visitsRoutesV1); // visitsRoutesV1 is now imported at the top
app.use('/api/v1/games', offlinegamesRoutesV1);   // gamesRoutesV1 is now imported at the top
app.use('/api/v1/chat', chatRouterV1);     // Use the imported router object
app.use('/api/hosty', hostyRoutes); // Hosty static site hosting API
app.use('/api/v1/ndyt-activities', ndytActivitiesRoutesV1);

// Serve the built ndyt-activities app at /ndyt endpoint
app.use('/ndyt', express.static(path.join(__dirname, 'ndyt/dist'), {
  index: 'index.html',
  setHeaders: (res, path) => {
    res.set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
    res.set('Pragma', 'no-cache');
    res.set('Expires', '0');
    res.set('X-Content-Type-Options', 'nosniff');
    res.set('X-Frame-Options', 'DENY');
    res.set('X-XSS-Protection', '1; mode=block');
  }
}));

// Handle Vue Router routes - serve index.html for any /ndyt/* routes that don't match files
app.get('/ndyt/*', (req, res) => {
  res.sendFile(path.join(__dirname, 'ndyt-activities/dist/index.html'));
});

// Import hosty links middleware
const { createHostyLinksMiddleware } = require('./routes/hosty-links');

// Serve the React app at /hosty endpoint
app.use('/hosty', express.static(path.join(__dirname, 'dist'), {
  index: 'index.html',
  setHeaders: (res, path) => {
    res.set('X-Content-Type-Options', 'nosniff');
    res.set('X-Frame-Options', 'DENY');
    res.set('X-XSS-Protection', '1; mode=block');
  }
}));

// Handle React Router routes - serve index.html for any /hosty/* routes that don't match files
app.get('/hosty/*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Direct hosting for sites - serve files directly from hosted_sites directory
app.use('/hosted/:sitename', createHostyLinksMiddleware(pool));
app.use('/hosted/:sitename/*', createHostyLinksMiddleware(pool));

// Create an HTTP server instance from the Express app for WebSocket compatibility
const server = http.createServer(app);

// Configure server timeouts for better WebSocket handling in production
server.timeout = 0; // Disable HTTP timeout for WebSocket connections
server.keepAliveTimeout = 65000; // Keep connections alive longer
server.headersTimeout = 66000; // Headers timeout slightly higher than keepAlive

// Initialize monitor
initializeMonitor(server, app);

// Initialize WebSocket for Offline Games Chat from chat-v1.js
initializeOfflineGamesChatWebSocket(server);

// Serve static files securely with development-friendly caching
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '0', // Disable caching in development
  setHeaders: (res, path) => {
    // Prevent directory listing
    if (fs.statSync(path).isDirectory()) {
      return res.status(403).end('403 Forbidden');
    }

    // Add security headers
    res.set('X-Content-Type-Options', 'nosniff');
    res.set('X-Frame-Options', 'DENY');
    res.set('X-XSS-Protection', '1; mode=block');

    // Development-friendly cache control
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  },
  // Deny access to sensitive files
  index: false,
  dotfiles: 'deny'
}));

// Middleware to protect sensitive files
app.use((req, res, next) => {
  const blockedPaths = ['.config', '.env', '.git', '.env.local', 'configcred'];
  const requestPath = req.path.toLowerCase();
  
  // Allow React app assets from /hosty/assets/
  if (requestPath.startsWith('/hosty/assets/')) {
    return next();
  }
  
  // Block sensitive files but allow legitimate JS/JSON from public directories
  if (blockedPaths.some(ext => requestPath.includes(ext)) && !requestPath.startsWith('/api/')) {
    return res.status(403).send('Access Denied');
  }
  
  // Block direct access to backend JS files (but not frontend assets or hosted sites)
  if (requestPath.endsWith('.js') && !requestPath.startsWith('/hosty/') && !requestPath.startsWith('/api/') && !requestPath.startsWith('/js/') && !requestPath.startsWith('/hosted/')) {
    return res.status(403).send('Access Denied');
  }
  
  // Block direct access to backend JSON files (but not API responses or hosted sites)
  if (requestPath.endsWith('.json') && !requestPath.startsWith('/api/') && !requestPath.startsWith('/hosted/')) {
    return res.status(403).send('Access Denied');
  }
  
  // Continue for safe paths
  next();
});

// Add cache-busting middleware
app.use((req, res, next) => {
  // Add timestamp as version for cache busting
  res.locals.version = Date.now();
  next();
});

// Routes
// Root route - serve index page
app.get('/', (req, res) => {
  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Page routes
app.get('/:page', (req, res, next) => {
  const page = req.params.page;
  
  // Skip API routes and let them be handled by their own handlers
  if (page.startsWith('api')) {
    return next();
  }
  
  const filePath = path.join(__dirname, 'public', `${page}.html`);
  
  // Check if the file exists before trying to serve it
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File doesn't exist, pass to the next middleware (eventually the 404 handler)
      return next();
    }
    // File exists, serve it
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error(`Error serving ${filePath}:`, err);
        next(err); // Pass errors to Express
      }
    });
  });
});

// Routes
app.get('/api/games', async (req, res) => {
    try {
      const result = await pool.query('SELECT * FROM games');
      res.json(result.rows);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
    }
  });
  // Route to edit a game
app.put('/api/games/:gameId', async (req, res) => {
  const { gameId } = req.params;
  const { title, imageUrl } = req.body;

  try {
      const result = await pool.query(
          'UPDATE games SET title = $1, imageurl = $2 WHERE id = $3 RETURNING *',
          [title, imageUrl, gameId]
      );
      res.status(200).json(result.rows[0]);
  } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
  }
});
// Get all game accounts with dislikes
app.get('/api/disliked-accounts', async (req, res) => {
  try {
    const result = await pool.query(
      `SELECT ga.id, ga.email, ga.password, ga.dislikes, g.title, g.imageurl, ga.game_id
       FROM public.game_accounts ga
       JOIN public.games g ON ga.game_id = g.id
       WHERE ga.dislikes > 0;`
    );
    res.json(result.rows);
  } catch (err) {
    res.status(500).send('Server Error');
  }
});
// get all games account only
app.get('/api/game-accounts', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM game_accounts');
    res.json(result.rows);
  } catch (err) {
    res.status(500).send('Server Error');
  }
});
app.delete('/api/deletegame/:id', async (req, res) => {
    try {
        const { password } = req.query;
        const gameId = req.params.id;

        if (!password) {
            return res.status(401).json({ message: 'No password provided' });
        }

        // Verify password
        if (String(password) !== String(ADMIN_PASSWORD)) {
            return res.status(401).json({ message: 'Incorrect password' });
        }

        // Delete associated accounts first
        await pool.query('DELETE FROM game_accounts WHERE game_id = $1', [gameId]);
        
        // Then delete the game
        const result = await pool.query('DELETE FROM games WHERE id = $1', [gameId]);

        if (result.rowCount === 0) {
            return res.status(404).json({ message: 'Game not found' });
        }

        return res.status(200).json({ message: 'Game deleted successfully' });
    } catch (error) {
        console.error('Error deleting game:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
});
  app.post('/api/send-message', (req, res) => {
    const newMessage = req.body.message;
    const chatFilePath = path.join(__dirname, 'offline.json');
    
    // Validate the message
    if (!newMessage || typeof newMessage !== 'string') {
        console.error('Invalid message format:', newMessage);
        return res.status(400).json({ error: 'Invalid message format' });
    }

    // Read existing chat messages
    fs.readFile(chatFilePath, 'utf8', (err, data) => {
        if (err) {
            console.error('Error reading chat messages:', err);
            return res.status(500).json({ error: 'Unable to read the messages' });
        }
        let messages = [];
        try {
            messages = JSON.parse(data).messages || [];
        } catch (parseError) {
            console.error('Error parsing chat messages:', parseError);
            return res.status(500).json({ error: 'Error parsing chat messages' });
        }

        // Add the new message
        messages.push({ text: newMessage });

        // Write updated messages back to the file
        fs.writeFile(chatFilePath, JSON.stringify({ messages: messages }, null, 2), (err) => {
            if (err) {
                console.error('Error writing chat messages:', err);
                return res.status(500).json({ error: 'Unable to send the message' });
            }
            res.json({ success: true });
        });
    });
});
app.get('/api/get-messages', (req, res) => {
    const chatFilePath = path.join(__dirname, 'offline.json');
  
    fs.readFile(chatFilePath, 'utf8', (err, data) => {
      if (err) {
        console.error('Error reading chat messages:', err);
        return res.status(500).json({ error: 'Unable to read the messages' });
      }
      let messages = [];
      try {
        messages = JSON.parse(data).messages || [];
      } catch (parseError) {
        console.error('Error parsing chat messages:', parseError);
        return res.status(500).json({ error: 'Error parsing chat messages' });
      }
  
      res.json({ messages: messages });
    });
  });
  app.post('/api/games', async (req, res) => {
    const { title, imageUrl } = req.body;
    try {
      const result = await pool.query(
        'INSERT INTO games (title, imageUrl) VALUES ($1, $2) RETURNING *',
        [title, imageUrl]
      );
      
      // Send notification to all connected clients
      await sendFcmNotification(
        'New Game Added',
        `${title} has been added to the library!`,
        '/games'
      );
      
      res.status(201).json(result.rows[0]);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
    }
  });

  // Route to get accounts for a specific game
app.get('/api/games/:gameId/accounts', async (req, res) => {
    const { gameId } = req.params;
    try {
      const result = await pool.query(
        'SELECT * FROM game_accounts WHERE game_id = $1',
        [gameId]
      );
      res.json(result.rows);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
    }
  });
  app.post('/register-token', (req, res) => {
    const { token } = req.body;
  
    // Store the token in your database
    pool.query('INSERT INTO fcm_tokens (token) VALUES ($1)', [token], (err, result) => {
      if (err) {
        console.error(err);
        return res.status(500).send('Error storing token');
      }
      res.status(200).send('Token stored successfully');
    });
  });
  
  // Route to add an account for a specific game
app.post('/api/games/:gameId/accounts', async (req, res) => {
    const { gameId } = req.params;
    const { email, password } = req.body;
  
    try {
      const result = await pool.query(
        'INSERT INTO game_accounts (game_id, email, password) VALUES ($1, $2, $3) RETURNING *',
        [gameId, email, password]

      );
    //   const fcmTokens = await pool.query('SELECT token FROM fcm_tokens');
    
    // const tokens = fcmTokens.rows.map(row => row.token); // Get all tokens
    
    // const message = {
    //   notification: {
    //     title: 'New Game Account Added',
    //     body: 'A new account has been added for the game.',
    //   },
    //   tokens: tokens, // Send to all tokens
    // };
  
    //   // Send push notification using Firebase Admin SDK
    //   admin.messaging().send(message)
      res.status(201).json(result.rows[0]);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
    }
  });

  // Route to edit an account for a specific game
app.put('/api/games/:gameId/accounts/:accountId', async (req, res) => {
    const { gameId, accountId } = req.params;
    const { email, password } = req.body;
  
    try {
      const result = await pool.query(
        'UPDATE game_accounts SET email = $1, password = $2 WHERE id = $3 AND game_id = $4 RETURNING *',
        [email, password, accountId, gameId]
      );
      res.status(200).json(result.rows[0]);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server error');
    }
  });
  
  // Route to delete an account for a specific game
  app.delete('/api/games/:gameId/accounts/:accountId/delete', async (req, res) => {
    try {
        const { password } = req.query;
        const { gameId, accountId } = req.params;

        if (!password) {
            return res.status(401).json({ message: 'No password provided' });
        }

        // Verify password
        if (String(password) !== String(ADMIN_PASSWORD)) {
            return res.status(401).json({ message: 'Incorrect password' });
        }

        // Delete the account
        const result = await pool.query(
            'DELETE FROM game_accounts WHERE id = $1 AND game_id = $2',
            [accountId, gameId]
        );

        if (result.rowCount === 0) {
            return res.status(404).json({ message: 'Account not found' });
        }

        return res.status(200).json({ message: 'Account deleted successfully' });
    } catch (error) {
        console.error('Error deleting account:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
});
// Update likes or dislikes
app.post('/api/accounts/:id/:type', async (req, res) => {
  const { id, type } = req.params;

  let query;
  if (type === 'like') {
      query = `UPDATE game_accounts SET likes = likes + 1 WHERE id = $1 RETURNING *`;
  } else if (type === 'dislike') {
      query = `UPDATE game_accounts SET dislikes = dislikes + 1 WHERE id = $1 RETURNING *`;
  } else {
      return res.status(400).json({ error: 'Invalid type' });
  }

  try {
      const result = await pool.query(query, [id]);
      res.json(result.rows[0]);
  } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed to update likes/dislikes' });
  }
});

app.get('/api/visit-count', (req, res) => {
    try {
      const data = fs.readFileSync('visits.json', 'utf8');
      const visits = JSON.parse(data);
      res.send(visits.count.toString());
    } catch (error) {
      console.error('Error reading visits.json:', error);
      res.status(500).send('Error reading visit count');
    }
  });
  
  // Route to update visit count
  app.put('/api/visit-count', (req, res) => {
    try {
      const data = fs.readFileSync('visits.json', 'utf8');
      const visits = JSON.parse(data);
      visits.count += 1;
      fs.writeFileSync('visits.json', JSON.stringify(visits));
      res.send(visits.count.toString());
    } catch (error) {
      console.error('Error updating visits.json:', error);
      res.status(500).send('Error updating visit count');
    }
  });
// Logging middleware to debug routes
// app.use((req, res, next) => {
//     console.log(`Received ${req.method} request for ${req.url}`);
//     next();
// });

app.get('/api/chat', (req, res) => {
    const chatFilePath = path.join(__dirname, 'chat','chat.json'); // Update the path
    fs.readFile(chatFilePath, 'utf8', (err, data) => {
        if (err) {
            console.error('Error reading chat messages:', err);
            return res.status(500).json({ error: 'Unable to read the messages' });
        }
        res.json(JSON.parse(data));
    });
});

// Route to add chat messages
app.post('/api/chat', (req, res) => {
    const newMessage = req.body.message;
    const chatFilePath = path.join(__dirname, 'chat', 'chat.json');
    
    // Validate the message
    if (!newMessage || typeof newMessage !== 'string') {
        console.error('Invalid message format:', newMessage);
        return res.status(400).json({ error: 'Invalid message format' });
    }

    // Read existing chat messages
    fs.readFile(chatFilePath, 'utf8', (err, data) => {
        if (err) {
            console.error('Error reading chat messages:', err);
            return res.status(500).json({ error: 'Unable to read the messages' });
        }
        let messages = [];
        try {
            messages = JSON.parse(data).messages || [];
        } catch (parseError) {
            console.error('Error parsing chat messages:', parseError);
            return res.status(500).json({ error: 'Error parsing chat messages' });
        }

        // Add the new message
        messages.push({ text: newMessage });

        // Write updated messages back to the file
        fs.writeFile(chatFilePath, JSON.stringify({ messages: messages }, null, 2), (err) => {
            if (err) {
                console.error('Error writing chat messages:', err);
                return res.status(500).json({ error: 'Unable to send the message' });
            }
            res.json({ success: true });
        });
    });
});

// Route to get the product count for each category
app.get('/api/product-count/:category', async (req, res) => {
    const { category } = req.params;

    try {
        const client = await pool.connect();
        const result = await client.query(`
            SELECT COUNT(*) as count
            FROM products
            WHERE product_type = $1
        `, [category]);

        const productCount = result.rows[0].count;
        res.json({ count: productCount });
        client.release();
    } catch (err) {
        console.error('Error fetching product count from database:', err);
        res.status(500).send('Error fetching product count');
    }
});


app.get('/api/all-products', async (req, res) => {
    try {
        const client = await pool.connect();
        const result = await client.query(`
            SELECT p.id, p.name, p.image_url, p.product_type, pr.store, pr.price
            FROM products p
            JOIN prices pr ON p.id = pr.product_id
        `);

        const products = result.rows.reduce((acc, row) => {
            const { id, name, image_url, product_type, store, price } = row;
            const product = acc.find(p => p.id === id);
            if (product) {
                product.prices.push({ store, price });
            } else {
                acc.push({ id, name, image_url, product_type, prices: [{ store, price }] });
            }
            return acc;
        }, []);

        res.json({ products });
        client.release();
    } catch (err) {
        console.error('Error fetching data from database:', err);
        res.status(500).send('Error fetching data');
    }
});

// Program Keys API Routes
app.get('/api/program', async (req, res) => {
  console.log('Received request for program step 1');
  try {
    const client = await pool.connect();
    const result = await client.query(
      'SELECT id, program_name, expire_date FROM program_licenses WHERE expire_date > NOW() ORDER BY RANDOM() LIMIT 1'
    );
    client.release();

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'No valid program keys available' });
    }

    // Generate a verification token that includes the program ID
    const verificationToken = jwt.sign(
      { 
        programId: result.rows[0].id,
        timestamp: Date.now() 
      },
      JWT_SECRET,
      { expiresIn: '5m' } // Token expires in 5 minutes
    );

    const response = {
      program_name: result.rows[0].program_name,
      expire_date: result.rows[0].expire_date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      verification_token: verificationToken
    };
    console.log('Sending initial response:', response);
    res.json(response);
  } catch (err) {
    console.error('Error fetching program:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Step 2: Verify ad and get program key
app.post('/api/verify-ad', async (req, res) => {
  const { verificationToken } = req.body;
  console.log('Received verification token:', verificationToken);
  
  if (!verificationToken) {
    return res.status(400).json({ error: 'Verification token is required' });
  }

  try {
    // Verify the token
    const decoded = jwt.verify(verificationToken, JWT_SECRET);
    
    // Get the program key using the ID from the token
    const client = await pool.connect();
    const result = await client.query(
      'SELECT program_key FROM program_licenses WHERE id = $1',
      [decoded.programId]
    );
    client.release();

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Program not found' });
    }

    // Return the program key only after successful verification
    res.json({ 
      program_key: result.rows[0].program_key,
      success: true 
    });
  } catch (err) {
    console.error('Error during verification:', err);
    if (err.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid verification token' });
    }
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Verification token has expired' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Generic route for other API endpoints - should be AFTER specific routes
app.get('/api/:type', async (req, res) => {
    const { type } = req.params;

    try {
        const client = await pool.connect();
        const result = await client.query(`
            SELECT p.id, p.name, p.image_url, pr.store, pr.price
            FROM products p
            JOIN prices pr ON p.id = pr.product_id
            WHERE p.product_type = $1
        `, [type]);

        const products = result.rows.reduce((acc, row) => {
            const { id, name, image_url, store, price } = row;
            const product = acc.find(p => p.id === id);
            if (product) {
                product.prices.push({ store, price });
            } else {
                acc.push({ id, name, image_url, prices: [{ store, price }] });
            }
            return acc;
        }, []);

        res.json({ products });
        client.release();
    } catch (err) {
        console.error('Error fetching data from database:', err);
        res.status(500).send('Error fetching data');
    }
});

// Route to post data
app.post('/api/products', async (req, res) => {
    const { name, image, prices, product_type } = req.body;

    try {
        const client = await pool.connect();

        // Check if the product already exists
        const checkProductQuery = `
            SELECT id FROM products
            WHERE name = $1 AND image_url = $2 AND product_type = $3
        `;
        const checkProductResult = await client.query(checkProductQuery, [name, image, product_type]);

        let productId;
        if (checkProductResult.rows.length > 0) {
            // Product exists, get the product ID
            productId = checkProductResult.rows[0].id;
        } else {
            // Product does not exist, insert the product
            const insertProductQuery = `
                INSERT INTO products (name, image_url, product_type)
                VALUES ($1, $2, $3) RETURNING id
            `;
            const productResult = await client.query(insertProductQuery, [name, image, product_type]);
            productId = productResult.rows[0].id;
        }

        // Insert prices into the prices table
        const insertPriceQuery = `
            INSERT INTO prices (product_id, store, price)
            VALUES ($1, $2, $3)
        `;
        for (const price of prices) {
            await client.query(insertPriceQuery, [productId, price.store, price.price]);
        }

        client.release();
        res.status(201).json({ id: productId, name, image, prices, product_type });
    } catch (err) {
        console.error('Error inserting data into database:', err);
        res.status(500).send('Error inserting data');
    }
});

app.put('/api/products', async (req, res) => {
    const { image, name, store, newPrice } = req.body;

    console.log('Received payload:', { image, name, store, newPrice }); // Log the received payload

    try {
        const client = await pool.connect();

        // Check if the product exists
        const checkProductQuery = `
            SELECT id FROM products
            WHERE image_url = $1 AND name = $2
        `;
        const checkProductResult = await client.query(checkProductQuery, [image, name]);

        console.log('Product query result:', checkProductResult.rows); // Log the product query result

        if (checkProductResult.rows.length === 0) {
            client.release();
            return res.status(404).json({ error: 'Product not found' });
        }

        const productId = checkProductResult.rows[0].id;

        // Check if the store exists for this product
        const checkStoreQuery = `
            SELECT id FROM prices
            WHERE product_id = $1 AND store = $2
        `;
        const checkStoreResult = await client.query(checkStoreQuery, [productId, store]);

        console.log('Store query result:', checkStoreResult.rows); // Log the store query result

        if (checkStoreResult.rows.length === 0) {
            client.release();
            return res.status(404).json({ error: 'Store not found' });
        }

        // Update the price for the specific store
        const updatePriceQuery = `
            UPDATE prices
            SET price = $1
            WHERE product_id = $2 AND store = $3
        `;
        await client.query(updatePriceQuery, [newPrice, productId, store]);

        client.release();
        res.status(200).json({ message: 'Price updated successfully' });
    } catch (err) {
        console.error('Error updating data in database:', err);
        res.status(500).send('Error updating data');
    }
});

// Express route for user login
app.post('/api/login', async (req, res) => {
    const { username, password } = req.body;

    try {
        // Check if the username exists in the database
        const user = await pool.query('SELECT * FROM accounts WHERE username = $1', [username]);
        if (user.rows.length === 0) {
            return res.status(401).json({ message: 'اسم المستخدم غير صحيح' });
        }

        // Compare the provided password with the hashed password stored in the database
        const isValidPassword = await bcrypt.compare(password, user.rows[0].password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ message: 'كلمة المرور غير صحيحة' });
        }

        // If authentication succeeds, generate a token (you can use JWT or session-based authentication)
        const token = generateToken(username); // You need to implement this function

        // Return the token to the client
        res.status(200).json({ token });
    } catch (err) {
        console.error('Error during login:', err);
        res.status(500).json({ message: 'حصل خطأ اثناء تسجيل الدخول' });
    }
});

// Express route for creating a new account
app.post('/api/create-account', async (req, res) => {
    const { username, password } = req.body;

    try {
        // Hash the password before storing it in the database
        const hashedPassword = await hashPassword(password);

        // Store the username and hashed password in the database
        const client = await pool.connect(); // Connect to the PostgreSQL database

        // Insert the username and hashed password into the 'accounts' table
        const insertQuery = `
            INSERT INTO accounts (username, password_hash)
            VALUES ($1, $2)
        `;
        await client.query(insertQuery, [username, hashedPassword]);

        client.release(); // Release the database connection

        // Return success response if account creation is successful
        res.status(200).json({ message: 'Account created successfully' });
    } catch (err) {
        console.error('Error creating account:', err);
        res.status(500).json({ error: 'Error creating account' });
    }
});

// Function to hash passwords
async function hashPassword(password) {
    const saltRounds = 10; // Number of salt rounds for bcrypt
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    return hashedPassword;
}

// Debt Management API Routes
app.post('/api/register', async (req, res) => {
  const { name, email, password, role, supervisorId } = req.body;

  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    const result = await pool.query(
      'INSERT INTO users (name, email, password_hash, role, supervisor_id) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [name, email, hashedPassword, role, supervisorId]
    );

    const user = result.rows[0];
    const token = generateToken(user.email);

    res.status(201).json({
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        supervisorId: user.supervisor_id,
        balance: parseFloat(user.balance)
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Failed to register user' });
  }
});

app.post('/api/debt/login', async (req, res) => {
  const { username, password } = req.body;

  try {
    const result = await pool.query(
      'SELECT * FROM users WHERE name = $1',
      [username]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const user = result.rows[0];
    const validPassword = await bcrypt.compare(password, user.password_hash);

    if (!validPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = generateToken(user.name);

    res.json({
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        supervisorId: user.supervisor_id,
        balance: parseFloat(user.balance)
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Failed to login' });
  }
});

app.post('/api/transactions', async (req, res) => {
  const { fromUserId, toUserId, supervisorId, amount, type, note } = req.body;

  try {
    const result = await pool.query(
      'INSERT INTO transactions (from_user_id, to_user_id, supervisor_id, amount, type, note) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [fromUserId, toUserId, supervisorId, amount, type, note]
    );

    const transaction = result.rows[0];
    res.status(201).json({
      id: transaction.id,
      fromUserId: transaction.from_user_id,
      toUserId: transaction.to_user_id,
      supervisorId: transaction.supervisor_id,
      amount: parseFloat(transaction.amount),
      type: transaction.type,
      status: transaction.status,
      note: transaction.note,
      timestamp: transaction.created_at
    });
  } catch (error) {
    console.error('Transaction creation error:', error);
    res.status(500).json({ error: 'Failed to create transaction' });
  }
});

app.patch('/api/transactions/:id', async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  try {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const transactionResult = await client.query(
        'UPDATE transactions SET status = $1 WHERE id = $2 RETURNING *',
        [status, id]
      );

      if (transactionResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Transaction not found' });
      }

      const transaction = transactionResult.rows[0];

      if (status === 'approved') {
        const amount = transaction.type === 'debt' ? transaction.amount : -transaction.amount;

        await client.query(
          'UPDATE users SET balance = balance - $1 WHERE id = $2',
          [amount, transaction.from_user_id]
        );

        await client.query(
          'UPDATE users SET balance = balance + $1 WHERE id = $2',
          [amount, transaction.to_user_id]
        );
      }

      await client.query('COMMIT');
      res.json({ success: true });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Transaction update error:', error);
    res.status(500).json({ error: 'Failed to update transaction' });
  }
});

app.get('/api/transactions/user/:userId', async (req, res) => {
  const { userId } = req.params;

  try {
    const result = await pool.query(
      'SELECT * FROM transactions WHERE from_user_id = $1 OR to_user_id = $1 ORDER BY created_at DESC',
      [userId]
    );

    const transactions = result.rows.map(row => ({
      id: row.id,
      fromUserId: row.from_user_id,
      toUserId: row.to_user_id,
      supervisorId: row.supervisor_id,
      amount: parseFloat(row.amount),
      type: row.type,
      status: row.status,
      note: row.note,
      timestamp: row.created_at
    }));

    res.json(transactions);
  } catch (error) {
    console.error('Transaction fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch transactions' });
  }
});

app.get('/api/transactions/supervisor/:supervisorId', async (req, res) => {
  const { supervisorId } = req.params;

  try {
    const result = await pool.query(
      'SELECT * FROM transactions WHERE supervisor_id = $1 ORDER BY created_at DESC',
      [supervisorId]
    );

    const transactions = result.rows.map(row => ({
      id: row.id,
      fromUserId: row.from_user_id,
      toUserId: row.to_user_id,
      supervisorId: row.supervisor_id,
      amount: parseFloat(row.amount),
      type: row.type,
      status: row.status,
      note: row.note,
      timestamp: row.created_at
    }));

    res.json(transactions);
  } catch (error) {
    console.error('Transaction fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch transactions' });
  }
});

app.get('/api/user', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM users WHERE email = $1',
      [req.user.username]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = result.rows[0];
    res.json({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      supervisorId: user.supervisor_id,
      balance: parseFloat(user.balance)
    });
  } catch (error) {
    console.error('User fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = decoded;
    next();
  });
}

// Store FCM tokens
const fcmTokens = new Set();

// Route to update FCM token
app.post('/api/update-fcm-token', async (req, res) => {
  try {
    const { token } = req.body;
    if (!token) {
      return res.status(400).json({ error: 'Token is required' });
    }
    fcmTokens.add(token); // For compatibility with older versions
    fcmNotificationsV1.addFcmToken(token); // For new v1 module
    console.log('FCM token updated (both versions):', token);
    res.status(200).json({ message: 'Token updated successfully' });
  } catch (error) {
    console.error('Error updating FCM token:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Function to send FCM notification
async function sendFcmNotification(title, body, route = null) {
  const message = {
    token: null, // Will be set in the loop
    notification: {
      title,
      body,
    },
    android: {
      notification: {
        channelId: 'game_updates',
        priority: 'high',
        defaultSound: true,
        defaultVibrateTimings: true,
      }
    },
    data: {
      route: route || '',
      click_action: 'FLUTTER_NOTIFICATION_CLICK'
    }
  };

  for (const token of fcmTokens) {
    try {
      message.token = token;
      await admin.messaging().send(message);
      console.log('Successfully sent message to token:', token);
    } catch (error) {
      console.error(`Error sending FCM notification to token ${token}:`, error);
      if (error.code === 'messaging/invalid-registration-token' ||
          error.code === 'messaging/registration-token-not-registered') {
        fcmTokens.delete(token);
      }
    }
  }
}

// Admin endpoint to add new program keys
app.post('/api/admin/program', authenticateToken, async (req, res) => {
  const { program_name, program_key, expire_date } = req.body;

  if (!program_name || !program_key || !expire_date) {
    return res.status(400).json({ error: 'All fields are required' });
  }

  try {
    const client = await pool.connect();
    const result = await client.query(
      'INSERT INTO program_licenses (program_name, program_key, expire_date) VALUES ($1, $2, $3) RETURNING *',
      [program_name, program_key, expire_date]
    );
    client.release();

    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error adding program:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all programs (admin only)
app.get('/api/admin/programs', authenticateToken, async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(
      'SELECT program_name, program_key, expire_date FROM program_licenses ORDER BY expire_date DESC'
    );
    client.release();

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching programs:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// Update the delete account endpoint to include password verification

// Technical Team Social Platform API Routes

// User Authentication and Management
app.post('/api/tech/register', async (req, res) => {
  const { username, password, pinCode } = req.body;
  
  try {
    // Verify the registration PIN matches the default team PIN
    if (pinCode !== DEFAULT_TEAM_PIN) {
      return res.status(401).json({ 
        error: 'رمز الفريق الرقمي غير صحيح. يرجى الاتصال بالمسؤول للحصول على الرمز الصحيح.' 
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    
    const result = await pool.query(
      'INSERT INTO ndt_users (username, password_hash, pin_code, rank) VALUES ($1, $2, $3, $4) RETURNING id, username, rank, avatar_url',
      [username, hashedPassword, DEFAULT_TEAM_PIN, 'member']
    );
    
    const token = generateToken(username);

    // Log new user registration (for admin)
    console.log(`New user registered: ${username} at ${new Date().toISOString()}`);
    
    res.status(201).json({
      token,
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Registration error:', error);
    if (error.code === '23505') { // PostgreSQL unique violation error code
      res.status(400).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to register user' });
    }
  }
});

app.post('/api/tech/login', async (req, res) => {
  const { username, password, pinCode } = req.body;
  
  try {
    const result = await pool.query(
      'SELECT * FROM ndt_users WHERE username = $1',
      [username]
    );
    
    if (result.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const user = result.rows[0];
    const validPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!validPassword || user.pin_code !== pinCode) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    await pool.query(
      'UPDATE ndt_users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );
    
    const token = generateToken(username);
    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        rank: user.rank,
        avatar_url: user.avatar_url
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Failed to login' });
  }
});

// Posts Management
app.post('/api/tech/posts', authenticateToken, async (req, res) => {
  const { title, content, images } = req.body;
  
  try {
    // Get user by username from the token
    const userResult = await pool.query(
      'SELECT id FROM ndt_users WHERE username = $1',
      [req.user.username]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userId = userResult.rows[0].id;
    
    const client = await pool.connect();
    await client.query('BEGIN');
    
    try {
      // Create post with user ID
      const postResult = await client.query(
        'INSERT INTO ndt_posts (user_id, title, content) VALUES ($1, $2, $3) RETURNING *',
        [userId, title, content]
      );
      
      // Add images if any
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          await client.query(
            'INSERT INTO ndt_post_images (post_id, image_url, image_order) VALUES ($1, $2, $3)',
            [postResult.rows[0].id, images[i], i]
          );
        }
      }
      
      await client.query('COMMIT');
      
      // Fetch complete post with images
      const completePost = await getCompletePost(postResult.rows[0].id);
      res.status(201).json(completePost);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating post:', error);
    res.status(500).json({ error: 'Failed to create post' });
  }
});

app.get('/api/tech/posts', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        p.*,
        json_agg(DISTINCT pi.*) AS images,
        json_agg(DISTINCT c.*) AS comments,
        COALESCE(AVG(r.stars), 0) as average_rating,
        COUNT(DISTINCT r.id) as rating_count
      FROM ndt_posts p
      LEFT JOIN ndt_post_images pi ON p.id = pi.post_id
      LEFT JOIN ndt_comments c ON p.id = c.post_id
      LEFT JOIN ndt_ratings r ON p.id = r.post_id
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching posts:', error);
    res.status(500).json({ error: 'Failed to fetch posts' });
  }
});

// Comments Management
app.post('/api/tech/posts/:postId/comments', authenticateToken, async (req, res) => {
  const { postId } = req.params;
  const { content } = req.body;
  
  try {
    const result = await pool.query(
      'INSERT INTO ndt_comments (post_id, user_id, content) VALUES ($1, $2, $3) RETURNING *',
      [postId, req.user.id, content]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating comment:', error);
    res.status(500).json({ error: 'Failed to create comment' });
  }
});

// Ratings Management
app.post('/api/tech/posts/:postId/rate', authenticateToken, async (req, res) => {
  const { postId } = req.params;
  const { stars } = req.body;
  
  try {
    const result = await pool.query(
      `INSERT INTO ndt_ratings (post_id, user_id, stars) 
       VALUES ($1, $2, $3) 
       ON CONFLICT (post_id, user_id) 
       DO UPDATE SET stars = $3
       RETURNING *`,
      [postId, req.user.id, stars]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error rating post:', error);
    res.status(500).json({ error: 'Failed to rate post' });
  }
});

// Helper function to get complete post data
async function getCompletePost(postId) {
  const result = await pool.query(`
    SELECT 
      p.*,
      json_agg(DISTINCT pi.*) AS images,
      json_agg(DISTINCT c.*) AS comments,
      COALESCE(AVG(r.stars), 0) as average_rating,
      COUNT(DISTINCT r.id) as rating_count
    FROM ndt_posts p
    LEFT JOIN ndt_post_images pi ON p.id = pi.post_id
    LEFT JOIN ndt_comments c ON p.id = c.post_id
    LEFT JOIN ndt_ratings r ON p.id = r.post_id
    WHERE p.id = $1
    GROUP BY p.id
  `, [postId]);
  
  return result.rows[0];
}

// Get default PIN from environment variables
const DEFAULT_TEAM_PIN = process.env.DEFAULT_TEAM_PIN;
if (!DEFAULT_TEAM_PIN) {
  console.error('DEFAULT_TEAM_PIN is not set in environment variables');
  process.exit(1);
}

// Add endpoint to change PIN code
app.post('/api/tech/change-pin', authenticateToken, async (req, res) => {
  const { currentPin, newPin } = req.body;
  
  try {
    // Get user by username from the token
    const userResult = await pool.query(
      'SELECT * FROM ndt_users WHERE username = $1',
      [req.user.username]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];

    // Verify current PIN
    if (user.pin_code !== currentPin) {
      return res.status(401).json({ error: 'Current PIN is incorrect' });
    }

    // Update PIN
    await pool.query(
      'UPDATE ndt_users SET pin_code = $1 WHERE username = $2',
      [newPin, req.user.username]
    );

    res.json({ message: 'PIN updated successfully' });
  } catch (error) {
    console.error('PIN change error:', error);
    res.status(500).json({ error: 'Failed to change PIN' });
  }
});

// Image Upload
app.post('/api/tech/upload-image', authenticateToken, async (req, res) => {
  const { image } = req.body;
  
  if (!image) {
    return res.status(400).json({ error: 'No image provided' });
  }

  try {
    // Remove the base64 prefix if present
    const base64Data = image.replace(/^data:image\/\w+;base64,/, '');
    
    // Generate a unique filename
    const filename = `${Date.now()}-${Math.round(Math.random() * 1E9)}.jpg`;
    const filepath = path.join(__dirname, 'public', 'uploads', filename);
    
    // Ensure uploads directory exists
    const uploadsDir = path.join(__dirname, 'public', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Save the image
    fs.writeFileSync(filepath, base64Data, 'base64');
    
    // Return the URL
    const imageUrl = `/uploads/${filename}`;
    res.status(201).json({ url: imageUrl });
  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({ error: 'Failed to upload image' });
  }
});

// Global error handler - catch all unhandled errors before they crash the server
app.use((err, req, res, next) => {
  console.error('Global error handler caught error:', err);
  console.error('Error stack:', err.stack);
  console.error('Request URL:', req.url);
  console.error('Request method:', req.method);
  
  // Handle Multer errors specifically
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({ error: 'File size exceeds limit' });
  }
  
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({ error: 'Unexpected file field' });
  }
  
  // Handle JSON parsing errors
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json({ error: 'Invalid JSON format' });
  }
  
  // Generic error response
  if (!res.headersSent) {
    res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
  }
});

// 404 Error Handler - Add this at the end of all routes
app.use((req, res, next) => {
  // Set status code to 404
  res.status(404);
  
  // Try to serve the 404.html page
  const notFoundPath = path.join(__dirname, 'public', '404.html');
  
  // Check if the 404.html file exists
  fs.access(notFoundPath, fs.constants.F_OK, (err) => {
    if (err) {
      // If 404.html doesn't exist, send a simple text response
      console.error('404.html not found:', err);
      return res.type('text').send('404 - Page Not Found');
    }
    
    // 404.html exists, serve it
    res.sendFile(notFoundPath, (err) => {
      if (err) {
        // If there's an error serving the file, send a simple text response
        console.error('Error serving 404.html:', err);
        return res.type('text').send('404 - Page Not Found');
      }
    });
  });
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Promise Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  console.error('Exception stack:', err.stack);
  // Don't exit the process, just log the error
});

// Start the HTTP server (which includes the Express app and WebSocket handling)
server.listen(port, () => {
  console.log(`HTTP Server is running on http://localhost:${port}`);
  console.log('WebSocket server is running on path: /ws');
});

// Create WebSocket server (with noServer: true to manually handle upgrades)
const wss = new WebSocket.Server({ 
  noServer: true
});

// Handle WebSocket upgrade requests for multiple paths
server.on('upgrade', (request, socket, head) => {
  const pathname = new URL(request.url, `http://${request.headers.host}`).pathname;
  
  // Add error handling for the socket
  socket.on('error', (err) => {
    console.error('WebSocket upgrade socket error:', err);
  });
  
  // Set socket timeout to prevent hanging connections
  socket.setTimeout(60000, () => {
    console.log('WebSocket upgrade timeout, destroying socket');
    socket.destroy();
  });
  
  if (pathname === '/ws') {
    wss.handleUpgrade(request, socket, head, (ws) => {
      wss.emit('connection', ws, request);
    });
  } else if (pathname === '/ws/offline-games-chat') {
    // Handle offline games chat WebSocket upgrade
    const chatWss = getWebSocketServer();
    if (chatWss) {
      chatWss.handleUpgrade(request, socket, head, (ws) => {
        chatWss.emit('connection', ws, request);
      });
    } else {
      console.error('Chat WebSocket server not available');
      socket.destroy();
    }
  } else {
    // For unmatched paths, destroy the socket to prevent hanging
    console.log('Unmatched WebSocket path:', pathname);
    socket.destroy();
  }
});

// Store connected clients
const connectedClients = new Set();

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log('Client connected from:', req.socket.remoteAddress);
  connectedClients.add(ws);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'NOTIFICATION',
    title: 'Connected',
    message: 'Connected to game notification service'
  }));

  // Handle incoming messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      if (data.type === 'PING') {
        ws.send(JSON.stringify({ type: 'PONG' }));
      }
    } catch (error) {
      console.error('Invalid message format:', error);
    }
  });

  ws.on('close', () => {
    console.log('Client disconnected');
    connectedClients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    connectedClients.delete(ws);
  });
});

// Function to broadcast notification to all connected clients
function broadcastNotification(title, message, route = null) {
  const notification = {
    type: 'NOTIFICATION',
    title: title,
    message: message,
    route: route
  };

  const messageStr = JSON.stringify(notification);
  console.log('Broadcasting:', messageStr);
  
  connectedClients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// Keep WebSocket connections alive
setInterval(() => {
  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.ping();
    }
  });
}, 30000);