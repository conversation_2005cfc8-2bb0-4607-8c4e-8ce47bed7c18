{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"content-wrapper\"\n};\nconst _hoisted_2 = {\n  class: \"auth-container\"\n};\nconst _hoisted_3 = {\n  class: \"card\"\n};\nconst _hoisted_4 = {\n  class: \"card-body\"\n};\nconst _hoisted_5 = {\n  class: \"form-row\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = {\n  class: \"form-group\"\n};\nconst _hoisted_8 = {\n  class: \"form-group\"\n};\nconst _hoisted_9 = [\"value\"];\nconst _hoisted_10 = {\n  class: \"form-row\"\n};\nconst _hoisted_11 = {\n  class: \"form-group\"\n};\nconst _hoisted_12 = {\n  class: \"form-group\"\n};\nconst _hoisted_13 = [\"disabled\"];\nconst _hoisted_14 = {\n  key: 0,\n  class: \"loading-spinner\",\n  width: \"20\",\n  height: \"20\",\n  viewBox: \"0 0 24 24\"\n};\nconst _hoisted_15 = {\n  key: 1\n};\nconst _hoisted_16 = {\n  key: 2\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_18 = {\n  class: \"auth-footer\"\n};\nconst _hoisted_19 = {\n  class: \"auth-hint\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_cache[17] || (_cache[17] = _createStaticVNode(\"<div class=\\\"navbar\\\" data-v-03589122><div class=\\\"navbar-content\\\" data-v-03589122><div class=\\\"navbar-brand\\\" data-v-03589122><span class=\\\"navbar-text\\\" data-v-03589122>المجلس الأعلى للشباب</span><img class=\\\"navbar-logo\\\" src=\\\"\" + _imports_0 + \"\\\" alt=\\\"شعار الفريق\\\" data-v-03589122><span class=\\\"navbar-text\\\" data-v-03589122>الفريق الوطني للشباب الرقمي</span></div></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"card-title\"\n  }, \"إنشاء حساب جديد\"), _createElementVNode(\"p\", {\n    class: \"card-subtitle\"\n  }, \"انضم إلى الفريق الوطني للشباب الرقمي\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"form\", {\n    onSubmit: _cache[5] || (_cache[5] = _withModifiers((...args) => $options.doRegister && $options.doRegister(...args), [\"prevent\"])),\n    class: \"auth-form\"\n  }, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"username\"\n  }, \"اسم المستخدم\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    type: \"text\",\n    class: \"form-input\",\n    placeholder: \"ادخل اسم المستخدم\",\n    required: \"\",\n    autocomplete: \"username\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]])]), _createElementVNode(\"div\", _hoisted_7, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"full_name\"\n  }, \"الاسم الكامل\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"full_name\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.full_name = $event),\n    type: \"text\",\n    class: \"form-input\",\n    placeholder: \"ادخل الاسم الكامل\",\n    required: \"\",\n    autocomplete: \"name\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.full_name]])])]), _createElementVNode(\"div\", _hoisted_8, [_cache[9] || (_cache[9] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"governorate\"\n  }, \"المحافظة\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    id: \"governorate\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.governorate = $event),\n    class: \"form-select\",\n    required: \"\"\n  }, [_cache[8] || (_cache[8] = _createElementVNode(\"option\", {\n    value: \"\",\n    disabled: \"\"\n  }, \"اختر المحافظة\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.governorates, gov => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: gov,\n      value: gov\n    }, _toDisplayString(gov), 9 /* TEXT, PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.governorate]])]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[10] || (_cache[10] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"password\"\n  }, \"كلمة المرور\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.password = $event),\n    type: \"password\",\n    class: \"form-input\",\n    placeholder: \"ادخل كلمة المرور\",\n    required: \"\",\n    autocomplete: \"new-password\",\n    minlength: \"6\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]])]), _createElementVNode(\"div\", _hoisted_12, [_cache[11] || (_cache[11] = _createElementVNode(\"label\", {\n    class: \"form-label\",\n    for: \"team_pin\"\n  }, \"رمز الفريق الرقمي\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"team_pin\",\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.team_pin = $event),\n    type: \"text\",\n    class: \"form-input\",\n    placeholder: \"ادخل رمز الفريق الرقمي\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.team_pin]])])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.busy,\n    class: \"btn btn-primary btn-lg auth-submit\"\n  }, [$data.busy ? (_openBlock(), _createElementBlock(\"svg\", _hoisted_14, [...(_cache[12] || (_cache[12] = [_createElementVNode(\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    fill: \"none\",\n    \"stroke-dasharray\": \"31.416\",\n    \"stroke-dashoffset\": \"31.416\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"stroke-dasharray\",\n    dur: \"2s\",\n    values: \"0 31.416;15.708 15.708;0 31.416\",\n    repeatCount: \"indefinite\"\n  }), _createElementVNode(\"animate\", {\n    attributeName: \"stroke-dashoffset\",\n    dur: \"2s\",\n    values: \"0;-15.708;-31.416\",\n    repeatCount: \"indefinite\"\n  })], -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), !$data.busy ? (_openBlock(), _createElementBlock(\"span\", _hoisted_15, \"إنشاء الحساب\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_16, \"جاري إنشاء الحساب...\"))], 8 /* PROPS */, _hoisted_13), $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_cache[13] || (_cache[13] = _createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n  })], -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.error), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"p\", _hoisted_19, [_cache[15] || (_cache[15] = _createTextVNode(\" لديك حساب بالفعل؟ \", -1 /* CACHED */)), _createVNode(_component_router_link, {\n    to: \"/login\",\n    class: \"auth-link\"\n  }, {\n    default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\"سجّل الدخول\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  })])])], 32 /* NEED_HYDRATION */)])])])])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "width", "height", "viewBox", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onSubmit", "_cache", "_withModifiers", "args", "$options", "doRegister", "_hoisted_5", "_hoisted_6", "for", "id", "$data", "username", "$event", "type", "placeholder", "required", "autocomplete", "_hoisted_7", "full_name", "_hoisted_8", "governorate", "value", "disabled", "_createElementBlock", "_Fragment", "_renderList", "governorates", "gov", "key", "_hoisted_9", "_hoisted_10", "_hoisted_11", "password", "minlength", "_hoisted_12", "team_pin", "busy", "_hoisted_14", "cx", "cy", "r", "stroke", "fill", "attributeName", "dur", "values", "repeatCount", "_hoisted_15", "_hoisted_16", "error", "_hoisted_17", "d", "_toDisplayString", "_hoisted_18", "_hoisted_19", "_createVNode", "_component_router_link", "to"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\RegisterView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'RegisterView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      full_name: '',\r\n      password: '',\r\n      team_pin: '',\r\n      governorate: '',\r\n      busy: false,\r\n      error: '',\r\n      governorates: [\r\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\r\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\r\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    async doRegister() {\r\n      this.error = '';\r\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\r\n        // persist auth and team pin\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-brand\">\r\n        <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n        <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"content-wrapper\">\r\n    <div class=\"auth-container\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">إنشاء حساب جديد</h1>\r\n          <p class=\"card-subtitle\">انضم إلى الفريق الوطني للشباب الرقمي</p>\r\n        </div>\r\n\r\n        <div class=\"card-body\">\r\n          <form @submit.prevent=\"doRegister\" class=\"auth-form\">\r\n            <div class=\"form-row\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"username\">اسم المستخدم</label>\r\n                <input\r\n                  id=\"username\"\r\n                  v-model=\"username\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل اسم المستخدم\"\r\n                  required\r\n                  autocomplete=\"username\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"full_name\">الاسم الكامل</label>\r\n                <input\r\n                  id=\"full_name\"\r\n                  v-model=\"full_name\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل الاسم الكامل\"\r\n                  required\r\n                  autocomplete=\"name\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"governorate\">المحافظة</label>\r\n              <select\r\n                id=\"governorate\"\r\n                v-model=\"governorate\"\r\n                class=\"form-select\"\r\n                required\r\n              >\r\n                <option value=\"\" disabled>اختر المحافظة</option>\r\n                <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div class=\"form-row\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"password\">كلمة المرور</label>\r\n                <input\r\n                  id=\"password\"\r\n                  v-model=\"password\"\r\n                  type=\"password\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل كلمة المرور\"\r\n                  required\r\n                  autocomplete=\"new-password\"\r\n                  minlength=\"6\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"team_pin\">رمز الفريق الرقمي</label>\r\n                <input\r\n                  id=\"team_pin\"\r\n                  v-model=\"team_pin\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل رمز الفريق الرقمي\"\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <button type=\"submit\" :disabled=\"busy\" class=\"btn btn-primary btn-lg auth-submit\">\r\n              <svg v-if=\"busy\" class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\r\n                  <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\r\n                  <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\r\n                </circle>\r\n              </svg>\r\n              <span v-if=\"!busy\">إنشاء الحساب</span>\r\n              <span v-else>جاري إنشاء الحساب...</span>\r\n            </button>\r\n\r\n            <div v-if=\"error\" class=\"error-message\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n              </svg>\r\n              {{ error }}\r\n            </div>\r\n\r\n            <div class=\"auth-footer\">\r\n              <p class=\"auth-hint\">\r\n                لديك حساب بالفعل؟\r\n                <router-link to=\"/login\" class=\"auth-link\">سجّل الدخول</router-link>\r\n              </p>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n.navbar {\r\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n  border: 2px solid #3a3a5e;\r\n  border-radius: 12px;\r\n  margin: 16px;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navbar-content {\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.navbar-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.org-name, .team-name {\r\n  font-weight: 700;\r\n  font-size: clamp(16px, 3vw, 24px);\r\n  color: #f5f5f5;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n}\r\n\r\n.logo {\r\n  height: clamp(50px, 8vw, 70px);\r\n  width: auto;\r\n  border: 2px solid #4a5568;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.auth-wrapper {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #101214, #1b1f24);\r\n    padding: 16px;\r\n}\r\n\r\n.card {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(20px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 20px;\r\n    padding: 32px;\r\n    width: 100%;\r\n    max-width: 420px;\r\n    color: #e5e7eb;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    transition: all 0.3s ease;\r\n    direction: rtl;\r\n}\r\n\r\n.card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.card h1 {\r\n    font-size: 28px;\r\n    margin: 0 0 32px;\r\n    text-align: center;\r\n    font-weight: 700;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.field {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin: 20px 0;\r\n}\r\n\r\n.field label {\r\n    font-size: 14px;\r\n    color: #cbd5e1;\r\n    font-weight: 600;\r\n    text-align: right;\r\n}\r\n\r\ninput {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n}\r\n\r\ninput:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\ninput::placeholder {\r\n    color: #cbd5e1;\r\n    text-align: right;\r\n    opacity: 1;\r\n}\r\n\r\n.governorate-select {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    cursor: pointer;\r\n}\r\n\r\n.governorate-select:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\n.governorate-select option {\r\n    background: #1b1f24;\r\n    color: #e5e7eb;\r\n    padding: 8px;\r\n}\r\n\r\nbutton {\r\n    width: 100%;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: white;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n    margin-top: 8px;\r\n}\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.error {\r\n    color: #ef4444;\r\n    margin-top: 10px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint {\r\n    margin-top: 24px;\r\n    color: #cbd5e1;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint a {\r\n    color: #4f46e5;\r\n    text-decoration: none;\r\n    font-weight: 600;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.hint a:hover {\r\n    color: #6366f1;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 24px;\r\n        margin: 16px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 24px;\r\n        margin-bottom: 24px;\r\n    }\r\n    \r\n    input {\r\n        padding: 12px 14px;\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n    \r\n    button {\r\n        padding: 12px;\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 16px 0;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .auth-wrapper {\r\n        padding: 12px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 22px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 14px 0;\r\n    }\r\n}\r\n</style>"], "mappings": ";OAuDiCA,UAA6B;;EAMvDC,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAM;;EAMVA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAY;;EAcpBA,KAAK,EAAC;AAAY;;;EAalBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAclBA,KAAK,EAAC;AAAY;;;;EAcNA,KAAK,EAAC,iBAAiB;EAACC,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;;;;;;;EAUxDH,KAAK,EAAC;;;EAOnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAW;;;sfAlGhCI,mBAAA,CA2GM,OA3GNC,UA2GM,GA1GJD,mBAAA,CAyGM,OAzGNE,UAyGM,GAxGJF,mBAAA,CAuGM,OAvGNG,UAuGM,G,4BAtGJH,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA2C;IAAvCJ,KAAK,EAAC;EAAY,GAAC,iBAAe,GACtCI,mBAAA,CAAiE;IAA9DJ,KAAK,EAAC;EAAe,GAAC,sCAAoC,E,qBAG/DI,mBAAA,CAgGM,OAhGNI,UAgGM,GA/FJJ,mBAAA,CA8FO;IA9FAK,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;IAAEZ,KAAK,EAAC;MACvCI,mBAAA,CA0BM,OA1BNW,UA0BM,GAzBJX,mBAAA,CAWM,OAXNY,UAWM,G,0BAVJZ,mBAAA,CAA6D;IAAtDJ,KAAK,EAAC,YAAY;IAACiB,GAAG,EAAC;KAAW,cAAY,qB,gBACrDb,mBAAA,CAQE;IAPAc,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXtB,KAAK,EAAC,YAAY;IAClBuB,WAAW,EAAC,mBAAmB;IAC/BC,QAAQ,EAAR,EAAQ;IACRC,YAAY,EAAC;iDALJN,KAAA,CAAAC,QAAQ,E,KASrBhB,mBAAA,CAWM,OAXNsB,UAWM,G,0BAVJtB,mBAAA,CAA8D;IAAvDJ,KAAK,EAAC,YAAY;IAACiB,GAAG,EAAC;KAAY,cAAY,qB,gBACtDb,mBAAA,CAQE;IAPAc,EAAE,EAAC,WAAW;+DACLC,KAAA,CAAAQ,SAAS,GAAAN,MAAA;IAClBC,IAAI,EAAC,MAAM;IACXtB,KAAK,EAAC,YAAY;IAClBuB,WAAW,EAAC,mBAAmB;IAC/BC,QAAQ,EAAR,EAAQ;IACRC,YAAY,EAAC;iDALJN,KAAA,CAAAQ,SAAS,E,OAUxBvB,mBAAA,CAWM,OAXNwB,UAWM,G,0BAVJxB,mBAAA,CAA4D;IAArDJ,KAAK,EAAC,YAAY;IAACiB,GAAG,EAAC;KAAc,UAAQ,qB,gBACpDb,mBAAA,CAQS;IAPPc,EAAE,EAAC,aAAa;+DACPC,KAAA,CAAAU,WAAW,GAAAR,MAAA;IACpBrB,KAAK,EAAC,aAAa;IACnBwB,QAAQ,EAAR;gCAEApB,mBAAA,CAAgD;IAAxC0B,KAAK,EAAC,EAAE;IAACC,QAAQ,EAAR;KAAS,eAAa,sB,kBACvCC,mBAAA,CAA8EC,SAAA,QAAAC,WAAA,CAAxDf,KAAA,CAAAgB,YAAY,EAAnBC,GAAG;yBAAlBJ,mBAAA,CAA8E;MAAzCK,GAAG,EAAED,GAAG;MAAGN,KAAK,EAAEM;wBAAQA,GAAG,wBAAAE,UAAA;2EALzDnB,KAAA,CAAAU,WAAW,E,KASxBzB,mBAAA,CA0BM,OA1BNmC,WA0BM,GAzBJnC,mBAAA,CAYM,OAZNoC,WAYM,G,4BAXJpC,mBAAA,CAA4D;IAArDJ,KAAK,EAAC,YAAY;IAACiB,GAAG,EAAC;KAAW,aAAW,qB,gBACpDb,mBAAA,CASE;IARAc,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAsB,QAAQ,GAAApB,MAAA;IACjBC,IAAI,EAAC,UAAU;IACftB,KAAK,EAAC,YAAY;IAClBuB,WAAW,EAAC,kBAAkB;IAC9BC,QAAQ,EAAR,EAAQ;IACRC,YAAY,EAAC,cAAc;IAC3BiB,SAAS,EAAC;iDANDvB,KAAA,CAAAsB,QAAQ,E,KAUrBrC,mBAAA,CAUM,OAVNuC,WAUM,G,4BATJvC,mBAAA,CAAkE;IAA3DJ,KAAK,EAAC,YAAY;IAACiB,GAAG,EAAC;KAAW,mBAAiB,qB,gBAC1Db,mBAAA,CAOE;IANAc,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAyB,QAAQ,GAAAvB,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXtB,KAAK,EAAC,YAAY;IAClBuB,WAAW,EAAC,wBAAwB;IACpCC,QAAQ,EAAR;iDAJSL,KAAA,CAAAyB,QAAQ,E,OASvBxC,mBAAA,CASS;IATDkB,IAAI,EAAC,QAAQ;IAAES,QAAQ,EAAEZ,KAAA,CAAA0B,IAAI;IAAE7C,KAAK,EAAC;MAChCmB,KAAA,CAAA0B,IAAI,I,cAAfb,mBAAA,CAKM,OALNc,WAKM,OAAApC,MAAA,SAAAA,MAAA,QAJJN,mBAAA,CAGS;IAHD2C,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAACC,IAAI,EAAC,MAAM;IAAC,kBAAgB,EAAC,QAAQ;IAAC,mBAAiB,EAAC;MAC5H/C,mBAAA,CAAsH;IAA7GgD,aAAa,EAAC,kBAAkB;IAACC,GAAG,EAAC,IAAI;IAACC,MAAM,EAAC,iCAAiC;IAACC,WAAW,EAAC;MACxGnD,mBAAA,CAAyG;IAAhGgD,aAAa,EAAC,mBAAmB;IAACC,GAAG,EAAC,IAAI;IAACC,MAAM,EAAC,mBAAmB;IAACC,WAAW,EAAC;oEAGlFpC,KAAA,CAAA0B,IAAI,I,cAAjBb,mBAAA,CAAsC,QAAAwB,WAAA,EAAnB,cAAY,M,cAC/BxB,mBAAA,CAAwC,QAAAyB,WAAA,EAA3B,sBAAoB,G,+BAGxBtC,KAAA,CAAAuC,KAAK,I,cAAhB1B,mBAAA,CAKM,OALN2B,WAKM,G,4BAJJvD,mBAAA,CAEM;IAFDH,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACgD,IAAI,EAAC;MACnD/C,mBAAA,CAAiI;IAA3HwD,CAAC,EAAC;EAAuH,G,sCAC3H,GACN,GAAAC,gBAAA,CAAG1C,KAAA,CAAAuC,KAAK,iB,wCAGVtD,mBAAA,CAKM,OALN0D,WAKM,GAJJ1D,mBAAA,CAGI,KAHJ2D,WAGI,G,6CAHiB,qBAEnB,qBAAAC,YAAA,CAAoEC,sBAAA;IAAvDC,EAAE,EAAC,QAAQ;IAAClE,KAAK,EAAC;;sBAAY,MAAW,KAAAU,MAAA,SAAAA,MAAA,Q,iBAAX,aAAW,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}