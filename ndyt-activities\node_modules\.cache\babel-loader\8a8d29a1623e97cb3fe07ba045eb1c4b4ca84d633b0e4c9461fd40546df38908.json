{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport default {\n  name: 'ActivityView',\n  data() {\n    return {\n      activities: [],\n      user: null,\n      showUserMenu: false,\n      myActivities: [],\n      loadingActivities: false,\n      editingActivity: null,\n      showMyActivities: false,\n      currentView: 'submit',\n      coordinatorName: '',\n      selectedFilter: null,\n      // null means show all, otherwise filter by state\n      showAccountSettings: false,\n      accountForm: {\n        fullName: '',\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n        teamPin: ''\n      },\n      updatingAccount: false,\n      showPinConfirmation: false,\n      pinConfirmationData: {\n        pin: '',\n        action: '',\n        // 'edit' or 'delete'\n        activity: null,\n        callback: null\n      }\n    };\n  },\n  computed: {\n    filteredActivities() {\n      if (!this.selectedFilter) {\n        return this.myActivities;\n      }\n      return this.myActivities.filter(activity => activity.state === this.selectedFilter);\n    }\n  },\n  methods: {\n    selectFilter(state) {\n      // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\n      this.selectedFilter = this.selectedFilter === state ? null : state;\n    },\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n    },\n    logout() {\n      localStorage.removeItem('ndyt_token');\n      localStorage.removeItem('ndyt_user');\n      localStorage.removeItem('ndyt_team_pin');\n      this.$router.push('/login');\n    },\n    goToAdmin() {\n      this.showUserMenu = false;\n      this.$router.push('/admin');\n    },\n    openAccountSettings() {\n      this.showUserMenu = false;\n      this.accountForm.fullName = this.user?.full_name || '';\n      this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\n      this.accountForm.currentPassword = '';\n      this.accountForm.newPassword = '';\n      this.accountForm.confirmPassword = '';\n      this.showAccountSettings = true;\n    },\n    closeAccountSettings() {\n      this.showAccountSettings = false;\n      this.accountForm = {\n        fullName: '',\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n        teamPin: ''\n      };\n    },\n    async updateAccountSettings() {\n      // Validate form\n      if (!this.accountForm.fullName.trim()) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'يرجى إدخال الاسم الكامل',\n          icon: 'error'\n        });\n        return;\n      }\n      if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\n          icon: 'error'\n        });\n        return;\n      }\n      if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\n          icon: 'error'\n        });\n        return;\n      }\n      this.updatingAccount = true;\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const updateData = {\n          full_name: this.accountForm.fullName.trim(),\n          team_pin: this.accountForm.teamPin.trim()\n        };\n\n        // Only include password if user wants to change it\n        if (this.accountForm.newPassword) {\n          updateData.current_password = this.accountForm.currentPassword;\n          updateData.new_password = this.accountForm.newPassword;\n        }\n        const response = await fetch('/api/v1/ndyt-activities/user/update-profile', {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify(updateData)\n        });\n        if (response.ok) {\n          await response.json();\n\n          // Update local user data\n          this.user.full_name = this.accountForm.fullName.trim();\n          localStorage.setItem('ndyt_user', JSON.stringify(this.user));\n          localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\n          await this.$swal.fire({\n            title: 'تم التحديث بنجاح',\n            text: 'تم تحديث معلومات الحساب بنجاح',\n            icon: 'success'\n          });\n          this.closeAccountSettings();\n        } else {\n          const errorData = await response.json();\n          await this.$swal.fire({\n            title: 'خطأ في التحديث',\n            text: errorData.error || 'فشل في تحديث معلومات الحساب',\n            icon: 'error'\n          });\n        }\n      } catch (error) {\n        console.error('Error updating account:', error);\n        await this.$swal.fire({\n          title: 'خطأ في الاتصال',\n          text: 'حدث خطأ أثناء تحديث معلومات الحساب',\n          icon: 'error'\n        });\n      } finally {\n        this.updatingAccount = false;\n      }\n    },\n    async submitCV() {\n      try {\n        // Validate coordinator name\n        if (!this.coordinatorName.trim()) {\n          await this.$swal.fire({\n            title: 'خطأ في البيانات',\n            text: 'يرجى إدخال اسم منسق المحافظة',\n            icon: 'error'\n          });\n          return;\n        }\n\n        // Collect all activity items\n        const activityItems = document.querySelectorAll('.activity-item');\n        if (activityItems.length === 0) {\n          await this.$swal.fire({\n            title: 'خطأ في البيانات',\n            text: 'يرجى إضافة نشاط واحد على الأقل',\n            icon: 'error'\n          });\n          return;\n        }\n        const token = localStorage.getItem('ndyt_token');\n        const teamPin = localStorage.getItem('ndyt_team_pin');\n        if (!token) {\n          this.$toast.error('يرجى تسجيل الدخول أولاً');\n          this.$router.push('/login');\n          return;\n        }\n        const activities = [];\n        let hasErrors = false;\n\n        // Process each activity item\n        for (let index = 0; index < activityItems.length; index++) {\n          const item = activityItems[index];\n          const inputs = item.querySelectorAll('.activity-input');\n          const ownerName = inputs[0]?.value?.trim();\n          const title = inputs[1]?.value?.trim();\n          const shortDescription = inputs[2]?.value?.trim();\n          const activityDate = inputs[3]?.value;\n          const state = inputs[4]?.value;\n\n          // Validate required fields\n          if (!ownerName || !title || !activityDate || !state) {\n            this.$swal.fire({\n              title: 'خطأ في البيانات',\n              text: `يرجى ملء جميع الحقول المطلوبة للنشاط رقم ${index + 1}`,\n              icon: 'error'\n            });\n            hasErrors = true;\n            break;\n          }\n          let fileId = null;\n\n          // Handle file upload if a file is selected\n          if (item.selectedFile) {\n            try {\n              const formData = new FormData();\n              formData.append('file', item.selectedFile);\n              const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {\n                method: 'POST',\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                body: formData\n              });\n              if (uploadResponse.ok) {\n                const uploadResult = await uploadResponse.json();\n                if (uploadResult.file && uploadResult.file.id) {\n                  fileId = uploadResult.file.id;\n                }\n              } else {\n                const errorData = await uploadResponse.json();\n                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\n                hasErrors = true;\n                break;\n              }\n            } catch (uploadError) {\n              console.error('File upload error:', uploadError);\n              this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\n              hasErrors = true;\n              break;\n            }\n          }\n          activities.push({\n            owner_name: ownerName,\n            title: title,\n            short_description: shortDescription || '',\n            activity_date: activityDate,\n            state: state,\n            file_id: fileId\n          });\n        }\n        if (hasErrors) return;\n\n        // Prepare submission data\n        const submissionData = {\n          coordinator_name: this.coordinatorName.trim(),\n          activities: activities\n        };\n\n        // Submit to backend\n        const response = await fetch('/api/v1/ndyt-activities/submissions', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`,\n            'x-team-pin': teamPin\n          },\n          body: JSON.stringify(submissionData)\n        });\n        if (response.ok) {\n          await response.json();\n          this.$toast.success('تم إرسال النشاطات بنجاح!');\n\n          // Clear the form\n          this.coordinatorName = '';\n\n          // Remove all activity items\n          activityItems.forEach(item => item.remove());\n\n          // Refresh my activities if they're currently shown\n          if (this.showMyActivities) {\n            this.fetchMyActivities();\n          }\n        } else {\n          let errorData = {};\n          try {\n            errorData = await response.json();\n          } catch (jsonError) {\n            console.error('Error parsing response JSON:', jsonError);\n          }\n          this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\n        }\n      } catch (error) {\n        console.error('Error submitting activities:', error);\n        this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\n      }\n    },\n    async fetchMyActivities() {\n      this.loadingActivities = true;\n      this.myActivities = []; // Clear existing activities\n\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        if (!token) {\n          this.$toast.error('يرجى تسجيل الدخول أولاً');\n          this.$router.push('/login');\n          return;\n        }\n        const response = await fetch('/api/v1/ndyt-activities/activities', {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n\n          // The new endpoint returns activities directly with role-based filtering\n          if (data.activities && Array.isArray(data.activities)) {\n            this.myActivities = data.activities.map(activity => ({\n              ...activity,\n              submission_info: {\n                id: activity.submission_id,\n                governorate: activity.governorate,\n                coordinator_name: activity.coordinator_name,\n                created_at: activity.created_at\n              }\n            }));\n          }\n        } else if (response.status === 401) {\n          this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\n          localStorage.removeItem('ndyt_token');\n          localStorage.removeItem('ndyt_user');\n          this.$router.push('/login');\n        } else if (response.status === 404) {\n          // No activities found - this is normal, not an error\n          this.myActivities = [];\n        } else {\n          const errorData = await response.json().catch(() => ({}));\n          console.error('Failed to fetch activities:', errorData);\n          this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\n        }\n      } catch (error) {\n        console.error('Error fetching activities:', error);\n        if (error.name === 'TypeError' && error.message.includes('fetch')) {\n          this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\n        } else {\n          this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\n        }\n      } finally {\n        this.loadingActivities = false;\n      }\n    },\n    toggleMyActivities() {\n      this.showMyActivities = !this.showMyActivities;\n      if (this.showMyActivities && this.myActivities.length === 0) {\n        this.fetchMyActivities();\n      }\n    },\n    editActivity(activity) {\n      this.showPinConfirmationModal('edit', activity, () => {\n        this.openEditModal(activity);\n      });\n    },\n    openEditModal(activity) {\n      this.editingActivity = {\n        ...activity\n      };\n      // Ensure date is properly formatted for date input (YYYY-MM-DD)\n      if (this.editingActivity.activity_date) {\n        const date = new Date(this.editingActivity.activity_date);\n        // Use timezone-safe formatting to avoid date shifting\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        this.editingActivity.activity_date = `${year}-${month}-${day}`;\n      }\n    },\n    async saveActivity() {\n      if (!this.editingActivity) return;\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const teamPin = localStorage.getItem('ndyt_team_pin');\n        const response = await fetch(`/api/v1/ndyt-activities/activities/${this.editingActivity.id}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`,\n            'x-team-pin': teamPin\n          },\n          body: JSON.stringify({\n            owner_name: this.editingActivity.owner_name,\n            title: this.editingActivity.title,\n            short_description: this.editingActivity.short_description,\n            activity_date: this.editingActivity.activity_date,\n            state: this.editingActivity.state\n          })\n        });\n        if (response.ok) {\n          const updatedActivity = await response.json();\n          // Update the activity in the list\n          const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\n          if (index !== -1) {\n            this.myActivities[index] = {\n              ...updatedActivity,\n              submission_info: this.myActivities[index].submission_info\n            };\n          }\n          this.editingActivity = null;\n          this.$toast.success('تم تحديث النشاط بنجاح!');\n        } else {\n          this.$toast.error('فشل في تحديث النشاط');\n        }\n      } catch (error) {\n        console.error('Error updating activity:', error);\n        this.$toast.error('حدث خطأ أثناء تحديث النشاط');\n      }\n    },\n    cancelEdit() {\n      this.editingActivity = null;\n    },\n    deleteActivity(activityId) {\n      const activity = this.myActivities.find(a => a.id === activityId);\n      this.showPinConfirmationModal('delete', activity, async () => {\n        await this.performDeleteActivity(activityId);\n      });\n    },\n    async performDeleteActivity(activityId) {\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const teamPin = localStorage.getItem('ndyt_team_pin');\n        const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {\n          method: 'DELETE',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'x-team-pin': teamPin\n          }\n        });\n        if (response.ok) {\n          this.myActivities = this.myActivities.filter(a => a.id !== activityId);\n          this.$toast.success('تم حذف النشاط بنجاح!');\n        } else {\n          this.$toast.error('فشل في حذف النشاط');\n        }\n      } catch (error) {\n        console.error('Error deleting activity:', error);\n        this.$toast.error('حدث خطأ أثناء حذف النشاط');\n      }\n    },\n    getStatusClass(status) {\n      const statusMap = {\n        'منفذ بصرف': 'status-executed-paid',\n        'منفذ بدون صرف': 'status-executed-unpaid',\n        'مقبول': 'status-accepted',\n        'مرفوض': 'status-rejected',\n        'يحتاج تعديل': 'status-needs-edit',\n        'صرف و لم ينفذ': 'status-paid-not-executed',\n        'مقبول دون صرف': 'status-accepted-unpaid',\n        'مرسل': 'status-sent'\n      };\n      return statusMap[status] || 'status-default';\n    },\n    AddActivityItem() {\n      const activityDiv = document.createElement('div');\n      activityDiv.className = 'activity-item';\n      const activityOwner = document.createElement('input');\n      activityOwner.type = 'text';\n      activityOwner.placeholder = 'اسم صاحب االنشاط';\n      activityOwner.className = 'activity-input';\n      activityDiv.appendChild(activityOwner);\n      const activityTitle = document.createElement('input');\n      activityTitle.type = 'text';\n      activityTitle.placeholder = 'عنوان النشاط';\n      activityTitle.className = 'activity-input';\n      activityDiv.appendChild(activityTitle);\n      const activityShortDescription = document.createElement('input');\n      activityShortDescription.type = 'text';\n      activityShortDescription.placeholder = 'وصف قصير للنشاط';\n      activityShortDescription.className = 'activity-input';\n      activityDiv.appendChild(activityShortDescription);\n      const activityDateLabel = document.createElement('label');\n      activityDateLabel.textContent = 'تاريخ النشاط';\n      activityDiv.appendChild(activityDateLabel);\n      const activityDate = document.createElement('input');\n      activityDate.type = 'date';\n      activityDate.className = 'activity-input';\n      activityDiv.appendChild(activityDate);\n      const activityStateLabel = document.createElement('label');\n      activityStateLabel.textContent = 'حالة النشاط';\n      activityDiv.appendChild(activityStateLabel);\n      const activityApplyState = document.createElement('select');\n      activityApplyState.className = 'activity-input';\n      const states = ['منفذ بصرف', 'منفذ بدون صرف', 'مقبول', 'مرفوض', 'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'];\n      states.forEach(state => {\n        const option = document.createElement('option');\n        option.value = state;\n        option.textContent = state;\n        activityApplyState.appendChild(option);\n      });\n      const activityFileLabel = document.createElement('label');\n      activityFileLabel.textContent = 'ملف النشاط';\n      const activityFileInput = document.createElement('label');\n      activityFileInput.className = 'activity-file-input';\n      activityFileInput.textContent = 'اختر ملف';\n\n      // Create hidden file input\n      const hiddenFileInput = document.createElement('input');\n      hiddenFileInput.type = 'file';\n      hiddenFileInput.accept = '.pdf,.doc,.docx,.jpg,.png';\n      hiddenFileInput.style.display = 'none';\n      hiddenFileInput.onchange = event => {\n        const file = event.target.files[0];\n        if (file) {\n          const fileName = file.name;\n          activityFileInput.textContent = fileName;\n          // Store the file object in the activity div for later use\n          activityDiv.selectedFile = file;\n        } else {\n          activityFileInput.textContent = 'اختر ملف';\n          activityDiv.selectedFile = null;\n        }\n      };\n      activityFileInput.onclick = () => {\n        hiddenFileInput.click();\n      };\n\n      // Append hidden input to activity div\n      activityDiv.appendChild(hiddenFileInput);\n      const activityDeleteButton = document.createElement('button');\n      activityDeleteButton.className = 'activity-delete-button';\n      activityDeleteButton.textContent = 'حذف النشاط';\n      activityDeleteButton.onclick = () => {\n        activityDiv.remove();\n      };\n      activityDiv.appendChild(activityApplyState);\n      activityDiv.appendChild(activityFileLabel);\n      activityDiv.appendChild(activityFileInput);\n      activityDiv.appendChild(activityDeleteButton);\n      this.activities.push(activityDiv);\n      document.querySelector('.activities-list').appendChild(activityDiv);\n    },\n    handleClickOutside(event) {\n      const userSection = event.target.closest('.user-section');\n      if (!userSection) {\n        this.showUserMenu = false;\n      }\n    },\n    setCurrentView(view) {\n      this.currentView = view;\n      // Automatically fetch activities when switching to view tab\n      if (view === 'view') {\n        this.fetchMyActivities();\n      }\n    },\n    refreshActivities() {\n      this.fetchMyActivities();\n    },\n    async loadCoordinatorName() {\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        if (!token) return;\n        const response = await fetch('/api/v1/ndyt-activities/coordinator', {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          this.coordinatorName = data.coordinator_name || '';\n        } else if (response.status === 404) {\n          // No coordinator found for user's governorate\n          this.coordinatorName = '';\n        }\n      } catch (error) {\n        console.error('Error loading coordinator name:', error);\n        // Don't show alert for this error as it's not critical\n      }\n    },\n    exportToCSV() {\n      if (this.filteredActivities.length === 0) {\n        this.$toast.error('لا توجد نشاطات للتصدير');\n        return;\n      }\n\n      // Define CSV headers in Arabic\n      const headers = ['عنوان النشاط', 'صاحب النشاط', 'وصف النشاط', 'تاريخ النشاط', 'حالة النشاط', 'المحافظة', 'منسق المحافظة', 'تاريخ الإرسال'];\n\n      // Convert activities data to CSV format\n      const csvData = this.filteredActivities.map(activity => {\n        return [`\"${activity.title || ''}\"`, `\"${activity.owner_name || ''}\"`, `\"${activity.short_description || ''}\"`, activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '', `\"${activity.state || ''}\"`, `\"${activity.submission_info?.governorate || ''}\"`, `\"${activity.submission_info?.coordinator_name || ''}\"`, activity.submission_info?.created_at ? new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''].join(',');\n      });\n\n      // Combine headers and data\n      const csvContent = [headers.join(','), ...csvData].join('\\n');\n\n      // Add BOM for proper Arabic text encoding in Excel\n      const BOM = '\\uFEFF';\n      const csvWithBOM = BOM + csvContent;\n\n      // Create and download the file\n      const blob = new Blob([csvWithBOM], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      if (link.download !== undefined) {\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n\n        // Generate filename with current date and filter info\n        const currentDate = new Date().toISOString().split('T')[0];\n        const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\n        const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\n        link.setAttribute('download', filename);\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\n      } else {\n        this.$toast.error('المتصفح لا يدعم تحميل الملفات');\n      }\n    },\n    // PIN Confirmation Methods\n    showPinConfirmationModal(action, activity, callback) {\n      this.pinConfirmationData = {\n        pin: '',\n        action: action,\n        activity: activity,\n        callback: callback\n      };\n      this.showPinConfirmation = true;\n      // Focus on PIN input after modal opens\n      this.$nextTick(() => {\n        const pinInput = document.getElementById('confirmPin');\n        if (pinInput) {\n          pinInput.focus();\n        }\n      });\n    },\n    closePinConfirmation() {\n      this.showPinConfirmation = false;\n      this.pinConfirmationData = {\n        pin: '',\n        action: '',\n        activity: null,\n        callback: null\n      };\n    },\n    async confirmPinAction() {\n      const enteredPin = this.pinConfirmationData.pin;\n      const storedPin = localStorage.getItem('ndyt_team_pin');\n      if (enteredPin !== storedPin) {\n        this.$toast.error('رمز الفريق الرقمي غير صحيح');\n        return;\n      }\n\n      // Store callback before closing modal\n      const callback = this.pinConfirmationData.callback;\n\n      // Close modal first\n      this.closePinConfirmation();\n\n      // Execute the callback function after DOM update\n      if (callback) {\n        this.$nextTick(async () => {\n          await callback();\n        });\n      }\n    },\n    downloadFile(file) {\n      try {\n        // Create a direct download link\n        const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\n\n        // Create a temporary anchor element to trigger download\n        const link = document.createElement('a');\n        link.href = fileUrl;\n        link.download = file.file_name;\n        link.target = '_blank'; // Open in new tab as fallback\n        link.style.display = 'none';\n\n        // Append to body, click, and remove\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\n      } catch (error) {\n        console.error('Download error:', error);\n        this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\n      }\n    },\n    getShortFileName(fileName) {\n      // Truncate long file names for display\n      if (fileName.length > 20) {\n        const extension = fileName.split('.').pop();\n        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\n        return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\n      }\n      return fileName;\n    }\n  },\n  mounted() {\n    // Get user info from localStorage\n    const userStr = localStorage.getItem('ndyt_user');\n    if (userStr) {\n      this.user = JSON.parse(userStr);\n    }\n    // Add click outside listener\n    document.addEventListener('click', this.handleClickOutside);\n    // Load coordinator name from previous submissions\n    this.loadCoordinatorName();\n  },\n  beforeUnmount() {\n    document.removeEventListener('click', this.handleClickOutside);\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activities", "user", "showUserMenu", "myActivities", "loadingActivities", "editingActivity", "showMyActivities", "current<PERSON>iew", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showAccountSettings", "accountForm", "fullName", "currentPassword", "newPassword", "confirmPassword", "teamPin", "updatingAccount", "showPinConfirmation", "pinConfirmationData", "pin", "action", "activity", "callback", "computed", "filteredActivities", "filter", "state", "methods", "selectFilter", "toggleUserMenu", "logout", "localStorage", "removeItem", "$router", "push", "goToAdmin", "openAccountSettings", "full_name", "getItem", "closeAccountSettings", "updateAccountSettings", "trim", "$swal", "fire", "title", "text", "icon", "length", "token", "updateData", "team_pin", "current_password", "new_password", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "json", "setItem", "errorData", "error", "console", "submitCV", "activityItems", "document", "querySelectorAll", "$toast", "hasErrors", "index", "item", "inputs", "ownerName", "value", "shortDescription", "activityDate", "fileId", "selectedFile", "formData", "FormData", "append", "uploadResponse", "uploadResult", "file", "id", "uploadError", "owner_name", "short_description", "activity_date", "file_id", "submissionData", "coordinator_name", "success", "for<PERSON>ach", "remove", "fetchMyActivities", "jsonError", "message", "Array", "isArray", "map", "submission_info", "submission_id", "governorate", "created_at", "status", "catch", "includes", "toggleMyActivities", "editActivity", "showPinConfirmationModal", "openEditModal", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "saveActivity", "updatedActivity", "findIndex", "a", "cancelEdit", "deleteActivity", "activityId", "find", "performDeleteActivity", "getStatusClass", "statusMap", "AddActivityItem", "activityDiv", "createElement", "className", "activityOwner", "type", "placeholder", "append<PERSON><PERSON><PERSON>", "activityTitle", "activityShortDescription", "activityDateLabel", "textContent", "activityStateLabel", "activityApplyState", "states", "option", "activityFileLabel", "activityFileInput", "hiddenFileInput", "accept", "style", "display", "onchange", "event", "target", "files", "fileName", "onclick", "click", "activityDeleteButton", "querySelector", "handleClickOutside", "userSection", "closest", "set<PERSON><PERSON><PERSON>View", "view", "refreshActivities", "loadCoordinatorName", "exportToCSV", "csvData", "toLocaleDateString", "join", "csv<PERSON><PERSON>nt", "BOM", "csvWithBOM", "blob", "Blob", "link", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "currentDate", "toISOString", "split", "filterText", "filename", "visibility", "<PERSON><PERSON><PERSON><PERSON>", "$nextTick", "pinInput", "getElementById", "focus", "closePinConfirmation", "confirmPinAction", "enteredPin", "storedPin", "downloadFile", "fileUrl", "file_url", "href", "file_name", "getShortFileName", "extension", "pop", "nameWithoutExt", "substring", "lastIndexOf", "mounted", "userStr", "parse", "addEventListener", "beforeUnmount", "removeEventListener"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\ActivityView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n    name: 'ActivityView',\r\n    data() {\r\n        return {\r\n            activities: [],\r\n            user: null,\r\n            showUserMenu: false,\r\n            myActivities: [],\r\n            loadingActivities: false,\r\n            editingActivity: null,\r\n            showMyActivities: false,\r\n            currentView: 'submit',\r\n            coordinatorName: '',\r\n            selectedFilter: null, // null means show all, otherwise filter by state\r\n            showAccountSettings: false,\r\n            accountForm: {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            },\r\n            updatingAccount: false,\r\n            showPinConfirmation: false,\r\n            pinConfirmationData: {\r\n                pin: '',\r\n                action: '', // 'edit' or 'delete'\r\n                activity: null,\r\n                callback: null\r\n            }\r\n        };\r\n    },\r\n    computed: {\r\n        filteredActivities() {\r\n            if (!this.selectedFilter) {\r\n                return this.myActivities;\r\n            }\r\n            return this.myActivities.filter(activity => activity.state === this.selectedFilter);\r\n        }\r\n    },\r\n    methods: {\r\n        selectFilter(state) {\r\n            // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\r\n            this.selectedFilter = this.selectedFilter === state ? null : state;\r\n        },\r\n        toggleUserMenu() {\r\n            this.showUserMenu = !this.showUserMenu;\r\n        },\r\n        logout() {\r\n            localStorage.removeItem('ndyt_token');\r\n            localStorage.removeItem('ndyt_user');\r\n            localStorage.removeItem('ndyt_team_pin');\r\n            this.$router.push('/login');\r\n        },\r\n        goToAdmin() {\r\n            this.showUserMenu = false;\r\n            this.$router.push('/admin');\r\n        },\r\n        openAccountSettings() {\r\n            this.showUserMenu = false;\r\n            this.accountForm.fullName = this.user?.full_name || '';\r\n            this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\r\n            this.accountForm.currentPassword = '';\r\n            this.accountForm.newPassword = '';\r\n            this.accountForm.confirmPassword = '';\r\n            this.showAccountSettings = true;\r\n        },\r\n        closeAccountSettings() {\r\n            this.showAccountSettings = false;\r\n            this.accountForm = {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            };\r\n        },\r\n        async updateAccountSettings() {\r\n            // Validate form\r\n            if (!this.accountForm.fullName.trim()) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'يرجى إدخال الاسم الكامل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            this.updatingAccount = true;\r\n\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const updateData = {\r\n                    full_name: this.accountForm.fullName.trim(),\r\n                    team_pin: this.accountForm.teamPin.trim()\r\n                };\r\n\r\n                // Only include password if user wants to change it\r\n                if (this.accountForm.newPassword) {\r\n                    updateData.current_password = this.accountForm.currentPassword;\r\n                    updateData.new_password = this.accountForm.newPassword;\r\n                }\r\n\r\n                const response = await fetch('/api/v1/ndyt-activities/user/update-profile', {\r\n                    method: 'PUT',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`\r\n                    },\r\n                    body: JSON.stringify(updateData)\r\n                });\r\n\r\n                if (response.ok) {\r\n                    await response.json();\r\n                    \r\n                    // Update local user data\r\n                    this.user.full_name = this.accountForm.fullName.trim();\r\n                    localStorage.setItem('ndyt_user', JSON.stringify(this.user));\r\n                    localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\r\n\r\n                    await this.$swal.fire({\r\n                        title: 'تم التحديث بنجاح',\r\n                        text: 'تم تحديث معلومات الحساب بنجاح',\r\n                        icon: 'success'\r\n                    });\r\n\r\n                    this.closeAccountSettings();\r\n                } else {\r\n                    const errorData = await response.json();\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في التحديث',\r\n                        text: errorData.error || 'فشل في تحديث معلومات الحساب',\r\n                        icon: 'error'\r\n                    });\r\n                }\r\n            } catch (error) {\r\n                console.error('Error updating account:', error);\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في الاتصال',\r\n                    text: 'حدث خطأ أثناء تحديث معلومات الحساب',\r\n                    icon: 'error'\r\n                });\r\n            } finally {\r\n                this.updatingAccount = false;\r\n            }\r\n        },\r\n        async submitCV() {\r\n            try {\r\n                // Validate coordinator name\r\n                if (!this.coordinatorName.trim()) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إدخال اسم منسق المحافظة',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                // Collect all activity items\r\n                const activityItems = document.querySelectorAll('.activity-item');\r\n                \r\n                if (activityItems.length === 0) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إضافة نشاط واحد على الأقل',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const activities = [];\r\n                let hasErrors = false;\r\n                \r\n                // Process each activity item\r\n                for (let index = 0; index < activityItems.length; index++) {\r\n                    const item = activityItems[index];\r\n                    const inputs = item.querySelectorAll('.activity-input');\r\n                    const ownerName = inputs[0]?.value?.trim();\r\n                    const title = inputs[1]?.value?.trim();\r\n                    const shortDescription = inputs[2]?.value?.trim();\r\n                    const activityDate = inputs[3]?.value;\r\n                    const state = inputs[4]?.value;\r\n                    \r\n                    // Validate required fields\r\n                    if (!ownerName || !title || !activityDate || !state) {\r\n                        this.$swal.fire({\r\n                            title: 'خطأ في البيانات',\r\n                            text: `يرجى ملء جميع الحقول المطلوبة للنشاط رقم ${index + 1}`,\r\n                            icon: 'error'\r\n                        });\r\n                        hasErrors = true;\r\n                        break;\r\n                    }\r\n                    \r\n                    let fileId = null;\r\n                    \r\n                    // Handle file upload if a file is selected\r\n                    if (item.selectedFile) {\r\n                        try {\r\n                            const formData = new FormData();\r\n                            formData.append('file', item.selectedFile);\r\n                            \r\n                            const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {\r\n                                method: 'POST',\r\n                                headers: {\r\n                                    'Authorization': `Bearer ${token}`\r\n                                },\r\n                                body: formData\r\n                            });\r\n                            \r\n                            if (uploadResponse.ok) {\r\n                                const uploadResult = await uploadResponse.json();\r\n                                if (uploadResult.file && uploadResult.file.id) {\r\n                                    fileId = uploadResult.file.id;\r\n                                }\r\n                            } else {\r\n                                const errorData = await uploadResponse.json();\r\n                                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\r\n                                hasErrors = true;\r\n                                break;\r\n                            }\r\n                        } catch (uploadError) {\r\n                            console.error('File upload error:', uploadError);\r\n                            this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\r\n                            hasErrors = true;\r\n                            break;\r\n                        }\r\n                    }\r\n                    \r\n                    activities.push({\r\n                        owner_name: ownerName,\r\n                        title: title,\r\n                        short_description: shortDescription || '',\r\n                        activity_date: activityDate,\r\n                        state: state,\r\n                        file_id: fileId\r\n                    });\r\n                }\r\n                \r\n                if (hasErrors) return;\r\n                \r\n                // Prepare submission data\r\n                const submissionData = {\r\n                    coordinator_name: this.coordinatorName.trim(),\r\n                    activities: activities\r\n                };\r\n                \r\n                // Submit to backend\r\n                const response = await fetch('/api/v1/ndyt-activities/submissions', {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    },\r\n                    body: JSON.stringify(submissionData)\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    await response.json();\r\n                    this.$toast.success('تم إرسال النشاطات بنجاح!');\r\n                    \r\n                    // Clear the form\r\n                    this.coordinatorName = '';\r\n                    \r\n                    // Remove all activity items\r\n                    activityItems.forEach(item => item.remove());\r\n                    \r\n                    // Refresh my activities if they're currently shown\r\n                    if (this.showMyActivities) {\r\n                        this.fetchMyActivities();\r\n                    }\r\n                } else {\r\n                    let errorData = {};\r\n                    try {\r\n                        errorData = await response.json();\r\n                    } catch (jsonError) {\r\n                        console.error('Error parsing response JSON:', jsonError);\r\n                    }\r\n                    this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\r\n                }\r\n                \r\n            } catch (error) {\r\n                console.error('Error submitting activities:', error);\r\n                this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\r\n            }\r\n        },\r\n        async fetchMyActivities() {\r\n            this.loadingActivities = true;\r\n            this.myActivities = []; // Clear existing activities\r\n            \r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                \r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const response = await fetch('/api/v1/ndyt-activities/activities', {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    \r\n                    // The new endpoint returns activities directly with role-based filtering\r\n                    if (data.activities && Array.isArray(data.activities)) {\r\n                        this.myActivities = data.activities.map(activity => ({\r\n                            ...activity,\r\n                            submission_info: {\r\n                                id: activity.submission_id,\r\n                                governorate: activity.governorate,\r\n                                coordinator_name: activity.coordinator_name,\r\n                                created_at: activity.created_at\r\n                            }\r\n                        }));\r\n                    }\r\n                } else if (response.status === 401) {\r\n                    this.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\r\n                    localStorage.removeItem('ndyt_token');\r\n                    localStorage.removeItem('ndyt_user');\r\n                    this.$router.push('/login');\r\n                } else if (response.status === 404) {\r\n                    // No activities found - this is normal, not an error\r\n                    this.myActivities = [];\r\n                } else {\r\n                    const errorData = await response.json().catch(() => ({}));\r\n                    console.error('Failed to fetch activities:', errorData);\r\n                    this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\r\n                }\r\n            } catch (error) {\r\n                console.error('Error fetching activities:', error);\r\n                if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n                    this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\r\n                } else {\r\n                    this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\r\n                }\r\n            } finally {\r\n                this.loadingActivities = false;\r\n            }\r\n        },\r\n        toggleMyActivities() {\r\n            this.showMyActivities = !this.showMyActivities;\r\n            if (this.showMyActivities && this.myActivities.length === 0) {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        editActivity(activity) {\r\n            this.showPinConfirmationModal('edit', activity, () => {\r\n                this.openEditModal(activity);\r\n            });\r\n        },\r\n        openEditModal(activity) {\r\n            this.editingActivity = { ...activity };\r\n            // Ensure date is properly formatted for date input (YYYY-MM-DD)\r\n            if (this.editingActivity.activity_date) {\r\n                const date = new Date(this.editingActivity.activity_date);\r\n                // Use timezone-safe formatting to avoid date shifting\r\n                const year = date.getFullYear();\r\n                const month = String(date.getMonth() + 1).padStart(2, '0');\r\n                const day = String(date.getDate()).padStart(2, '0');\r\n                this.editingActivity.activity_date = `${year}-${month}-${day}`;\r\n            }\r\n        },\r\n        async saveActivity() {\r\n            if (!this.editingActivity) return;\r\n            \r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                const response = await fetch(`/api/v1/ndyt-activities/activities/${this.editingActivity.id}`, {\r\n                    method: 'PUT',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    },\r\n                    body: JSON.stringify({\r\n                        owner_name: this.editingActivity.owner_name,\r\n                        title: this.editingActivity.title,\r\n                        short_description: this.editingActivity.short_description,\r\n                        activity_date: this.editingActivity.activity_date,\r\n                        state: this.editingActivity.state\r\n                    })\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    const updatedActivity = await response.json();\r\n                    // Update the activity in the list\r\n                    const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\r\n                    if (index !== -1) {\r\n                        this.myActivities[index] = { ...updatedActivity, submission_info: this.myActivities[index].submission_info };\r\n                    }\r\n                    this.editingActivity = null;\r\n                    this.$toast.success('تم تحديث النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في تحديث النشاط');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error updating activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء تحديث النشاط');\r\n            }\r\n        },\r\n        cancelEdit() {\r\n            this.editingActivity = null;\r\n        },\r\n        deleteActivity(activityId) {\r\n            const activity = this.myActivities.find(a => a.id === activityId);\r\n            this.showPinConfirmationModal('delete', activity, async () => {\r\n                await this.performDeleteActivity(activityId);\r\n            });\r\n        },\r\n        async performDeleteActivity(activityId) {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {\r\n                    method: 'DELETE',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    }\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    this.myActivities = this.myActivities.filter(a => a.id !== activityId);\r\n                    this.$toast.success('تم حذف النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في حذف النشاط');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error deleting activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء حذف النشاط');\r\n            }\r\n        },\r\n        getStatusClass(status) {\r\n            const statusMap = {\r\n                'منفذ بصرف': 'status-executed-paid',\r\n                'منفذ بدون صرف': 'status-executed-unpaid',\r\n                'مقبول': 'status-accepted',\r\n                'مرفوض': 'status-rejected',\r\n                'يحتاج تعديل': 'status-needs-edit',\r\n                'صرف و لم ينفذ': 'status-paid-not-executed',\r\n                'مقبول دون صرف': 'status-accepted-unpaid',\r\n                'مرسل': 'status-sent'\r\n            };\r\n            return statusMap[status] || 'status-default';\r\n        },\r\n        AddActivityItem() {\r\n            const activityDiv = document.createElement('div');\r\n            activityDiv.className = 'activity-item';\r\n            const activityOwner = document.createElement('input');\r\n            activityOwner.type = 'text';\r\n            activityOwner.placeholder = 'اسم صاحب االنشاط';\r\n            activityOwner.className = 'activity-input';\r\n            activityDiv.appendChild(activityOwner);\r\n            const activityTitle = document.createElement('input');\r\n            activityTitle.type = 'text';\r\n            activityTitle.placeholder = 'عنوان النشاط';\r\n            activityTitle.className = 'activity-input';\r\n            activityDiv.appendChild(activityTitle);\r\n            const activityShortDescription = document.createElement('input');\r\n            activityShortDescription.type = 'text';\r\n            activityShortDescription.placeholder = 'وصف قصير للنشاط';\r\n            activityShortDescription.className = 'activity-input';\r\n            activityDiv.appendChild(activityShortDescription);\r\n            const activityDateLabel = document.createElement('label');\r\n            activityDateLabel.textContent = 'تاريخ النشاط';\r\n            activityDiv.appendChild(activityDateLabel);\r\n            const activityDate = document.createElement('input');\r\n            activityDate.type = 'date';\r\n            activityDate.className = 'activity-input';\r\n            activityDiv.appendChild(activityDate);\r\n            const activityStateLabel = document.createElement('label');\r\n            activityStateLabel.textContent = 'حالة النشاط';\r\n            activityDiv.appendChild(activityStateLabel);\r\n            const activityApplyState = document.createElement('select');\r\n            activityApplyState.className = 'activity-input';\r\n            const states = \r\n            ['منفذ بصرف',  'منفذ بدون صرف', 'مقبول', 'مرفوض',\r\n            'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'\r\n            ];\r\n            states.forEach(state => {\r\n                const option = document.createElement('option');\r\n                option.value = state;\r\n                option.textContent = state;\r\n                activityApplyState.appendChild(option);\r\n            });\r\n            const activityFileLabel = document.createElement('label');\r\n            activityFileLabel.textContent = 'ملف النشاط';\r\n            const activityFileInput = document.createElement('label');\r\n            activityFileInput.className = 'activity-file-input';\r\n            activityFileInput.textContent = 'اختر ملف';\r\n            \r\n            // Create hidden file input\r\n            const hiddenFileInput = document.createElement('input');\r\n            hiddenFileInput.type = 'file';\r\n            hiddenFileInput.accept = '.pdf,.doc,.docx,.jpg,.png';\r\n            hiddenFileInput.style.display = 'none';\r\n            hiddenFileInput.onchange = (event) => {\r\n                const file = event.target.files[0];\r\n                if (file) {\r\n                    const fileName = file.name;\r\n                    activityFileInput.textContent = fileName;\r\n                    // Store the file object in the activity div for later use\r\n                    activityDiv.selectedFile = file;\r\n                } else {\r\n                    activityFileInput.textContent = 'اختر ملف';\r\n                    activityDiv.selectedFile = null;\r\n                }\r\n            };\r\n            \r\n            activityFileInput.onclick = () => {\r\n                hiddenFileInput.click();\r\n            };\r\n            \r\n            // Append hidden input to activity div\r\n            activityDiv.appendChild(hiddenFileInput);\r\n            const activityDeleteButton = document.createElement('button');\r\n            activityDeleteButton.className = 'activity-delete-button';\r\n            activityDeleteButton.textContent = 'حذف النشاط';\r\n            activityDeleteButton.onclick = () => {\r\n                activityDiv.remove();\r\n            };\r\n            activityDiv.appendChild(activityApplyState);\r\n            activityDiv.appendChild(activityFileLabel);\r\n            activityDiv.appendChild(activityFileInput);\r\n            activityDiv.appendChild(activityDeleteButton);\r\n            this.activities.push(activityDiv);\r\n            document.querySelector('.activities-list').appendChild(activityDiv);\r\n        },\r\n        handleClickOutside(event) {\r\n            const userSection = event.target.closest('.user-section');\r\n            if (!userSection) {\r\n                this.showUserMenu = false;\r\n            }\r\n        },\r\n        setCurrentView(view) {\r\n            this.currentView = view;\r\n            // Automatically fetch activities when switching to view tab\r\n            if (view === 'view') {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        refreshActivities() {\r\n            this.fetchMyActivities();\r\n        },\r\n        async loadCoordinatorName() {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                if (!token) return;\r\n\r\n                const response = await fetch('/api/v1/ndyt-activities/coordinator', {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    this.coordinatorName = data.coordinator_name || '';\r\n                } else if (response.status === 404) {\r\n                    // No coordinator found for user's governorate\r\n                    this.coordinatorName = '';\r\n                }\r\n            } catch (error) {\r\n                console.error('Error loading coordinator name:', error);\r\n                // Don't show alert for this error as it's not critical\r\n            }\r\n        },\r\n        exportToCSV() {\r\n            if (this.filteredActivities.length === 0) {\r\n                this.$toast.error('لا توجد نشاطات للتصدير');\r\n                return;\r\n            }\r\n\r\n            // Define CSV headers in Arabic\r\n            const headers = [\r\n                'عنوان النشاط',\r\n                'صاحب النشاط', \r\n                'وصف النشاط',\r\n                'تاريخ النشاط',\r\n                'حالة النشاط',\r\n                'المحافظة',\r\n                'منسق المحافظة',\r\n                'تاريخ الإرسال'\r\n            ];\r\n\r\n            // Convert activities data to CSV format\r\n            const csvData = this.filteredActivities.map(activity => {\r\n                return [\r\n                    `\"${activity.title || ''}\"`,\r\n                    `\"${activity.owner_name || ''}\"`,\r\n                    `\"${activity.short_description || ''}\"`,\r\n                    activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '',\r\n                    `\"${activity.state || ''}\"`,\r\n                    `\"${activity.submission_info?.governorate || ''}\"`,\r\n                    `\"${activity.submission_info?.coordinator_name || ''}\"`,\r\n                    activity.submission_info?.created_at ? new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''\r\n                ].join(',');\r\n            });\r\n\r\n            // Combine headers and data\r\n            const csvContent = [headers.join(','), ...csvData].join('\\n');\r\n\r\n            // Add BOM for proper Arabic text encoding in Excel\r\n            const BOM = '\\uFEFF';\r\n            const csvWithBOM = BOM + csvContent;\r\n\r\n            // Create and download the file\r\n            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });\r\n            const link = document.createElement('a');\r\n            \r\n            if (link.download !== undefined) {\r\n                const url = URL.createObjectURL(blob);\r\n                link.setAttribute('href', url);\r\n                \r\n                // Generate filename with current date and filter info\r\n                const currentDate = new Date().toISOString().split('T')[0];\r\n                const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\r\n                const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\r\n                \r\n                link.setAttribute('download', filename);\r\n                link.style.visibility = 'hidden';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\r\n            } else {\r\n                this.$toast.error('المتصفح لا يدعم تحميل الملفات');\r\n            }\r\n        },\r\n        // PIN Confirmation Methods\r\n        showPinConfirmationModal(action, activity, callback) {\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: action,\r\n                activity: activity,\r\n                callback: callback\r\n            };\r\n            this.showPinConfirmation = true;\r\n            // Focus on PIN input after modal opens\r\n            this.$nextTick(() => {\r\n                const pinInput = document.getElementById('confirmPin');\r\n                if (pinInput) {\r\n                    pinInput.focus();\r\n                }\r\n            });\r\n        },\r\n        closePinConfirmation() {\r\n            this.showPinConfirmation = false;\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: '',\r\n                activity: null,\r\n                callback: null\r\n            };\r\n        },\r\n        async confirmPinAction() {\r\n            const enteredPin = this.pinConfirmationData.pin;\r\n            const storedPin = localStorage.getItem('ndyt_team_pin');\r\n            \r\n            if (enteredPin !== storedPin) {\r\n                this.$toast.error('رمز الفريق الرقمي غير صحيح');\r\n                return;\r\n            }\r\n            \r\n            // Store callback before closing modal\r\n            const callback = this.pinConfirmationData.callback;\r\n            \r\n            // Close modal first\r\n            this.closePinConfirmation();\r\n            \r\n            // Execute the callback function after DOM update\r\n            if (callback) {\r\n                this.$nextTick(async () => {\r\n                    await callback();\r\n                });\r\n            }\r\n        },\r\n        downloadFile(file) {\r\n            try {\r\n                // Create a direct download link\r\n                const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\r\n                \r\n                // Create a temporary anchor element to trigger download\r\n                const link = document.createElement('a');\r\n                link.href = fileUrl;\r\n                link.download = file.file_name;\r\n                link.target = '_blank'; // Open in new tab as fallback\r\n                link.style.display = 'none';\r\n                \r\n                // Append to body, click, and remove\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\r\n            } catch (error) {\r\n                console.error('Download error:', error);\r\n                this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\r\n            }\r\n        },\r\n        getShortFileName(fileName) {\r\n            // Truncate long file names for display\r\n            if (fileName.length > 20) {\r\n                const extension = fileName.split('.').pop();\r\n                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\r\n                return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\r\n            }\r\n            return fileName;\r\n        }\r\n    },\r\n    mounted() {\r\n        // Get user info from localStorage\r\n        const userStr = localStorage.getItem('ndyt_user');\r\n        if (userStr) {\r\n            this.user = JSON.parse(userStr);\r\n        }\r\n        // Add click outside listener\r\n        document.addEventListener('click', this.handleClickOutside);\r\n        // Load coordinator name from previous submissions\r\n        this.loadCoordinatorName();\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n    }\r\n}\r\n</script>\r\n<template>\r\n    \r\n        <div class=\"navbar\">\r\n            <div class=\"navbar-content\">\r\n                <div class=\"navbar-brand\">\r\n                    <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n                    <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n                    <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n                </div>\r\n                <div class=\"nav-actions\">\r\n                    <button class=\"btn nav-btn\" :class=\"{ 'btn-primary': currentView === 'submit', 'btn-secondary': currentView !== 'submit' }\" @click=\"setCurrentView('submit')\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/>\r\n                        </svg>\r\n                        <span>إرسال النشاطات</span>\r\n                    </button>\r\n                    <button class=\"btn nav-btn\" :class=\"{ 'btn-primary': currentView === 'view', 'btn-secondary': currentView !== 'view' }\" @click=\"setCurrentView('view')\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                        </svg>\r\n                        <span>النشاطات المرسلة</span>\r\n                    </button>\r\n                </div>\r\n                <div class=\"user-section\">\r\n                    <div class=\"user-button\" @click=\"toggleUserMenu\">\r\n                        <div class=\"user-avatar\">\r\n                            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\r\n                            </svg>\r\n                        </div>\r\n                        <div class=\"user-info\">\r\n                            <span class=\"user-name\">{{ user?.full_name || user?.username || 'المستخدم' }}</span>\r\n                            <svg class=\"dropdown-arrow\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M7 10l5 5 5-5z\"/>\r\n                            </svg>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"showUserMenu\" class=\"user-menu\">\r\n                        <div class=\"user-menu-item\" @click=\"openAccountSettings\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"/>\r\n                            </svg>\r\n                            <span>إعدادات الحساب</span>\r\n                        </div>\r\n                        <div v-if=\"user && user.rank === 'admin'\" class=\"user-menu-item\" @click=\"goToAdmin\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"/>\r\n                            </svg>\r\n                            <span>لوحة الإدارة</span>\r\n                        </div>\r\n                        <div class=\"user-menu-item\" @click=\"logout\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"/>\r\n                            </svg>\r\n                            <span>تسجيل الخروج</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"main-content\">\r\n            <!-- Submit Activities View -->\r\n            <div v-if=\"currentView === 'submit'\" class=\"submit-view\">\r\n                <div class=\"view-header\">\r\n                    <h2 class=\"view-title\">إرسال النشاطات الجديدة</h2>\r\n                    <p class=\"view-description\">قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة</p>\r\n                </div>\r\n                <div class=\"base-info-container\">\r\n                    <span class=\"base-info-label\">المعلومات الأساسية</span>\r\n                    <label for=\"coordinator-name\" class=\"field-label\">منسق المحافظة</label>\r\n                    <input type=\"text\" id=\"coordinator-name\" placeholder=\"اسم منسق المحافظة\" class=\"base-info-input\" v-model=\"coordinatorName\" required>\r\n                </div>\r\n                \r\n                <div class=\"splitter\"></div>\r\n                \r\n                <div class=\"activities-info-container\">\r\n                    <span class=\"base-info-label\">إضافة نشاطات جديدة</span>\r\n                    <div class=\"activities-list\">\r\n                    </div>\r\n                    <button style=\"margin: 10px; max-width: 250px;\" @click=\"AddActivityItem\">\r\n                        <span>إضافة نشاط جديد</span>\r\n                    </button>\r\n                </div>\r\n                <div class=\"splitter\"></div>\r\n                <button style=\"margin: 0 50px; background-color: orange; max-width: 180px;\" @click=\"submitCV\">\r\n                    <span>إرسال النشاطات</span>\r\n                </button>\r\n            </div>\r\n            \r\n            <!-- View Activities Section -->\r\n            <div v-if=\"currentView === 'view'\" class=\"view-activities\">\r\n                <div class=\"view-header\">\r\n                    <div class=\"view-header-content\">\r\n                        <h2 class=\"view-title\">النشاطات</h2>\r\n                        <p class=\"view-description\">عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)</p>\r\n                    </div>\r\n                    <div class=\"header-actions\">\r\n                        <button @click=\"exportToCSV\" class=\"export-btn\" :disabled=\"loadingActivities || filteredActivities.length === 0\" title=\"تصدير إلى Excel\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                            </svg>\r\n                            <span>تصدير CSV</span>\r\n                        </button>\r\n                        <button @click=\"refreshActivities\" class=\"refresh-btn\" :disabled=\"loadingActivities\">\r\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\" :class=\"{ 'spinning': loadingActivities }\">\r\n                                <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\r\n                            </svg>\r\n                            <span>تحديث</span>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"activities-container\">\r\n                    <div v-if=\"loadingActivities\" class=\"loading-message\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <span>جاري تحميل النشاطات...</span>\r\n                    </div>\r\n                    \r\n                    <div v-else-if=\"myActivities.length === 0\" class=\"no-activities-message\">\r\n                        <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"empty-icon\">\r\n                            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n                        </svg>\r\n                        <h3>لا توجد نشاطات</h3>\r\n                        <p>لا توجد نشاطات متاحة حسب صلاحياتك حالياً</p>\r\n                    </div>\r\n                    \r\n                    <!-- Edit Form Modal -->\r\n                    <div v-if=\"editingActivity\" class=\"edit-modal-overlay\" @click=\"cancelEdit\">\r\n                        <div class=\"edit-modal\" @click.stop>\r\n                            <div class=\"edit-modal-header\">\r\n                                <h3>تعديل النشاط</h3>\r\n                                <button @click=\"cancelEdit\" class=\"close-btn\">\r\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                    </svg>\r\n                                </button>\r\n                            </div>\r\n                            <div class=\"edit-form\">\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group\">\r\n                                        <label>اسم صاحب النشاط:</label>\r\n                                        <input v-model=\"editingActivity.owner_name\" type=\"text\" class=\"form-input\">\r\n                                    </div>\r\n                                    <div class=\"form-group\">\r\n                                        <label>عنوان النشاط:</label>\r\n                                        <input v-model=\"editingActivity.title\" type=\"text\" class=\"form-input\">\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group\">\r\n                                        <label>تاريخ النشاط:</label>\r\n                                        <input v-model=\"editingActivity.activity_date\" type=\"date\" class=\"form-input\">\r\n                                    </div>\r\n                                    <div class=\"form-group\">\r\n                                        <label>حالة النشاط:</label>\r\n                                        <select v-model=\"editingActivity.state\" class=\"form-select\">\r\n                                            <option value=\"مرسل\">مرسل</option>\r\n                                            <option value=\"منفذ بصرف\">منفذ بصرف</option>\r\n                                            <option value=\"منفذ بدون صرف\">منفذ بدون صرف</option>\r\n                                            <option value=\"مقبول\">مقبول</option>\r\n                                            <option value=\"مرفوض\">مرفوض</option>\r\n                                            <option value=\"يحتاج تعديل\">يحتاج تعديل</option>\r\n                                            <option value=\"صرف و لم ينفذ\">صرف و لم ينفذ</option>\r\n                                            <option value=\"مقبول دون صرف\">مقبول دون صرف</option>\r\n                                        </select>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-row\">\r\n                                    <div class=\"form-group full-width\">\r\n                                        <label>وصف مختصر:</label>\r\n                                        <textarea v-model=\"editingActivity.short_description\" class=\"form-textarea\" rows=\"3\"></textarea>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"form-actions\">\r\n                                    <button @click=\"saveActivity\" class=\"save-btn\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                            <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\r\n                                        </svg>\r\n                                        حفظ التغييرات\r\n                                    </button>\r\n                                    <button @click=\"cancelEdit\" class=\"cancel-btn\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                            <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                        </svg>\r\n                                        إلغاء\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <!-- Activities Table -->\r\n                    <div v-else-if=\"myActivities.length > 0\" class=\"activities-table-container\">\r\n                        <div class=\"table-header\">\r\n                            <div class=\"table-stats\">\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === null }\" @click=\"selectFilter(null)\">\r\n                                    <span class=\"stat-number\">{{ myActivities.length }}</span>\r\n                                    <span class=\"stat-label\">إجمالي النشاطات</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بصرف' }\" @click=\"selectFilter('منفذ بصرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بصرف').length }}</span>\r\n                                    <span class=\"stat-label\">منفذ بصرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بدون صرف' }\" @click=\"selectFilter('منفذ بدون صرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بدون صرف').length }}</span>\r\n                                    <span class=\"stat-label\">منفذ بدون صرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول' }\" @click=\"selectFilter('مقبول')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول').length }}</span>\r\n                                    <span class=\"stat-label\">مقبول</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرفوض' }\" @click=\"selectFilter('مرفوض')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرفوض').length }}</span>\r\n                                    <span class=\"stat-label\">مرفوض</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'يحتاج تعديل' }\" @click=\"selectFilter('يحتاج تعديل')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'يحتاج تعديل').length }}</span>\r\n                                    <span class=\"stat-label\">يحتاج تعديل</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'صرف و لم ينفذ' }\" @click=\"selectFilter('صرف و لم ينفذ')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'صرف و لم ينفذ').length }}</span>\r\n                                    <span class=\"stat-label\">صرف و لم ينفذ</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول دون صرف' }\" @click=\"selectFilter('مقبول دون صرف')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول دون صرف').length }}</span>\r\n                                    <span class=\"stat-label\">مقبول دون صرف</span>\r\n                                </div>\r\n                                <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرسل' }\" @click=\"selectFilter('مرسل')\">\r\n                                    <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرسل').length }}</span>\r\n                                    <span class=\"stat-label\">مرسل</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        \r\n                        <div class=\"table-wrapper\">\r\n                            <table class=\"activities-table\">\r\n                                <thead>\r\n                                    <tr>\r\n                                        <th class=\"col-title\">عنوان النشاط</th>\r\n                                        <th class=\"col-owner\">صاحب النشاط</th>\r\n                                        <th class=\"col-date\">تاريخ النشاط</th>\r\n                                        <th class=\"col-status\">الحالة</th>\r\n                                        <th class=\"col-files\">الملفات</th>\r\n                                        <th class=\"col-governorate\">المحافظة</th>\r\n                                        <th class=\"col-coordinator\">المنسق</th>\r\n                                        <th class=\"col-actions\">الإجراءات</th>\r\n                                    </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                    <tr v-for=\"activity in filteredActivities\" :key=\"activity.id\" class=\"activity-row\">\r\n                                        <td class=\"col-title\">\r\n                                            <div class=\"activity-title\">\r\n                                                <h4>{{ activity.title }}</h4>\r\n                                                <p class=\"activity-description\">{{ activity.short_description || 'لا يوجد وصف' }}</p>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-owner\">\r\n                                            <div class=\"owner-info\">\r\n                                                <span>{{ activity.owner_name }}</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-date\">\r\n                                            <div class=\"date-info\">\r\n                                                <span class=\"date-main\">{{ new Date(activity.activity_date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }) }}</span>\r\n                                                <span class=\"date-year\">{{ new Date(activity.activity_date).getFullYear() }}</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-status\">\r\n                                            <span class=\"status-badge\" :class=\"getStatusClass(activity.state)\">\r\n                                                <div class=\"status-indicator\"></div>\r\n                                                {{ activity.state }}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td class=\"col-files\">\r\n                                            <div class=\"files-container\">\r\n                                                <div v-if=\"activity.files && activity.files.length > 0\" class=\"file-buttons\">\r\n                                                    <button \r\n                                                        v-for=\"file in activity.files\" \r\n                                                        :key=\"file.id\"\r\n                                                        @click=\"downloadFile(file)\"\r\n                                                        class=\"file-download-btn\"\r\n                                                        :title=\"`تحميل ${file.file_name}`\"\r\n                                                    >\r\n                                                        <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                            <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                                                        </svg>\r\n                                                        <span class=\"file-name\">{{ getShortFileName(file.file_name) }}</span>\r\n                                                    </button>\r\n                                                </div>\r\n                                                <span v-else class=\"no-files\">لا توجد ملفات</span>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td class=\"col-governorate\">\r\n                                            <span class=\"governorate-name\">{{ activity.submission_info.governorate }}</span>\r\n                                        </td>\r\n                                        <td class=\"col-coordinator\">\r\n                                            <span class=\"coordinator-name\">{{ activity.submission_info.coordinator_name }}</span>\r\n                                        </td>\r\n                                        <td class=\"col-actions\">\r\n                                            <div class=\"action-buttons\">\r\n                                                <button @click=\"editActivity(activity)\" class=\"action-btn edit-btn\" title=\"تعديل\">\r\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                        <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\r\n                                                    </svg>\r\n                                                </button>\r\n                                                <button @click=\"deleteActivity(activity.id)\" class=\"action-btn delete-btn\" title=\"حذف\">\r\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                        <path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/>\r\n                                                    </svg>\r\n                                                </button>\r\n                                            </div>\r\n                                        </td>\r\n                                    </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Account Settings Modal -->\r\n        <div v-if=\"showAccountSettings\" class=\"modal-overlay\" @click=\"closeAccountSettings\">\r\n            <div class=\"modal-content account-settings-modal\" @click.stop>\r\n                <div class=\"modal-header\">\r\n                    <h3>إعدادات الحساب</h3>\r\n                    <button @click=\"closeAccountSettings\" class=\"close-btn\">&times;</button>\r\n                </div>\r\n                \r\n                <form @submit.prevent=\"updateAccountSettings\" class=\"account-form\">\r\n                    <div class=\"form-group\">\r\n                        <label for=\"fullName\">الاسم الكامل:</label>\r\n                        <input \r\n                            type=\"text\" \r\n                            id=\"fullName\" \r\n                            v-model=\"accountForm.fullName\" \r\n                            required \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل اسمك الكامل\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"teamPin\">رمز الفريق الرقمي:</label>\r\n                        <input \r\n                            type=\"text\" \r\n                            id=\"teamPin\" \r\n                            v-model=\"accountForm.teamPin\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"password-section\">\r\n                        <h4>تغيير كلمة المرور (اختياري)</h4>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"currentPassword\">كلمة المرور الحالية:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"currentPassword\" \r\n                                v-model=\"accountForm.currentPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أدخل كلمة المرور الحالية\"\r\n                            >\r\n                        </div>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"newPassword\">كلمة المرور الجديدة:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"newPassword\" \r\n                                v-model=\"accountForm.newPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\r\n                            >\r\n                        </div>\r\n                        \r\n                        <div class=\"form-group\">\r\n                            <label for=\"confirmPassword\">تأكيد كلمة المرور الجديدة:</label>\r\n                            <input \r\n                                type=\"password\" \r\n                                id=\"confirmPassword\" \r\n                                v-model=\"accountForm.confirmPassword\" \r\n                                class=\"form-input\"\r\n                                placeholder=\"أعد إدخال كلمة المرور الجديدة\"\r\n                            >\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"form-actions\">\r\n                        <button type=\"button\" @click=\"closeAccountSettings\" class=\"cancel-btn\">إلغاء</button>\r\n                        <button type=\"submit\" :disabled=\"updatingAccount\" class=\"save-btn\">\r\n                            <span v-if=\"updatingAccount\">جاري الحفظ...</span>\r\n                            <span v-else>حفظ التغييرات</span>\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- PIN Confirmation Modal -->\r\n        <div v-if=\"showPinConfirmation\" class=\"modal-overlay\" @click=\"closePinConfirmation\">\r\n            <div class=\"modal-content pin-modal\" @click.stop>\r\n                <div class=\"modal-header\">\r\n                    <h3>تأكيد العملية</h3>\r\n                    <button @click=\"closePinConfirmation\" class=\"close-btn\">&times;</button>\r\n                </div>\r\n                <div class=\"modal-body\">\r\n                    <p class=\"pin-message\">\r\n                        {{ pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' }}\r\n                    </p>\r\n                    <div class=\"form-group\">\r\n                        <label for=\"confirmPin\">رمز الفريق الرقمي:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"confirmPin\"\r\n                            v-model=\"pinConfirmationData.pin\" \r\n                            placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                            @keyup.enter=\"confirmPinAction\"\r\n                            class=\"form-control\"\r\n                        >\r\n                    </div>\r\n                </div>\r\n                <div class=\"modal-footer\">\r\n                    <button @click=\"closePinConfirmation\" class=\"cancel-btn\">إلغاء</button>\r\n                    <button @click=\"confirmPinAction\" class=\"confirm-btn\" :disabled=\"!pinConfirmationData.pin\">\r\n                        {{ pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل' }}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n</template>\r\n<style>\r\n/* Enhanced Navigation Styling */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: var(--space-3);\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n    padding: var(--space-3) var(--space-5);\r\n    border-radius: var(--radius-xl);\r\n    font-weight: var(--font-weight-semibold);\r\n    font-size: var(--font-size-sm);\r\n    transition: all var(--transition-base);\r\n    min-width: auto;\r\n    white-space: nowrap;\r\n}\r\n\r\n.nav-btn svg {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.nav-btn span {\r\n    display: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .nav-btn span {\r\n        display: inline;\r\n    }\r\n\r\n    .nav-btn {\r\n        padding: var(--space-3) var(--space-6);\r\n    }\r\n}\r\n\r\n/* Enhanced User Section */\r\n.user-section {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n    padding: var(--space-3) var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    cursor: pointer;\r\n    transition: all var(--transition-base);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: var(--border-secondary);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.user-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));\r\n    border-radius: var(--radius-full);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n}\r\n\r\n.user-name {\r\n    font-weight: var(--font-weight-semibold);\r\n    font-size: var(--font-size-sm);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform var(--transition-fast);\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n/* Enhanced User Menu */\r\n.user-menu {\r\n    position: absolute;\r\n    top: 100%;\r\n    right: 0;\r\n    margin-top: var(--space-2);\r\n    background: var(--bg-secondary);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    box-shadow: var(--shadow-dark-xl);\r\n    backdrop-filter: blur(20px);\r\n    min-width: 200px;\r\n    z-index: var(--z-dropdown);\r\n    animation: slideDown 0.2s ease-out;\r\n}\r\n\r\n.user-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n    padding: var(--space-4);\r\n    cursor: pointer;\r\n    transition: all var(--transition-fast);\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n    font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.user-menu-item:hover {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    color: var(--text-primary);\r\n}\r\n\r\n.user-menu-item:first-child {\r\n    border-radius: var(--radius-xl) var(--radius-xl) 0 0;\r\n}\r\n\r\n.user-menu-item:last-child {\r\n    border-radius: 0 0 var(--radius-xl) var(--radius-xl);\r\n}\r\n\r\n.user-menu-item svg {\r\n    flex-shrink: 0;\r\n    opacity: 0.7;\r\n}\r\n\r\n.user-menu-item:hover svg {\r\n    opacity: 1;\r\n}\r\n\r\n/* Enhanced Content Container */\r\n.content-container {\r\n    max-width: var(--container-xl);\r\n    margin: 0 auto;\r\n    padding: var(--space-6) var(--space-4);\r\n}\r\n\r\n/* Enhanced View Header */\r\n.view-header {\r\n    text-align: center;\r\n    margin-bottom: var(--space-8);\r\n    padding: var(--space-6);\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: var(--radius-2xl);\r\n    border: 1px solid var(--border-primary);\r\n}\r\n\r\n.view-title {\r\n    font-size: var(--font-size-3xl);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin-bottom: var(--space-3);\r\n    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.view-description {\r\n    font-size: var(--font-size-lg);\r\n    color: var(--text-secondary);\r\n    line-height: var(--line-height-relaxed);\r\n    margin: 0;\r\n}\r\n\r\n/* Enhanced Form Styling */\r\n.form-container {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    backdrop-filter: blur(20px);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-3xl);\r\n    padding: var(--space-8);\r\n    box-shadow: var(--shadow-dark-lg);\r\n    margin-bottom: var(--space-8);\r\n}\r\n\r\n.form-row {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: var(--space-6);\r\n    margin-bottom: var(--space-6);\r\n    padding: var(--space-4);\r\n    background: rgba(255, 255, 255, 0.03);\r\n    border-radius: var(--radius-xl);\r\n    border: 1px solid var(--border-primary);\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: var(--space-2);\r\n}\r\n\r\n.form-group label {\r\n    font-size: var(--font-size-sm);\r\n    font-weight: var(--font-weight-semibold);\r\n    color: var(--text-secondary);\r\n    margin-bottom: var(--space-1);\r\n}\r\n\r\n.form-group input,\r\n.form-group select,\r\n.form-group textarea {\r\n    padding: var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-xl);\r\n    color: var(--text-primary);\r\n    font-family: inherit;\r\n    font-size: var(--font-size-base);\r\n    transition: all var(--transition-base);\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.form-group input:focus,\r\n.form-group select:focus,\r\n.form-group textarea:focus {\r\n    outline: none;\r\n    border-color: var(--accent-primary);\r\n    background: rgba(255, 255, 255, 0.12);\r\n    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);\r\n}\r\n\r\n.form-group textarea {\r\n    min-height: 120px;\r\n    resize: vertical;\r\n}\r\n\r\n/* Enhanced Activity Cards */\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n    gap: var(--space-6);\r\n    margin-top: var(--space-6);\r\n    padding: 0 var(--space-2);\r\n}\r\n\r\n.activity-card {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    backdrop-filter: blur(20px);\r\n    border: 2px solid var(--border-primary);\r\n    border-radius: var(--radius-3xl);\r\n    padding: var(--space-6);\r\n    box-shadow: var(--shadow-dark-lg);\r\n    transition: all var(--transition-base);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));\r\n    opacity: 0;\r\n    transition: opacity var(--transition-base);\r\n}\r\n\r\n.activity-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: var(--shadow-dark-xl);\r\n    border-color: var(--border-secondary);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    opacity: 1;\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: var(--space-4);\r\n    gap: var(--space-3);\r\n}\r\n\r\n.activity-title {\r\n    font-size: var(--font-size-lg);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin: 0;\r\n    line-height: var(--line-height-tight);\r\n}\r\n\r\n.activity-status {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.activity-details {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: var(--space-3);\r\n    margin-bottom: var(--space-5);\r\n}\r\n\r\n.activity-detail {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: var(--space-2);\r\n    font-size: var(--font-size-sm);\r\n    color: var(--text-secondary);\r\n}\r\n\r\n.activity-detail svg {\r\n    flex-shrink: 0;\r\n    opacity: 0.7;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: var(--space-2);\r\n    margin-top: auto;\r\n    padding-top: var(--space-4);\r\n    border-top: 1px solid var(--border-primary);\r\n}\r\n\r\n/* Enhanced Table Styling */\r\n.table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: var(--radius-2xl);\r\n    border: 2px solid var(--border-primary);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n    box-shadow: var(--shadow-dark-lg);\r\n    margin-top: var(--space-6);\r\n}\r\n\r\n.table-header {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: var(--space-4) var(--space-6);\r\n    border-bottom: 2px solid var(--border-primary);\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: var(--space-4);\r\n}\r\n\r\n.table-title {\r\n    font-size: var(--font-size-xl);\r\n    font-weight: var(--font-weight-bold);\r\n    color: var(--text-primary);\r\n    margin: 0;\r\n}\r\n\r\n.table-filters {\r\n    display: flex;\r\n    gap: var(--space-2);\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.filter-btn {\r\n    padding: var(--space-2) var(--space-4);\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid var(--border-primary);\r\n    border-radius: var(--radius-lg);\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n    cursor: pointer;\r\n    transition: all var(--transition-fast);\r\n}\r\n\r\n.filter-btn:hover,\r\n.filter-btn.active {\r\n    background: var(--accent-primary);\r\n    color: white;\r\n    border-color: var(--accent-primary);\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: var(--space-4) var(--space-3);\r\n    text-align: right;\r\n    font-weight: var(--font-weight-semibold);\r\n    color: var(--text-primary);\r\n    font-size: var(--font-size-sm);\r\n    border-bottom: 2px solid var(--border-primary);\r\n    white-space: nowrap;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 10;\r\n}\r\n\r\n.activities-table td {\r\n    padding: var(--space-4) var(--space-3);\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n    color: var(--text-secondary);\r\n    font-size: var(--font-size-sm);\r\n}\r\n\r\n.activities-table tr:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.activities-table tr:last-child td {\r\n    border-bottom: none;\r\n}\r\n\r\n.user-section {\r\n    position: relative;\r\n    z-index: 100;\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    border-radius: 8px;\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.user-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    background: linear-gradient(135deg, #667eea, #764ba2);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n    color: #f5f5f5;\r\n    white-space: nowrap;\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform 0.3s ease;\r\n    color: #a0aec0;\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n.user-menu {\r\n    position: absolute;\r\n    top: 100%;\r\n    right: auto;\r\n    left: 0;\r\n    margin-top: 8px;\r\n    background: #1a202c;\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    min-width: 180px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n    overflow: hidden;\r\n    animation: slideDown 0.3s ease;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.nav-buttons {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: center;\r\n}\r\n\r\n/* Arabic RTL alignment for navbar actions */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    min-width: 160px; /* unified width with global buttons */\r\n    justify-content: center;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    font-size: 14px;\r\n    letter-spacing: 0.2px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n}\r\n\r\n/* Compact button utility for small actions */\r\n.btn-compact {\r\n    min-width: 96px;\r\n    height: 40px;\r\n    padding: 8px 14px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.nav-btn:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: rgba(255, 255, 255, 0.3);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.nav-btn.active {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    border-color: #4f46e5;\r\n    color: #ffffff;\r\n    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.nav-btn.active:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n    margin: 24px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.submit-view {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n}\r\n\r\n.submit-header {\r\n    text-align: center;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n}\r\n\r\n.submit-header h2 {\r\n    color: #ffffff;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n}\r\n\r\n/* View Activities */\r\n.view-activities {\r\n    margin-top: 32px;\r\n    padding: 0 8px;\r\n}\r\n\r\n.view-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 32px 24px;\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));\r\n    border: 2px solid rgba(79, 70, 229, 0.3);\r\n    border-radius: 16px;\r\n    margin-bottom: 32px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.view-header::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);\r\n}\r\n\r\n.view-header-content {\r\n    text-align: center;\r\n    flex: 1;\r\n}\r\n\r\n.view-title {\r\n    color: #ffffff;\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    margin: 0 0 12px 0;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.view-description {\r\n    color: #e2e8f0;\r\n    font-size: 16px;\r\n    margin: 0;\r\n    opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.export-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #f59e0b, #d97706);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\r\n    min-width: 120px;\r\n    height: auto;\r\n}\r\n\r\n.export-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d97706, #b45309);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);\r\n}\r\n\r\n.export-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n    background: linear-gradient(135deg, #6b7280, #4b5563);\r\n}\r\n\r\n.refresh-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #10b981, #059669);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\r\n    min-width: 100px;\r\n    height: auto;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.refresh-btn svg.spinning {\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    from { transform: rotate(0deg); }\r\n    to { transform: rotate(360deg); }\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 24px;\r\n}\r\n\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n    padding: 0 4px;\r\n}\r\n\r\n.activity-card {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed);\r\n    transform: scaleX(0);\r\n    transition: transform 0.4s ease;\r\n}\r\n\r\n.activity-card:hover {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);\r\n    border-color: rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 2px solid rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.activity-header h4 {\r\n    color: #ffffff;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n    line-height: 1.3;\r\n    flex: 1;\r\n    margin-right: 16px;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-btn, .delete-btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 40px;\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: none;\r\n    text-align: center;\r\n}\r\n\r\n.edit-btn {\r\n    background: linear-gradient(135deg, #3182ce, #2c5aa0);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);\r\n}\r\n\r\n.edit-btn:hover {\r\n    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);\r\n}\r\n\r\n.delete-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);\r\n}\r\n\r\n.delete-btn:hover {\r\n    background: linear-gradient(135deg, #c53030, #a02626);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.activity-details {\r\n    margin-top: 16px;\r\n}\r\n\r\n.activity-details p {\r\n    color: #e2e8f0;\r\n    font-size: 15px;\r\n    margin: 12px 0;\r\n    line-height: 1.6;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.activity-details strong {\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    min-width: 100px;\r\n    display: inline-block;\r\n}\r\n\r\n.status-badge {\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    text-align: center;\r\n    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);\r\n    border: 1px solid rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.no-activities-message, .loading-message {\r\n    text-align: center;\r\n    padding: 48px 24px;\r\n    color: #cbd5e0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    border-radius: 16px;\r\n    margin: 24px 0;\r\n}\r\n\r\n.loading-message {\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n    border-color: rgba(79, 70, 229, 0.2);\r\n    animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes slideDown {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.user-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    padding: 12px 16px;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-menu-item:hover {\r\n    background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-menu-item svg {\r\n    color: #e53e3e;\r\n}\r\n\r\nspan {\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n}\r\n\r\n.view {\r\n    padding: 16px;\r\n    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);\r\n    border: 1px solid #3a3a4e;\r\n    border-radius: 20px;\r\n    max-width: 90%;\r\n    margin: 16px auto;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\nbutton {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    padding: 12px 24px;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 15px;\r\n    letter-spacing: 0.2px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n/* Make icon+label buttons look consistent */\r\nbutton svg { flex-shrink: 0; }\r\nbutton span { display: inline-block; }\r\n\r\n/* Inputs: improve placeholder visibility */\r\ninput::placeholder,\r\nselect::placeholder,\r\ntextarea::placeholder {\r\n    color: #cbd5e1; /* brighter placeholder */\r\n    opacity: 1;\r\n}\r\n\r\n/* Edge/Firefox vendor prefixes */\r\ninput::-ms-input-placeholder { color: #cbd5e1; }\r\ninput::-webkit-input-placeholder { color: #cbd5e1; }\r\ntextarea::-webkit-input-placeholder { color: #cbd5e1; }\r\nselect::-ms-input-placeholder { color: #cbd5e1; }\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton span {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.info {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n}\r\n\r\n.base-info-container,\r\n.activities-info-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    direction: rtl;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n    padding: 24px;\r\n}\r\n\r\n.base-info-label {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #f1f5f9;\r\n    margin-bottom: 16px;\r\n    text-align: right;\r\n    border-bottom: 2px solid #4f46e5;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.field-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 8px;\r\n    text-align: right;\r\n    display: block;\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect {\r\n    direction: rtl;\r\n    width: 100%;\r\n    max-width: 400px;\r\n    margin: 8px 0;\r\n    padding: 12px 16px;\r\n    background: rgba(255, 255, 255, 0.12);\r\n    color: #ffffff;\r\n    border: 2px solid rgba(255, 255, 255, 0.25);\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\ninput[type=\"text\"]:focus,\r\ninput[type=\"date\"]:focus,\r\nselect:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);\r\n    background: rgba(255, 255, 255, 0.18);\r\n    color: #ffffff;\r\n}\r\n\r\nselect {\r\n    cursor: pointer;\r\n}\r\n\r\nselect option {\r\n    background: #1a1a2e;\r\n    color: #f0f0f0;\r\n    padding: 8px;\r\n}\r\n.activity-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 8px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-item:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.activity-file-input {\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 12px;\r\n    width: fit-content;\r\n    max-width: 200px;\r\n    text-align: center;\r\n    padding: 12px 16px;\r\n    font-size: 14px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    color: #f0f0f0;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n    align-self: flex-start;\r\n}\r\n\r\n.activity-file-input:hover {\r\n    background: rgba(255, 255, 255, 0.15);\r\n    border-color: #4f46e5;\r\n}\r\n\r\n.activity-delete-button {\r\n    background: linear-gradient(135deg, #ef4444, #dc2626);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    margin: 8px 0;\r\n    padding: 10px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-weight: 600;\r\n    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.activity-delete-button:hover {\r\n    background: linear-gradient(135deg, #f87171, #ef4444);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .user-button {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n    \r\n    .info {\r\n        grid-template-columns: 1fr;\r\n        gap: 16px;\r\n    }\r\n    \r\n    .base-info-container,\r\n    .activities-info-container {\r\n        padding: 16px;\r\n    }\r\n    \r\n    .view {\r\n        margin: 8px;\r\n        padding: 12px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .user-name {\r\n        font-size: 12px;\r\n    }\r\n    \r\n    input[type=\"text\"],\r\n    input[type=\"date\"],\r\n    select {\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n}\r\n\r\n/* RTL Improvements */\r\n.view {\r\n    direction: rtl;\r\n}\r\n\r\n.navbar-content {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.navbar-text {\r\n    flex-direction: row-reverse;\r\n    text-align: right;\r\n}\r\n\r\n.nav-actions .nav-btn {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.view-header-content,\r\n.view-title,\r\n.view-description {\r\n    text-align: right;\r\n}\r\n\r\n.activities-list {\r\n    direction: rtl;\r\n}\r\n\r\nlabel {\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 4px;\r\n    display: block;\r\n}\r\n\r\n/* Inputs RTL */\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect,\r\ntextarea {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n/* Fine color and spacing tweaks for Arabic */\r\n.navbar {\r\n    border-color: #4b5563;\r\n}\r\n.nav-btn {\r\n    letter-spacing: 0.2px; /* tighter Arabic rhythm */\r\n}\r\n.view-header {\r\n    border-color: rgba(79, 70, 229, 0.35);\r\n}\r\n.activity-card {\r\n    padding-inline: 24px;\r\n}\r\n.activity-header h4 {\r\n    margin-left: 0;\r\n    margin-right: 15px; /* move spacing to the right for RTL */\r\n}\r\n.activity-details,\r\n.activity-details p {\r\n    text-align: right;\r\n}\r\n\r\n.splitter {\r\n    height: 2px;\r\n    background: linear-gradient(90deg, transparent, #4f46e5, transparent);\r\n    margin: 24px 0;\r\n    border-radius: 1px;\r\n}\r\n\r\n/* My Activities Section Styles */\r\n.my-activities-section {\r\n    margin: 30px 0;\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.toggle-activities-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.toggle-activities-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.toggle-icon {\r\n    font-size: 14px;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 15px;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.loading-message, .no-activities-message {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n}\r\n\r\n/* Activities Container */\r\n.activities-container {\r\n    margin-top: 20px;\r\n    padding: 0;\r\n}\r\n\r\n/* Loading and Empty States */\r\n.loading-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    gap: 16px;\r\n}\r\n\r\n.loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 3px solid rgba(255, 255, 255, 0.1);\r\n    border-top: 3px solid #4f46e5;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n}\r\n\r\n.no-activities-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n    color: #8892b0;\r\n    gap: 16px;\r\n}\r\n\r\n.empty-icon {\r\n    opacity: 0.6;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.no-activities-message h3 {\r\n    margin: 0;\r\n    color: #e6f1ff;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.no-activities-message p {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    opacity: 0.8;\r\n}\r\n\r\n/* Edit Modal - Clean Flexbox Design */\r\n.edit-modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.75);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.edit-modal {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 600px;\r\n    max-height: calc(100vh - 40px);\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.edit-modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 24px;\r\n    border-bottom: 1px solid #334155;\r\n    background: #0f172a;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-modal-header h3 {\r\n    margin: 0;\r\n    color: #f1f5f9;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: #64748b;\r\n    cursor: pointer;\r\n    padding: 8px;\r\n    border-radius: 6px;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.edit-form {\r\n    padding: 24px;\r\n    direction: rtl;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    overflow-y: auto;\r\n    flex: 1;\r\n    min-height: 0;\r\n}\r\n\r\n.form-row {\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-row.single {\r\n    flex-direction: column;\r\n}\r\n\r\n.form-row.double {\r\n    flex-direction: row;\r\n}\r\n\r\n.form-row.double .form-group {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* Table Styles */\r\n.activities-table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 16px;\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n    padding: 24px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.table-stats {\r\n    display: flex;\r\n    gap: 32px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.stat-item.clickable {\r\n    cursor: pointer;\r\n    padding: 12px 16px;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.stat-item.clickable:hover {\r\n    background: rgba(79, 70, 229, 0.1);\r\n    border-color: rgba(79, 70, 229, 0.3);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.stat-item.selected {\r\n    background: rgba(79, 70, 229, 0.2);\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.stat-item.selected .stat-number {\r\n    color: #6366f1;\r\n}\r\n\r\n.stat-item.selected .stat-label {\r\n    color: #c7d2fe;\r\n}\r\n\r\n.stat-number {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #4f46e5;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n    font-size: 14px;\r\n    color: #8892b0;\r\n    font-weight: 500;\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: 16px 12px;\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #e6f1ff;\r\n    font-size: 14px;\r\n    border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n    white-space: nowrap;\r\n}\r\n\r\n.activities-table td {\r\n    padding: 16px 12px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n}\r\n\r\n.activity-row {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-row:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n/* Column Specific Styles */\r\n.col-title {\r\n    min-width: 250px;\r\n}\r\n\r\n.activity-title h4 {\r\n    margin: 0 0 4px 0;\r\n    color: #e6f1ff;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 1.3;\r\n}\r\n\r\n.activity-description {\r\n    margin: 0;\r\n    color: #8892b0;\r\n    font-size: 13px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.owner-info {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.date-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.date-main {\r\n    color: #e6f1ff;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n}\r\n\r\n.date-year {\r\n    color: #8892b0;\r\n    font-size: 12px;\r\n}\r\n\r\n.status-badge {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    white-space: nowrap;\r\n}\r\n\r\n.status-indicator {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    background: currentColor;\r\n}\r\n\r\n/* Status Colors */\r\n.status-executed-paid {\r\n    background: rgba(34, 197, 94, 0.2);\r\n    color: #22c55e;\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n}\r\n\r\n.status-executed-unpaid {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.status-accepted {\r\n    background: rgba(16, 185, 129, 0.2);\r\n    color: #10b981;\r\n    border: 1px solid rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-rejected {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.status-needs-edit {\r\n    background: rgba(245, 158, 11, 0.2);\r\n    color: #f59e0b;\r\n    border: 1px solid rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.status-paid-not-executed {\r\n    background: rgba(168, 85, 247, 0.2);\r\n    color: #a855f7;\r\n    border: 1px solid rgba(168, 85, 247, 0.3);\r\n}\r\n\r\n.status-accepted-unpaid {\r\n    background: rgba(6, 182, 212, 0.2);\r\n    color: #06b6d4;\r\n    border: 1px solid rgba(6, 182, 212, 0.3);\r\n}\r\n\r\n.status-sent {\r\n    background: rgba(139, 92, 246, 0.2);\r\n    color: #8b5cf6;\r\n    border: 1px solid rgba(139, 92, 246, 0.3);\r\n}\r\n\r\n.governorate-name, .coordinator-name {\r\n    color: #ccd6f6;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 36px;\r\n    height: 36px;\r\n    border: none;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.edit-btn {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.action-btn.edit-btn:hover {\r\n    background: rgba(59, 130, 246, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n.action-btn.delete-btn {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.action-btn.delete-btn:hover {\r\n    background: rgba(239, 68, 68, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n/* File Download Styles */\r\n.files-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n    min-width: 120px;\r\n}\r\n\r\n.file-buttons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.file-download-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 10px;\r\n    background: rgba(34, 197, 94, 0.15);\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n    border-radius: 6px;\r\n    color: #22c55e;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n    min-height: 28px;\r\n    max-width: 140px;\r\n}\r\n\r\n.file-download-btn:hover {\r\n    background: rgba(34, 197, 94, 0.25);\r\n    border-color: rgba(34, 197, 94, 0.5);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);\r\n}\r\n\r\n.file-download-btn svg {\r\n    flex-shrink: 0;\r\n    color: #22c55e;\r\n}\r\n\r\n.file-name {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.no-files {\r\n    color: #64748b;\r\n    font-size: 12px;\r\n    font-style: italic;\r\n    text-align: center;\r\n    padding: 8px;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n}\r\n\r\n@keyframes slideUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Edit Form Styles */\r\n.edit-form {\r\n    direction: rtl;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-group label {\r\n    color: #cbd5e1;\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    margin-bottom: 0;\r\n    text-align: right;\r\n}\r\n\r\n.form-input, .form-textarea, .form-select {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    border: 1px solid #475569;\r\n    border-radius: 6px;\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n    font-size: 14px;\r\n    font-family: inherit;\r\n    transition: all 0.2s ease;\r\n    direction: rtl;\r\n    text-align: right;\r\n    box-sizing: border-box;\r\n    outline: none;\r\n}\r\n\r\n.form-input:focus, .form-textarea:focus, .form-select:focus {\r\n    border-color: #3b82f6;\r\n    background: #1e293b;\r\n    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.form-input::placeholder, .form-textarea::placeholder {\r\n    color: #94a3b8;\r\n}\r\n\r\n.form-select {\r\n    cursor: pointer;\r\n    appearance: none;\r\n    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23f1f5f9\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>');\r\n    background-repeat: no-repeat;\r\n    background-position: left 12px center;\r\n    background-size: 12px;\r\n    padding-left: 32px;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n.form-textarea {\r\n    min-height: 100px;\r\n    resize: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 24px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #475569;\r\n}\r\n\r\n.save-btn, .cancel-btn {\r\n    padding: 10px 20px;\r\n    border-radius: 6px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    border: 1px solid transparent;\r\n    outline: none;\r\n}\r\n\r\n.save-btn {\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.save-btn:hover {\r\n    background: #2563eb;\r\n    border-color: #2563eb;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #94a3b8;\r\n    border-color: #475569;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n}\r\n\r\n    /* Responsive Design for My Activities */\r\n    @media (max-width: 768px) {\r\n        .view-activities {\r\n            padding: 0 4px;\r\n        }\r\n        \r\n        .view-header {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            padding: 24px 16px;\r\n            margin-bottom: 24px;\r\n        }\r\n        \r\n        .view-header-content {\r\n            order: 1;\r\n        }\r\n        \r\n        .refresh-btn {\r\n            order: 2;\r\n            align-self: center;\r\n            min-width: 120px;\r\n            padding: 10px 16px;\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .view-title {\r\n            font-size: 24px;\r\n        }\r\n        \r\n        .view-description {\r\n            font-size: 14px;\r\n        }\r\n        \r\n        .activities-grid {\r\n            grid-template-columns: 1fr;\r\n            gap: 16px;\r\n            padding: 0;\r\n        }\r\n        \r\n        .activity-card {\r\n            padding: 20px;\r\n            margin: 0;\r\n        }\r\n        \r\n        .activity-header {\r\n            flex-direction: column;\r\n            gap: 12px;\r\n            align-items: stretch;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 18px;\r\n            margin-right: 0;\r\n        }\r\n        \r\n        .activity-actions {\r\n            justify-content: flex-end;\r\n            gap: 8px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 8px 12px;\r\n            font-size: 13px;\r\n            min-width: 96px;\r\n            height: 40px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 14px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 4px;\r\n        }\r\n        \r\n        .activity-details strong {\r\n            min-width: auto;\r\n        }\r\n        \r\n        .no-activities-message, .loading-message {\r\n            padding: 32px 16px;\r\n            font-size: 16px;\r\n        }\r\n    }\r\n    \r\n    @media (max-width: 480px) {\r\n        .activity-card {\r\n            padding: 12px;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 16px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 5px 10px;\r\n            font-size: 11px;\r\n        }\r\n        \r\n        .edit-modal {\r\n            width: 95%;\r\n            max-width: none;\r\n            margin: 20px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .edit-modal-header {\r\n            padding: 20px 24px;\r\n        }\r\n        \r\n        .edit-form {\r\n            padding: 24px;\r\n            gap: 20px;\r\n        }\r\n        \r\n        .form-row {\r\n            grid-template-columns: 1fr;\r\n            gap: 20px;\r\n            padding: 12px;\r\n            margin-bottom: 16px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .form-row:hover {\r\n            transform: none;\r\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        }\r\n        \r\n        .form-input, .form-textarea, .form-select {\r\n            padding: 16px 20px;\r\n            font-size: 16px;\r\n            border-radius: 14px;\r\n        }\r\n        \r\n        .form-textarea {\r\n            min-height: 100px;\r\n            padding-top: 18px;\r\n            padding-bottom: 18px;\r\n        }\r\n        \r\n        .form-group label {\r\n            font-size: 13px;\r\n            margin-right: 2px;\r\n        }\r\n        \r\n        .form-select {\r\n            padding-left: 35px;\r\n            background-position: calc(100% - 18px) calc(1em + 2px), \r\n                                 calc(100% - 13px) calc(1em + 2px);\r\n        }\r\n        \r\n        .form-actions {\r\n            flex-direction: column-reverse;\r\n            align-items: stretch;\r\n            gap: 12px;\r\n            padding: 20px 0 0 0;\r\n        }\r\n        \r\n        .save-btn, .cancel-btn {\r\n            width: 100%;\r\n            padding: 14px;\r\n            min-width: auto;\r\n        }\r\n    }\r\n\r\n/* Modal Overlay Styles */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    backdrop-filter: blur(5px);\r\n}\r\n\r\n.modal-content {\r\n    background: #1a1a2e;\r\n    border-radius: 20px;\r\n    width: 90%;\r\n    max-width: 500px;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n    animation: modalSlideIn 0.3s ease;\r\n    border: 2px solid #4facfe;\r\n    position: relative;\r\n    z-index: 1001;\r\n    padding: 10px;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-50px) scale(0.9);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0) scale(1);\r\n    }\r\n}\r\n\r\n/* Modal Header Styles */\r\n.modal-header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    border-radius: 20px 20px 0 0;\r\n}\r\n\r\n.modal-header h3 {\r\n    margin: 0;\r\n    font-size: 1.3rem;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: white;\r\n    font-size: 1.5rem;\r\n    cursor: pointer;\r\n    padding: 0;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: background 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Account Settings Modal Styles */\r\n.account-settings-modal {\r\n    max-width: 600px;\r\n    width: 90%;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n}\r\n\r\n.account-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 8px;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n    transition: all 0.3s ease;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #4facfe;\r\n    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);\r\n    background: #374151;\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #a0aec0;\r\n}\r\n\r\n.password-section {\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.password-section h4 {\r\n    margin: 0 0 20px 0;\r\n    color: #4facfe;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.save-btn {\r\n    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.save-btn:hover:not(:disabled) {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);\r\n}\r\n\r\n.save-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n    color: #f5f5f5;\r\n}\r\n\r\n/* Responsive styles for account modal */\r\n@media (max-width: 768px) {\r\n    .account-settings-modal {\r\n        width: 95%;\r\n        margin: 20px;\r\n    }\r\n    \r\n    .form-actions {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .save-btn,\r\n    .cancel-btn {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n/* PIN Confirmation Modal Styles */\r\n.pin-modal {\r\n    max-width: 450px;\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #4a5568;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.pin-modal .modal-header {\r\n    background: linear-gradient(135deg, #2d3748, #4a5568);\r\n    border-bottom: 2px solid #4a5568;\r\n    border-radius: 14px 14px 0 0;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.pin-modal .modal-header h3 {\r\n    color: #4fc3f7;\r\n    margin: 0;\r\n    font-size: 1.4rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.pin-modal .modal-body {\r\n    padding: 25px;\r\n}\r\n\r\n.pin-message {\r\n    color: #e2e8f0;\r\n    font-size: 1.1rem;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n    line-height: 1.6;\r\n}\r\n\r\n.pin-modal .form-group {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.pin-modal .form-group label {\r\n    color: #4fc3f7;\r\n    font-weight: 600;\r\n    margin-bottom: 8px;\r\n    display: block;\r\n}\r\n\r\n.pin-modal .form-control {\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    color: #e2e8f0;\r\n    padding: 12px 16px;\r\n    border-radius: 8px;\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.pin-modal .form-control:focus {\r\n    border-color: #4fc3f7;\r\n    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);\r\n    outline: none;\r\n}\r\n\r\n.pin-modal .modal-footer {\r\n    padding: 20px 25px;\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.confirm-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #c53030, #9c2626);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.confirm-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.pin-modal .cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 10px 20px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.pin-modal .cancel-btn:hover {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    transform: translateY(-2px);\r\n}\r\n</style>"], "mappings": ";;;;;;;;;AACA,eAAe;EACXA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,WAAW,EAAE,QAAQ;MACrBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE;MACb,CAAC;MACDC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,mBAAmB,EAAE;QACjBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd;IACJ,CAAC;EACL,CAAC;EACDC,QAAQ,EAAE;IACNC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAChB,cAAc,EAAE;QACtB,OAAO,IAAI,CAACN,YAAY;MAC5B;MACA,OAAO,IAAI,CAACA,YAAY,CAACuB,MAAM,CAACJ,QAAO,IAAKA,QAAQ,CAACK,KAAI,KAAM,IAAI,CAAClB,cAAc,CAAC;IACvF;EACJ,CAAC;EACDmB,OAAO,EAAE;IACLC,YAAYA,CAACF,KAAK,EAAE;MAChB;MACA,IAAI,CAAClB,cAAa,GAAI,IAAI,CAACA,cAAa,KAAMkB,KAAI,GAAI,IAAG,GAAIA,KAAK;IACtE,CAAC;IACDG,cAAcA,CAAA,EAAG;MACb,IAAI,CAAC5B,YAAW,GAAI,CAAC,IAAI,CAACA,YAAY;IAC1C,CAAC;IACD6B,MAAMA,CAAA,EAAG;MACLC,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;MACrCD,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;MACpCD,YAAY,CAACC,UAAU,CAAC,eAAe,CAAC;MACxC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC/B,CAAC;IACDC,SAASA,CAAA,EAAG;MACR,IAAI,CAAClC,YAAW,GAAI,KAAK;MACzB,IAAI,CAACgC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC/B,CAAC;IACDE,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAACnC,YAAW,GAAI,KAAK;MACzB,IAAI,CAACS,WAAW,CAACC,QAAO,GAAI,IAAI,CAACX,IAAI,EAAEqC,SAAQ,IAAK,EAAE;MACtD,IAAI,CAAC3B,WAAW,CAACK,OAAM,GAAIgB,YAAY,CAACO,OAAO,CAAC,eAAe,KAAK,EAAE;MACtE,IAAI,CAAC5B,WAAW,CAACE,eAAc,GAAI,EAAE;MACrC,IAAI,CAACF,WAAW,CAACG,WAAU,GAAI,EAAE;MACjC,IAAI,CAACH,WAAW,CAACI,eAAc,GAAI,EAAE;MACrC,IAAI,CAACL,mBAAkB,GAAI,IAAI;IACnC,CAAC;IACD8B,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC9B,mBAAkB,GAAI,KAAK;MAChC,IAAI,CAACC,WAAU,GAAI;QACfC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE;MACb,CAAC;IACL,CAAC;IACD,MAAMyB,qBAAqBA,CAAA,EAAG;MAC1B;MACA,IAAI,CAAC,IAAI,CAAC9B,WAAW,CAACC,QAAQ,CAAC8B,IAAI,CAAC,CAAC,EAAE;QACnC,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;UAClBC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,yBAAyB;UAC/BC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,IAAI,CAACpC,WAAW,CAACG,WAAU,IAAK,IAAI,CAACH,WAAW,CAACG,WAAU,KAAM,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;QACnG,MAAM,IAAI,CAAC4B,KAAK,CAACC,IAAI,CAAC;UAClBC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,4CAA4C;UAClDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,IAAI,CAACpC,WAAW,CAACG,WAAU,IAAK,IAAI,CAACH,WAAW,CAACG,WAAW,CAACkC,MAAK,GAAI,CAAC,EAAE;QACzE,MAAM,IAAI,CAACL,KAAK,CAACC,IAAI,CAAC;UAClBC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,0CAA0C;UAChDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,CAAC9B,eAAc,GAAI,IAAI;MAE3B,IAAI;QACA,MAAMgC,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMW,UAAS,GAAI;UACfZ,SAAS,EAAE,IAAI,CAAC3B,WAAW,CAACC,QAAQ,CAAC8B,IAAI,CAAC,CAAC;UAC3CS,QAAQ,EAAE,IAAI,CAACxC,WAAW,CAACK,OAAO,CAAC0B,IAAI,CAAC;QAC5C,CAAC;;QAED;QACA,IAAI,IAAI,CAAC/B,WAAW,CAACG,WAAW,EAAE;UAC9BoC,UAAU,CAACE,gBAAe,GAAI,IAAI,CAACzC,WAAW,CAACE,eAAe;UAC9DqC,UAAU,CAACG,YAAW,GAAI,IAAI,CAAC1C,WAAW,CAACG,WAAW;QAC1D;QAEA,MAAMwC,QAAO,GAAI,MAAMC,KAAK,CAAC,6CAA6C,EAAE;UACxEC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUR,KAAK;UACpC,CAAC;UACDS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACV,UAAU;QACnC,CAAC,CAAC;QAEF,IAAII,QAAQ,CAACO,EAAE,EAAE;UACb,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;UAErB;UACA,IAAI,CAAC7D,IAAI,CAACqC,SAAQ,GAAI,IAAI,CAAC3B,WAAW,CAACC,QAAQ,CAAC8B,IAAI,CAAC,CAAC;UACtDV,YAAY,CAAC+B,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3D,IAAI,CAAC,CAAC;UAC5D+B,YAAY,CAAC+B,OAAO,CAAC,eAAe,EAAE,IAAI,CAACpD,WAAW,CAACK,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAAC;UAEtE,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;YAClBC,KAAK,EAAE,kBAAkB;YACzBC,IAAI,EAAE,+BAA+B;YACrCC,IAAI,EAAE;UACV,CAAC,CAAC;UAEF,IAAI,CAACP,oBAAoB,CAAC,CAAC;QAC/B,OAAO;UACH,MAAMwB,SAAQ,GAAI,MAAMV,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACvC,MAAM,IAAI,CAACnB,KAAK,CAACC,IAAI,CAAC;YAClBC,KAAK,EAAE,gBAAgB;YACvBC,IAAI,EAAEkB,SAAS,CAACC,KAAI,IAAK,6BAA6B;YACtDlB,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,EAAE,OAAOkB,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAM,IAAI,CAACtB,KAAK,CAACC,IAAI,CAAC;UAClBC,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,oCAAoC;UAC1CC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,UAAU;QACN,IAAI,CAAC9B,eAAc,GAAI,KAAK;MAChC;IACJ,CAAC;IACD,MAAMkD,QAAQA,CAAA,EAAG;MACb,IAAI;QACA;QACA,IAAI,CAAC,IAAI,CAAC3D,eAAe,CAACkC,IAAI,CAAC,CAAC,EAAE;UAC9B,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;YAClBC,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,8BAA8B;YACpCC,IAAI,EAAE;UACV,CAAC,CAAC;UACF;QACJ;;QAEA;QACA,MAAMqB,aAAY,GAAIC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;QAEjE,IAAIF,aAAa,CAACpB,MAAK,KAAM,CAAC,EAAE;UAC5B,MAAM,IAAI,CAACL,KAAK,CAACC,IAAI,CAAC;YAClBC,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,gCAAgC;YACtCC,IAAI,EAAE;UACV,CAAC,CAAC;UACF;QACJ;QAEA,MAAME,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMvB,OAAM,GAAIgB,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;QAErD,IAAI,CAACU,KAAK,EAAE;UACR,IAAI,CAACsB,MAAM,CAACN,KAAK,CAAC,yBAAyB,CAAC;UAC5C,IAAI,CAAC/B,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;UAC3B;QACJ;QAEA,MAAMnC,UAAS,GAAI,EAAE;QACrB,IAAIwE,SAAQ,GAAI,KAAK;;QAErB;QACA,KAAK,IAAIC,KAAI,GAAI,CAAC,EAAEA,KAAI,GAAIL,aAAa,CAACpB,MAAM,EAAEyB,KAAK,EAAE,EAAE;UACvD,MAAMC,IAAG,GAAIN,aAAa,CAACK,KAAK,CAAC;UACjC,MAAME,MAAK,GAAID,IAAI,CAACJ,gBAAgB,CAAC,iBAAiB,CAAC;UACvD,MAAMM,SAAQ,GAAID,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEnC,IAAI,CAAC,CAAC;UAC1C,MAAMG,KAAI,GAAI8B,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEnC,IAAI,CAAC,CAAC;UACtC,MAAMoC,gBAAe,GAAIH,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEnC,IAAI,CAAC,CAAC;UACjD,MAAMqC,YAAW,GAAIJ,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK;UACrC,MAAMlD,KAAI,GAAIgD,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK;;UAE9B;UACA,IAAI,CAACD,SAAQ,IAAK,CAAC/B,KAAI,IAAK,CAACkC,YAAW,IAAK,CAACpD,KAAK,EAAE;YACjD,IAAI,CAACgB,KAAK,CAACC,IAAI,CAAC;cACZC,KAAK,EAAE,iBAAiB;cACxBC,IAAI,EAAE,4CAA4C2B,KAAI,GAAI,CAAC,EAAE;cAC7D1B,IAAI,EAAE;YACV,CAAC,CAAC;YACFyB,SAAQ,GAAI,IAAI;YAChB;UACJ;UAEA,IAAIQ,MAAK,GAAI,IAAI;;UAEjB;UACA,IAAIN,IAAI,CAACO,YAAY,EAAE;YACnB,IAAI;cACA,MAAMC,QAAO,GAAI,IAAIC,QAAQ,CAAC,CAAC;cAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEV,IAAI,CAACO,YAAY,CAAC;cAE1C,MAAMI,cAAa,GAAI,MAAM9B,KAAK,CAAC,qCAAqC,EAAE;gBACtEC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE;kBACL,eAAe,EAAE,UAAUR,KAAK;gBACpC,CAAC;gBACDS,IAAI,EAAEwB;cACV,CAAC,CAAC;cAEF,IAAIG,cAAc,CAACxB,EAAE,EAAE;gBACnB,MAAMyB,YAAW,GAAI,MAAMD,cAAc,CAACvB,IAAI,CAAC,CAAC;gBAChD,IAAIwB,YAAY,CAACC,IAAG,IAAKD,YAAY,CAACC,IAAI,CAACC,EAAE,EAAE;kBAC3CR,MAAK,GAAIM,YAAY,CAACC,IAAI,CAACC,EAAE;gBACjC;cACJ,OAAO;gBACH,MAAMxB,SAAQ,GAAI,MAAMqB,cAAc,CAACvB,IAAI,CAAC,CAAC;gBAC7C,IAAI,CAACS,MAAM,CAACN,KAAK,CAAC,6BAA6BQ,KAAI,GAAI,CAAC,KAAKT,SAAS,CAACC,KAAI,IAAK,eAAe,EAAE,CAAC;gBAClGO,SAAQ,GAAI,IAAI;gBAChB;cACJ;YACJ,EAAE,OAAOiB,WAAW,EAAE;cAClBvB,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEwB,WAAW,CAAC;cAChD,IAAI,CAAClB,MAAM,CAACN,KAAK,CAAC,6BAA6BQ,KAAI,GAAI,CAAC,EAAE,CAAC;cAC3DD,SAAQ,GAAI,IAAI;cAChB;YACJ;UACJ;UAEAxE,UAAU,CAACmC,IAAI,CAAC;YACZuD,UAAU,EAAEd,SAAS;YACrB/B,KAAK,EAAEA,KAAK;YACZ8C,iBAAiB,EAAEb,gBAAe,IAAK,EAAE;YACzCc,aAAa,EAAEb,YAAY;YAC3BpD,KAAK,EAAEA,KAAK;YACZkE,OAAO,EAAEb;UACb,CAAC,CAAC;QACN;QAEA,IAAIR,SAAS,EAAE;;QAEf;QACA,MAAMsB,cAAa,GAAI;UACnBC,gBAAgB,EAAE,IAAI,CAACvF,eAAe,CAACkC,IAAI,CAAC,CAAC;UAC7C1C,UAAU,EAAEA;QAChB,CAAC;;QAED;QACA,MAAMsD,QAAO,GAAI,MAAMC,KAAK,CAAC,qCAAqC,EAAE;UAChEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,YAAY,EAAEjC;UAClB,CAAC;UACD0C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACkC,cAAc;QACvC,CAAC,CAAC;QAEF,IAAIxC,QAAQ,CAACO,EAAE,EAAE;UACb,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACrB,IAAI,CAACS,MAAM,CAACyB,OAAO,CAAC,0BAA0B,CAAC;;UAE/C;UACA,IAAI,CAACxF,eAAc,GAAI,EAAE;;UAEzB;UACA4D,aAAa,CAAC6B,OAAO,CAACvB,IAAG,IAAKA,IAAI,CAACwB,MAAM,CAAC,CAAC,CAAC;;UAE5C;UACA,IAAI,IAAI,CAAC5F,gBAAgB,EAAE;YACvB,IAAI,CAAC6F,iBAAiB,CAAC,CAAC;UAC5B;QACJ,OAAO;UACH,IAAInC,SAAQ,GAAI,CAAC,CAAC;UAClB,IAAI;YACAA,SAAQ,GAAI,MAAMV,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACrC,EAAE,OAAOsC,SAAS,EAAE;YAChBlC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEmC,SAAS,CAAC;UAC5D;UACA,IAAI,CAAC7B,MAAM,CAACN,KAAK,CAAC,0BAA0BD,SAAS,CAACqC,OAAM,IAAK,eAAe,EAAE,CAAC;QACvF;MAEJ,EAAE,OAAOpC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACM,MAAM,CAACN,KAAK,CAAC,uDAAuD,CAAC;MAC9E;IACJ,CAAC;IACD,MAAMkC,iBAAiBA,CAAA,EAAG;MACtB,IAAI,CAAC/F,iBAAgB,GAAI,IAAI;MAC7B,IAAI,CAACD,YAAW,GAAI,EAAE,EAAE;;MAExB,IAAI;QACA,MAAM8C,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAEhD,IAAI,CAACU,KAAK,EAAE;UACR,IAAI,CAACsB,MAAM,CAACN,KAAK,CAAC,yBAAyB,CAAC;UAC5C,IAAI,CAAC/B,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;UAC3B;QACJ;QAEA,MAAMmB,QAAO,GAAI,MAAMC,KAAK,CAAC,oCAAoC,EAAE;UAC/DE,OAAO,EAAE;YACL,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEF,IAAIK,QAAQ,CAACO,EAAE,EAAE;UACb,MAAM9D,IAAG,GAAI,MAAMuD,QAAQ,CAACQ,IAAI,CAAC,CAAC;;UAElC;UACA,IAAI/D,IAAI,CAACC,UAAS,IAAKsG,KAAK,CAACC,OAAO,CAACxG,IAAI,CAACC,UAAU,CAAC,EAAE;YACnD,IAAI,CAACG,YAAW,GAAIJ,IAAI,CAACC,UAAU,CAACwG,GAAG,CAAClF,QAAO,KAAM;cACjD,GAAGA,QAAQ;cACXmF,eAAe,EAAE;gBACbjB,EAAE,EAAElE,QAAQ,CAACoF,aAAa;gBAC1BC,WAAW,EAAErF,QAAQ,CAACqF,WAAW;gBACjCZ,gBAAgB,EAAEzE,QAAQ,CAACyE,gBAAgB;gBAC3Ca,UAAU,EAAEtF,QAAQ,CAACsF;cACzB;YACJ,CAAC,CAAC,CAAC;UACP;QACJ,OAAO,IAAItD,QAAQ,CAACuD,MAAK,KAAM,GAAG,EAAE;UAChC,IAAI,CAACtC,MAAM,CAACN,KAAK,CAAC,iDAAiD,CAAC;UACpEjC,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;UACrCD,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;UACpC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,IAAImB,QAAQ,CAACuD,MAAK,KAAM,GAAG,EAAE;UAChC;UACA,IAAI,CAAC1G,YAAW,GAAI,EAAE;QAC1B,OAAO;UACH,MAAM6D,SAAQ,GAAI,MAAMV,QAAQ,CAACQ,IAAI,CAAC,CAAC,CAACgD,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;UACzD5C,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAED,SAAS,CAAC;UACvD,IAAI,CAACO,MAAM,CAACN,KAAK,CAAC,0BAA0BD,SAAS,CAACqC,OAAM,IAAK,eAAe,EAAE,CAAC;QACvF;MACJ,EAAE,OAAOpC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAIA,KAAK,CAACnE,IAAG,KAAM,WAAU,IAAKmE,KAAK,CAACoC,OAAO,CAACU,QAAQ,CAAC,OAAO,CAAC,EAAE;UAC/D,IAAI,CAACxC,MAAM,CAACN,KAAK,CAAC,2EAA2E,CAAC;QAClG,OAAO;UACH,IAAI,CAACM,MAAM,CAACN,KAAK,CAAC,uDAAuD,CAAC;QAC9E;MACJ,UAAU;QACN,IAAI,CAAC7D,iBAAgB,GAAI,KAAK;MAClC;IACJ,CAAC;IACD4G,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC1G,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9C,IAAI,IAAI,CAACA,gBAAe,IAAK,IAAI,CAACH,YAAY,CAAC6C,MAAK,KAAM,CAAC,EAAE;QACzD,IAAI,CAACmD,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACDc,YAAYA,CAAC3F,QAAQ,EAAE;MACnB,IAAI,CAAC4F,wBAAwB,CAAC,MAAM,EAAE5F,QAAQ,EAAE,MAAM;QAClD,IAAI,CAAC6F,aAAa,CAAC7F,QAAQ,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACD6F,aAAaA,CAAC7F,QAAQ,EAAE;MACpB,IAAI,CAACjB,eAAc,GAAI;QAAE,GAAGiB;MAAS,CAAC;MACtC;MACA,IAAI,IAAI,CAACjB,eAAe,CAACuF,aAAa,EAAE;QACpC,MAAMwB,IAAG,GAAI,IAAIC,IAAI,CAAC,IAAI,CAAChH,eAAe,CAACuF,aAAa,CAAC;QACzD;QACA,MAAM0B,IAAG,GAAIF,IAAI,CAACG,WAAW,CAAC,CAAC;QAC/B,MAAMC,KAAI,GAAIC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAE,GAAIH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,IAAI,CAACtH,eAAe,CAACuF,aAAY,GAAI,GAAG0B,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;MAClE;IACJ,CAAC;IACD,MAAME,YAAYA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACzH,eAAe,EAAE;MAE3B,IAAI;QACA,MAAM4C,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMvB,OAAM,GAAIgB,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;QAErD,MAAMe,QAAO,GAAI,MAAMC,KAAK,CAAC,sCAAsC,IAAI,CAAClD,eAAe,CAACmF,EAAE,EAAE,EAAE;UAC1FhC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,YAAY,EAAEjC;UAClB,CAAC;UACD0C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACjB8B,UAAU,EAAE,IAAI,CAACrF,eAAe,CAACqF,UAAU;YAC3C7C,KAAK,EAAE,IAAI,CAACxC,eAAe,CAACwC,KAAK;YACjC8C,iBAAiB,EAAE,IAAI,CAACtF,eAAe,CAACsF,iBAAiB;YACzDC,aAAa,EAAE,IAAI,CAACvF,eAAe,CAACuF,aAAa;YACjDjE,KAAK,EAAE,IAAI,CAACtB,eAAe,CAACsB;UAChC,CAAC;QACL,CAAC,CAAC;QAEF,IAAI2B,QAAQ,CAACO,EAAE,EAAE;UACb,MAAMkE,eAAc,GAAI,MAAMzE,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAC7C;UACA,MAAMW,KAAI,GAAI,IAAI,CAACtE,YAAY,CAAC6H,SAAS,CAACC,CAAA,IAAKA,CAAC,CAACzC,EAAC,KAAMuC,eAAe,CAACvC,EAAE,CAAC;UAC3E,IAAIf,KAAI,KAAM,CAAC,CAAC,EAAE;YACd,IAAI,CAACtE,YAAY,CAACsE,KAAK,IAAI;cAAE,GAAGsD,eAAe;cAAEtB,eAAe,EAAE,IAAI,CAACtG,YAAY,CAACsE,KAAK,CAAC,CAACgC;YAAgB,CAAC;UAChH;UACA,IAAI,CAACpG,eAAc,GAAI,IAAI;UAC3B,IAAI,CAACkE,MAAM,CAACyB,OAAO,CAAC,wBAAwB,CAAC;QACjD,OAAO;UACH,IAAI,CAACzB,MAAM,CAACN,KAAK,CAAC,qBAAqB,CAAC;QAC5C;MACJ,EAAE,OAAOA,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACM,MAAM,CAACN,KAAK,CAAC,4BAA4B,CAAC;MACnD;IACJ,CAAC;IACDiE,UAAUA,CAAA,EAAG;MACT,IAAI,CAAC7H,eAAc,GAAI,IAAI;IAC/B,CAAC;IACD8H,cAAcA,CAACC,UAAU,EAAE;MACvB,MAAM9G,QAAO,GAAI,IAAI,CAACnB,YAAY,CAACkI,IAAI,CAACJ,CAAA,IAAKA,CAAC,CAACzC,EAAC,KAAM4C,UAAU,CAAC;MACjE,IAAI,CAAClB,wBAAwB,CAAC,QAAQ,EAAE5F,QAAQ,EAAE,YAAY;QAC1D,MAAM,IAAI,CAACgH,qBAAqB,CAACF,UAAU,CAAC;MAChD,CAAC,CAAC;IACN,CAAC;IACD,MAAME,qBAAqBA,CAACF,UAAU,EAAE;MACpC,IAAI;QACA,MAAMnF,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMvB,OAAM,GAAIgB,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;QAErD,MAAMe,QAAO,GAAI,MAAMC,KAAK,CAAC,sCAAsC6E,UAAU,EAAE,EAAE;UAC7E5E,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,YAAY,EAAEjC;UAClB;QACJ,CAAC,CAAC;QAEF,IAAIsC,QAAQ,CAACO,EAAE,EAAE;UACb,IAAI,CAAC1D,YAAW,GAAI,IAAI,CAACA,YAAY,CAACuB,MAAM,CAACuG,CAAA,IAAKA,CAAC,CAACzC,EAAC,KAAM4C,UAAU,CAAC;UACtE,IAAI,CAAC7D,MAAM,CAACyB,OAAO,CAAC,sBAAsB,CAAC;QAC/C,OAAO;UACH,IAAI,CAACzB,MAAM,CAACN,KAAK,CAAC,mBAAmB,CAAC;QAC1C;MACJ,EAAE,OAAOA,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACM,MAAM,CAACN,KAAK,CAAC,0BAA0B,CAAC;MACjD;IACJ,CAAC;IACDsE,cAAcA,CAAC1B,MAAM,EAAE;MACnB,MAAM2B,SAAQ,GAAI;QACd,WAAW,EAAE,sBAAsB;QACnC,eAAe,EAAE,wBAAwB;QACzC,OAAO,EAAE,iBAAiB;QAC1B,OAAO,EAAE,iBAAiB;QAC1B,aAAa,EAAE,mBAAmB;QAClC,eAAe,EAAE,0BAA0B;QAC3C,eAAe,EAAE,wBAAwB;QACzC,MAAM,EAAE;MACZ,CAAC;MACD,OAAOA,SAAS,CAAC3B,MAAM,KAAK,gBAAgB;IAChD,CAAC;IACD4B,eAAeA,CAAA,EAAG;MACd,MAAMC,WAAU,GAAIrE,QAAQ,CAACsE,aAAa,CAAC,KAAK,CAAC;MACjDD,WAAW,CAACE,SAAQ,GAAI,eAAe;MACvC,MAAMC,aAAY,GAAIxE,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACrDE,aAAa,CAACC,IAAG,GAAI,MAAM;MAC3BD,aAAa,CAACE,WAAU,GAAI,kBAAkB;MAC9CF,aAAa,CAACD,SAAQ,GAAI,gBAAgB;MAC1CF,WAAW,CAACM,WAAW,CAACH,aAAa,CAAC;MACtC,MAAMI,aAAY,GAAI5E,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACrDM,aAAa,CAACH,IAAG,GAAI,MAAM;MAC3BG,aAAa,CAACF,WAAU,GAAI,cAAc;MAC1CE,aAAa,CAACL,SAAQ,GAAI,gBAAgB;MAC1CF,WAAW,CAACM,WAAW,CAACC,aAAa,CAAC;MACtC,MAAMC,wBAAuB,GAAI7E,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MAChEO,wBAAwB,CAACJ,IAAG,GAAI,MAAM;MACtCI,wBAAwB,CAACH,WAAU,GAAI,iBAAiB;MACxDG,wBAAwB,CAACN,SAAQ,GAAI,gBAAgB;MACrDF,WAAW,CAACM,WAAW,CAACE,wBAAwB,CAAC;MACjD,MAAMC,iBAAgB,GAAI9E,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACzDQ,iBAAiB,CAACC,WAAU,GAAI,cAAc;MAC9CV,WAAW,CAACM,WAAW,CAACG,iBAAiB,CAAC;MAC1C,MAAMpE,YAAW,GAAIV,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACpD5D,YAAY,CAAC+D,IAAG,GAAI,MAAM;MAC1B/D,YAAY,CAAC6D,SAAQ,GAAI,gBAAgB;MACzCF,WAAW,CAACM,WAAW,CAACjE,YAAY,CAAC;MACrC,MAAMsE,kBAAiB,GAAIhF,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MAC1DU,kBAAkB,CAACD,WAAU,GAAI,aAAa;MAC9CV,WAAW,CAACM,WAAW,CAACK,kBAAkB,CAAC;MAC3C,MAAMC,kBAAiB,GAAIjF,QAAQ,CAACsE,aAAa,CAAC,QAAQ,CAAC;MAC3DW,kBAAkB,CAACV,SAAQ,GAAI,gBAAgB;MAC/C,MAAMW,MAAK,GACX,CAAC,WAAW,EAAG,eAAe,EAAE,OAAO,EAAE,OAAO,EAChD,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,MAAK,CACrD;MACDA,MAAM,CAACtD,OAAO,CAACtE,KAAI,IAAK;QACpB,MAAM6H,MAAK,GAAInF,QAAQ,CAACsE,aAAa,CAAC,QAAQ,CAAC;QAC/Ca,MAAM,CAAC3E,KAAI,GAAIlD,KAAK;QACpB6H,MAAM,CAACJ,WAAU,GAAIzH,KAAK;QAC1B2H,kBAAkB,CAACN,WAAW,CAACQ,MAAM,CAAC;MAC1C,CAAC,CAAC;MACF,MAAMC,iBAAgB,GAAIpF,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACzDc,iBAAiB,CAACL,WAAU,GAAI,YAAY;MAC5C,MAAMM,iBAAgB,GAAIrF,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACzDe,iBAAiB,CAACd,SAAQ,GAAI,qBAAqB;MACnDc,iBAAiB,CAACN,WAAU,GAAI,UAAU;;MAE1C;MACA,MAAMO,eAAc,GAAItF,QAAQ,CAACsE,aAAa,CAAC,OAAO,CAAC;MACvDgB,eAAe,CAACb,IAAG,GAAI,MAAM;MAC7Ba,eAAe,CAACC,MAAK,GAAI,2BAA2B;MACpDD,eAAe,CAACE,KAAK,CAACC,OAAM,GAAI,MAAM;MACtCH,eAAe,CAACI,QAAO,GAAKC,KAAK,IAAK;QAClC,MAAMzE,IAAG,GAAIyE,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QAClC,IAAI3E,IAAI,EAAE;UACN,MAAM4E,QAAO,GAAI5E,IAAI,CAACzF,IAAI;UAC1B4J,iBAAiB,CAACN,WAAU,GAAIe,QAAQ;UACxC;UACAzB,WAAW,CAACzD,YAAW,GAAIM,IAAI;QACnC,OAAO;UACHmE,iBAAiB,CAACN,WAAU,GAAI,UAAU;UAC1CV,WAAW,CAACzD,YAAW,GAAI,IAAI;QACnC;MACJ,CAAC;MAEDyE,iBAAiB,CAACU,OAAM,GAAI,MAAM;QAC9BT,eAAe,CAACU,KAAK,CAAC,CAAC;MAC3B,CAAC;;MAED;MACA3B,WAAW,CAACM,WAAW,CAACW,eAAe,CAAC;MACxC,MAAMW,oBAAmB,GAAIjG,QAAQ,CAACsE,aAAa,CAAC,QAAQ,CAAC;MAC7D2B,oBAAoB,CAAC1B,SAAQ,GAAI,wBAAwB;MACzD0B,oBAAoB,CAAClB,WAAU,GAAI,YAAY;MAC/CkB,oBAAoB,CAACF,OAAM,GAAI,MAAM;QACjC1B,WAAW,CAACxC,MAAM,CAAC,CAAC;MACxB,CAAC;MACDwC,WAAW,CAACM,WAAW,CAACM,kBAAkB,CAAC;MAC3CZ,WAAW,CAACM,WAAW,CAACS,iBAAiB,CAAC;MAC1Cf,WAAW,CAACM,WAAW,CAACU,iBAAiB,CAAC;MAC1ChB,WAAW,CAACM,WAAW,CAACsB,oBAAoB,CAAC;MAC7C,IAAI,CAACtK,UAAU,CAACmC,IAAI,CAACuG,WAAW,CAAC;MACjCrE,QAAQ,CAACkG,aAAa,CAAC,kBAAkB,CAAC,CAACvB,WAAW,CAACN,WAAW,CAAC;IACvE,CAAC;IACD8B,kBAAkBA,CAACR,KAAK,EAAE;MACtB,MAAMS,WAAU,GAAIT,KAAK,CAACC,MAAM,CAACS,OAAO,CAAC,eAAe,CAAC;MACzD,IAAI,CAACD,WAAW,EAAE;QACd,IAAI,CAACvK,YAAW,GAAI,KAAK;MAC7B;IACJ,CAAC;IACDyK,cAAcA,CAACC,IAAI,EAAE;MACjB,IAAI,CAACrK,WAAU,GAAIqK,IAAI;MACvB;MACA,IAAIA,IAAG,KAAM,MAAM,EAAE;QACjB,IAAI,CAACzE,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACD0E,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC1E,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,MAAM2E,mBAAmBA,CAAA,EAAG;MACxB,IAAI;QACA,MAAM7H,KAAI,GAAIjB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,IAAI,CAACU,KAAK,EAAE;QAEZ,MAAMK,QAAO,GAAI,MAAMC,KAAK,CAAC,qCAAqC,EAAE;UAChEE,OAAO,EAAE;YACL,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEF,IAAIK,QAAQ,CAACO,EAAE,EAAE;UACb,MAAM9D,IAAG,GAAI,MAAMuD,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAClC,IAAI,CAACtD,eAAc,GAAIT,IAAI,CAACgG,gBAAe,IAAK,EAAE;QACtD,OAAO,IAAIzC,QAAQ,CAACuD,MAAK,KAAM,GAAG,EAAE;UAChC;UACA,IAAI,CAACrG,eAAc,GAAI,EAAE;QAC7B;MACJ,EAAE,OAAOyD,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACJ;IACJ,CAAC;IACD8G,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACtJ,kBAAkB,CAACuB,MAAK,KAAM,CAAC,EAAE;QACtC,IAAI,CAACuB,MAAM,CAACN,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACJ;;MAEA;MACA,MAAMR,OAAM,GAAI,CACZ,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,aAAa,EACb,UAAU,EACV,eAAe,EACf,eAAc,CACjB;;MAED;MACA,MAAMuH,OAAM,GAAI,IAAI,CAACvJ,kBAAkB,CAAC+E,GAAG,CAAClF,QAAO,IAAK;QACpD,OAAO,CACH,IAAIA,QAAQ,CAACuB,KAAI,IAAK,EAAE,GAAG,EAC3B,IAAIvB,QAAQ,CAACoE,UAAS,IAAK,EAAE,GAAG,EAChC,IAAIpE,QAAQ,CAACqE,iBAAgB,IAAK,EAAE,GAAG,EACvCrE,QAAQ,CAACsE,aAAY,GAAI,IAAIyB,IAAI,CAAC/F,QAAQ,CAACsE,aAAa,CAAC,CAACqF,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAC1F,IAAI3J,QAAQ,CAACK,KAAI,IAAK,EAAE,GAAG,EAC3B,IAAIL,QAAQ,CAACmF,eAAe,EAAEE,WAAU,IAAK,EAAE,GAAG,EAClD,IAAIrF,QAAQ,CAACmF,eAAe,EAAEV,gBAAe,IAAK,EAAE,GAAG,EACvDzE,QAAQ,CAACmF,eAAe,EAAEG,UAAS,GAAI,IAAIS,IAAI,CAAC/F,QAAQ,CAACmF,eAAe,CAACG,UAAU,CAAC,CAACqE,kBAAkB,CAAC,OAAO,IAAI,EAAC,CACvH,CAACC,IAAI,CAAC,GAAG,CAAC;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,UAAS,GAAI,CAAC1H,OAAO,CAACyH,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGF,OAAO,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;;MAE7D;MACA,MAAME,GAAE,GAAI,QAAQ;MACpB,MAAMC,UAAS,GAAID,GAAE,GAAID,UAAU;;MAEnC;MACA,MAAMG,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;QAAEvC,IAAI,EAAE;MAA0B,CAAC,CAAC;MACxE,MAAM0C,IAAG,GAAInH,QAAQ,CAACsE,aAAa,CAAC,GAAG,CAAC;MAExC,IAAI6C,IAAI,CAACC,QAAO,KAAMC,SAAS,EAAE;QAC7B,MAAMC,GAAE,GAAIC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QACrCE,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;;QAE9B;QACA,MAAMI,WAAU,GAAI,IAAI1E,IAAI,CAAC,CAAC,CAAC2E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAMC,UAAS,GAAI,IAAI,CAACzL,cAAa,GAAI,IAAI,IAAI,CAACA,cAAc,EAAC,GAAI,gBAAgB;QACrF,MAAM0L,QAAO,GAAI,uBAAuBD,UAAU,IAAIH,WAAW,MAAM;QAEvEP,IAAI,CAACM,YAAY,CAAC,UAAU,EAAEK,QAAQ,CAAC;QACvCX,IAAI,CAAC3B,KAAK,CAACuC,UAAS,GAAI,QAAQ;QAChC/H,QAAQ,CAACX,IAAI,CAACsF,WAAW,CAACwC,IAAI,CAAC;QAC/BA,IAAI,CAACnB,KAAK,CAAC,CAAC;QACZhG,QAAQ,CAACX,IAAI,CAAC2I,WAAW,CAACb,IAAI,CAAC;QAE/B,IAAI,CAACjH,MAAM,CAACyB,OAAO,CAAC,YAAY,IAAI,CAACvE,kBAAkB,CAACuB,MAAM,cAAc,CAAC;MACjF,OAAO;QACH,IAAI,CAACuB,MAAM,CAACN,KAAK,CAAC,+BAA+B,CAAC;MACtD;IACJ,CAAC;IACD;IACAiD,wBAAwBA,CAAC7F,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;MACjD,IAAI,CAACJ,mBAAkB,GAAI;QACvBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEA,QAAQ;QAClBC,QAAQ,EAAEA;MACd,CAAC;MACD,IAAI,CAACL,mBAAkB,GAAI,IAAI;MAC/B;MACA,IAAI,CAACoL,SAAS,CAAC,MAAM;QACjB,MAAMC,QAAO,GAAIlI,QAAQ,CAACmI,cAAc,CAAC,YAAY,CAAC;QACtD,IAAID,QAAQ,EAAE;UACVA,QAAQ,CAACE,KAAK,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN,CAAC;IACDC,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACxL,mBAAkB,GAAI,KAAK;MAChC,IAAI,CAACC,mBAAkB,GAAI;QACvBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd,CAAC;IACL,CAAC;IACD,MAAMoL,gBAAgBA,CAAA,EAAG;MACrB,MAAMC,UAAS,GAAI,IAAI,CAACzL,mBAAmB,CAACC,GAAG;MAC/C,MAAMyL,SAAQ,GAAI7K,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;MAEvD,IAAIqK,UAAS,KAAMC,SAAS,EAAE;QAC1B,IAAI,CAACtI,MAAM,CAACN,KAAK,CAAC,4BAA4B,CAAC;QAC/C;MACJ;;MAEA;MACA,MAAM1C,QAAO,GAAI,IAAI,CAACJ,mBAAmB,CAACI,QAAQ;;MAElD;MACA,IAAI,CAACmL,oBAAoB,CAAC,CAAC;;MAE3B;MACA,IAAInL,QAAQ,EAAE;QACV,IAAI,CAAC+K,SAAS,CAAC,YAAY;UACvB,MAAM/K,QAAQ,CAAC,CAAC;QACpB,CAAC,CAAC;MACN;IACJ,CAAC;IACDuL,YAAYA,CAACvH,IAAI,EAAE;MACf,IAAI;QACA;QACA,MAAMwH,OAAM,GAAI,0BAA0BxH,IAAI,CAACyH,QAAQ,EAAE;;QAEzD;QACA,MAAMxB,IAAG,GAAInH,QAAQ,CAACsE,aAAa,CAAC,GAAG,CAAC;QACxC6C,IAAI,CAACyB,IAAG,GAAIF,OAAO;QACnBvB,IAAI,CAACC,QAAO,GAAIlG,IAAI,CAAC2H,SAAS;QAC9B1B,IAAI,CAACvB,MAAK,GAAI,QAAQ,EAAE;QACxBuB,IAAI,CAAC3B,KAAK,CAACC,OAAM,GAAI,MAAM;;QAE3B;QACAzF,QAAQ,CAACX,IAAI,CAACsF,WAAW,CAACwC,IAAI,CAAC;QAC/BA,IAAI,CAACnB,KAAK,CAAC,CAAC;QACZhG,QAAQ,CAACX,IAAI,CAAC2I,WAAW,CAACb,IAAI,CAAC;QAE/B,IAAI,CAACjH,MAAM,CAACyB,OAAO,CAAC,mBAAmBT,IAAI,CAAC2H,SAAS,EAAE,CAAC;MAC5D,EAAE,OAAOjJ,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC,IAAI,CAACM,MAAM,CAACN,KAAK,CAAC,uBAAuBsB,IAAI,CAAC2H,SAAS,EAAE,CAAC;MAC9D;IACJ,CAAC;IACDC,gBAAgBA,CAAChD,QAAQ,EAAE;MACvB;MACA,IAAIA,QAAQ,CAACnH,MAAK,GAAI,EAAE,EAAE;QACtB,MAAMoK,SAAQ,GAAIjD,QAAQ,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACoB,GAAG,CAAC,CAAC;QAC3C,MAAMC,cAAa,GAAInD,QAAQ,CAACoD,SAAS,CAAC,CAAC,EAAEpD,QAAQ,CAACqD,WAAW,CAAC,GAAG,CAAC,CAAC;QACvE,OAAOF,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI,GAAI,GAAE,GAAIH,SAAS;MACpE;MACA,OAAOjD,QAAQ;IACnB;EACJ,CAAC;EACDsD,OAAOA,CAAA,EAAG;IACN;IACA,MAAMC,OAAM,GAAI1L,YAAY,CAACO,OAAO,CAAC,WAAW,CAAC;IACjD,IAAImL,OAAO,EAAE;MACT,IAAI,CAACzN,IAAG,GAAI0D,IAAI,CAACgK,KAAK,CAACD,OAAO,CAAC;IACnC;IACA;IACArJ,QAAQ,CAACuJ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACpD,kBAAkB,CAAC;IAC3D;IACA,IAAI,CAACM,mBAAmB,CAAC,CAAC;EAC9B,CAAC;EACD+C,aAAaA,CAAA,EAAG;IACZxJ,QAAQ,CAACyJ,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACtD,kBAAkB,CAAC;EAClE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}