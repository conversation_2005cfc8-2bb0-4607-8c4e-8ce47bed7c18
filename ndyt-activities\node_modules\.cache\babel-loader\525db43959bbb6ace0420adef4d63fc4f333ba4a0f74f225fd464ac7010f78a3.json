{"ast": null, "code": "import ActivityView from \"@/views/ActivityView.vue\";\nimport LoginView from \"@/views/LoginView.vue\";\nimport RegisterView from \"@/views/RegisterView.vue\";\nimport AdminView from \"@/views/AdminView.vue\";\nimport { createRouter, createWebHistory } from \"vue-router\";\nimport Swal from 'sweetalert2';\nconst router = createRouter({\n  // The app is served under this base path by Express\n  history: createWebHistory('/ndyt/'),\n  // Disable router caching\n  scrollBehavior() {\n    return {\n      top: 0\n    };\n  },\n  routes: [{\n    path: '/',\n    component: ActivityView,\n    meta: {\n      requiresAuth: true\n    }\n  }, {\n    path: '/login',\n    component: LoginView,\n    meta: {\n      guestOnly: true\n    }\n  }, {\n    path: '/register',\n    component: RegisterView,\n    meta: {\n      guestOnly: true\n    }\n  }, {\n    path: '/admin',\n    component: AdminView,\n    meta: {\n      requiresAuth: true,\n      requiresAdmin: true\n    }\n  }]\n});\nrouter.beforeEach(async (to, from, next) => {\n  const token = localStorage.getItem('ndyt_token');\n\n  // Force cache busting for login route\n  if (to.path === '/login') {\n    // Add timestamp to force fresh load\n    const timestamp = Date.now();\n    if (!to.query.t || to.query.t !== timestamp.toString()) {\n      next({\n        path: '/login',\n        query: {\n          ...to.query,\n          t: timestamp\n        },\n        replace: true\n      });\n      return;\n    }\n  }\n  if (to.meta?.requiresAuth && !token) {\n    const timestamp = Date.now();\n    next({\n      path: '/login',\n      query: {\n        redirect: to.fullPath,\n        t: timestamp\n      }\n    });\n    return;\n  }\n  if (to.meta?.guestOnly && token) {\n    next('/');\n    return;\n  }\n\n  // Check admin access for admin routes\n  if (to.meta?.requiresAdmin && token) {\n    try {\n      const response = await fetch('/api/v1/ndyt-activities/auth/me', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const user = await response.json();\n        if (user.rank !== 'admin') {\n          Swal.fire({\n            title: 'غير مصرح',\n            text: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',\n            icon: 'error',\n            confirmButtonColor: '#3085d6',\n            confirmButtonText: 'موافق'\n          });\n          next('/');\n          return;\n        }\n      } else {\n        next('/login');\n        return;\n      }\n    } catch (error) {\n      console.error('Error checking admin access:', error);\n      next('/login');\n      return;\n    }\n  }\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["ActivityView", "<PERSON><PERSON><PERSON>ie<PERSON>", "RegisterView", "Admin<PERSON>iew", "createRouter", "createWebHistory", "<PERSON><PERSON>", "router", "history", "scroll<PERSON>eh<PERSON>or", "top", "routes", "path", "component", "meta", "requiresAuth", "guestOn<PERSON>", "requiresAdmin", "beforeEach", "to", "from", "next", "token", "localStorage", "getItem", "timestamp", "Date", "now", "query", "t", "toString", "replace", "redirect", "fullPath", "response", "fetch", "headers", "ok", "user", "json", "rank", "fire", "title", "text", "icon", "confirmButtonColor", "confirmButtonText", "error", "console"], "sources": ["F:/My Apps/iqtp/iqtp_backend/ndyt-activities/src/router/index.js"], "sourcesContent": ["import ActivityView from \"@/views/ActivityView.vue\";\r\nimport LoginView from \"@/views/LoginView.vue\";\r\nimport RegisterView from \"@/views/RegisterView.vue\";\r\nimport AdminView from \"@/views/AdminView.vue\";\r\nimport { createRouter, createWebHistory } from \"vue-router\";\r\nimport Swal from 'sweetalert2';\r\n\r\nconst router = createRouter({\r\n  // The app is served under this base path by Express\r\n  history: createWebHistory('/ndyt/'),\r\n  // Disable router caching\r\n  scrollBehavior() {\r\n    return { top: 0 }\r\n  },\r\n  routes: [\r\n    {\r\n      path: '/',\r\n      component: ActivityView,\r\n      meta: { requiresAuth: true }\r\n    },\r\n    {\r\n      path: '/login',\r\n      component: LoginView,\r\n      meta: { guestOnly: true }\r\n    },\r\n    {\r\n      path: '/register',\r\n      component: RegisterView,\r\n      meta: { guestOnly: true }\r\n    },\r\n    {\r\n      path: '/admin',\r\n      component: AdminView,\r\n      meta: { requiresAuth: true, requiresAdmin: true }\r\n    },\r\n  ]\r\n});\r\n\r\nrouter.beforeEach(async (to, from, next) => {\r\n  const token = localStorage.getItem('ndyt_token');\r\n  \r\n  // Force cache busting for login route\r\n  if (to.path === '/login') {\r\n    // Add timestamp to force fresh load\r\n    const timestamp = Date.now();\r\n    if (!to.query.t || to.query.t !== timestamp.toString()) {\r\n      next({ \r\n        path: '/login', \r\n        query: { ...to.query, t: timestamp },\r\n        replace: true \r\n      });\r\n      return;\r\n    }\r\n  }\r\n  \r\n  if (to.meta?.requiresAuth && !token) {\r\n    const timestamp = Date.now();\r\n    next({ \r\n      path: '/login', \r\n      query: { redirect: to.fullPath, t: timestamp }\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (to.meta?.guestOnly && token) {\r\n    next('/');\r\n    return;\r\n  }\r\n  \r\n  // Check admin access for admin routes\r\n  if (to.meta?.requiresAdmin && token) {\r\n    try {\r\n      const response = await fetch('/api/v1/ndyt-activities/auth/me', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`\r\n        }\r\n      });\r\n      \r\n      if (response.ok) {\r\n        const user = await response.json();\r\n        if (user.rank !== 'admin') {\r\n          Swal.fire({\r\n            title: 'غير مصرح',\r\n            text: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',\r\n            icon: 'error',\r\n            confirmButtonColor: '#3085d6',\r\n            confirmButtonText: 'موافق'\r\n          });\r\n          next('/');\r\n          return;\r\n        }\r\n      } else {\r\n        next('/login');\r\n        return;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error checking admin access:', error);\r\n      next('/login');\r\n      return;\r\n    }\r\n  }\r\n  \r\n  next();\r\n});\r\n\r\nexport default router;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,IAAI,MAAM,aAAa;AAE9B,MAAMC,MAAM,GAAGH,YAAY,CAAC;EAC1B;EACAI,OAAO,EAAEH,gBAAgB,CAAC,QAAQ,CAAC;EACnC;EACAI,cAAcA,CAAA,EAAG;IACf,OAAO;MAAEC,GAAG,EAAE;IAAE,CAAC;EACnB,CAAC;EACDC,MAAM,EAAE,CACN;IACEC,IAAI,EAAE,GAAG;IACTC,SAAS,EAAEb,YAAY;IACvBc,IAAI,EAAE;MAAEC,YAAY,EAAE;IAAK;EAC7B,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEZ,SAAS;IACpBa,IAAI,EAAE;MAAEE,SAAS,EAAE;IAAK;EAC1B,CAAC,EACD;IACEJ,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEX,YAAY;IACvBY,IAAI,EAAE;MAAEE,SAAS,EAAE;IAAK;EAC1B,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEV,SAAS;IACpBW,IAAI,EAAE;MAAEC,YAAY,EAAE,IAAI;MAAEE,aAAa,EAAE;IAAK;EAClD,CAAC;AAEL,CAAC,CAAC;AAEFV,MAAM,CAACW,UAAU,CAAC,OAAOC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAC1C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;;EAEhD;EACA,IAAIL,EAAE,CAACP,IAAI,KAAK,QAAQ,EAAE;IACxB;IACA,MAAMa,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACR,EAAE,CAACS,KAAK,CAACC,CAAC,IAAIV,EAAE,CAACS,KAAK,CAACC,CAAC,KAAKJ,SAAS,CAACK,QAAQ,CAAC,CAAC,EAAE;MACtDT,IAAI,CAAC;QACHT,IAAI,EAAE,QAAQ;QACdgB,KAAK,EAAE;UAAE,GAAGT,EAAE,CAACS,KAAK;UAAEC,CAAC,EAAEJ;QAAU,CAAC;QACpCM,OAAO,EAAE;MACX,CAAC,CAAC;MACF;IACF;EACF;EAEA,IAAIZ,EAAE,CAACL,IAAI,EAAEC,YAAY,IAAI,CAACO,KAAK,EAAE;IACnC,MAAMG,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5BN,IAAI,CAAC;MACHT,IAAI,EAAE,QAAQ;MACdgB,KAAK,EAAE;QAAEI,QAAQ,EAAEb,EAAE,CAACc,QAAQ;QAAEJ,CAAC,EAAEJ;MAAU;IAC/C,CAAC,CAAC;IACF;EACF;EAEA,IAAIN,EAAE,CAACL,IAAI,EAAEE,SAAS,IAAIM,KAAK,EAAE;IAC/BD,IAAI,CAAC,GAAG,CAAC;IACT;EACF;;EAEA;EACA,IAAIF,EAAE,CAACL,IAAI,EAAEG,aAAa,IAAIK,KAAK,EAAE;IACnC,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9DC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUd,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,IAAI,KAAK,OAAO,EAAE;UACzBlC,IAAI,CAACmC,IAAI,CAAC;YACRC,KAAK,EAAE,UAAU;YACjBC,IAAI,EAAE,uCAAuC;YAC7CC,IAAI,EAAE,OAAO;YACbC,kBAAkB,EAAE,SAAS;YAC7BC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACFzB,IAAI,CAAC,GAAG,CAAC;UACT;QACF;MACF,CAAC,MAAM;QACLA,IAAI,CAAC,QAAQ,CAAC;QACd;MACF;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD1B,IAAI,CAAC,QAAQ,CAAC;MACd;IACF;EACF;EAEAA,IAAI,CAAC,CAAC;AACR,CAAC,CAAC;AAEF,eAAed,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}