<script>
export default {
  name: 'LoginView',
  data() {
    return {
      username: '',
      password: '',
      team_pin: '',
      busy: false,
      error: ''
    };
  },
  mounted() {
    // Aggressive cache busting for login component
    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);
    
    // Add cache-busting meta tags specifically for login
    const metaTags = [
      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },
      { name: 'pragma', content: 'no-cache' },
      { name: 'expires', content: '0' },
      { name: 'last-modified', content: new Date().toUTCString() },
      { name: 'etag', content: cacheBuster },
      { name: 'login-cache-buster', content: cacheBuster }
    ];
    
    metaTags.forEach(tag => {
      // Remove existing meta tags with same name
      const existing = document.querySelector(`meta[http-equiv="${tag.name}"]`);
      if (existing) existing.remove();
      
      // Add new meta tag
      const meta = document.createElement('meta');
      meta.setAttribute('http-equiv', tag.name);
      meta.setAttribute('content', tag.content);
      document.head.appendChild(meta);
    });
    
    // Force component refresh to prevent caching issues
    this.$forceUpdate();
    
    // Clear any cached form data
    this.username = '';
    this.password = '';
    this.team_pin = '';
    this.error = '';
    
    // Clear only non-authentication browser storage
    if (typeof Storage !== 'undefined') {
      // Only clear sessionStorage, preserve localStorage auth tokens
      sessionStorage.clear();
    }
    
    // Force browser to not cache this page
    if (window.history && window.history.replaceState) {
      const url = new URL(window.location.href);
      url.searchParams.set('_cb', cacheBuster);
      url.searchParams.set('_nocache', '1');
      window.history.replaceState(null, null, url.toString());
    }
    
    // Add cache-busting attribute to component element
    if (this.$el && this.$el.setAttribute) {
      this.$el.setAttribute('data-login-cache-bust', cacheBuster);
      this.$el.setAttribute('data-no-cache', 'true');
    }
  },
  beforeUnmount() {
    // Clear form data when component is destroyed
    this.username = '';
    this.password = '';
    this.team_pin = '';
    this.error = '';
  },
  methods: {
    async doLogin() {
      this.error = '';
      if (!this.username || !this.password || !this.team_pin) {
        this.error = 'يرجى تعبئة جميع الحقول';
        return;
      }
      this.busy = true;
      try {
        const res = await fetch('/api/v1/ndyt-activities/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })
        });
        const data = await res.json();
        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');
        localStorage.setItem('ndyt_token', data.token);
        // optional: store user info
        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));
        // store team pin for later submissions
        localStorage.setItem('ndyt_team_pin', this.team_pin);
        const redirect = this.$route.query.redirect || '/';
        this.$router.replace(redirect);
      } catch (e) {
        this.error = e.message;
      } finally {
        this.busy = false;
      }
    }
  }
}
</script>
<template>
  <div class="navbar">
    <div class="navbar-content">
      <div class="navbar-brand">
        <span class="navbar-text">المجلس الأعلى للشباب</span>
        <img class="navbar-logo" src="../assets/ndyt_logo.jpg" alt="شعار الفريق">
        <span class="navbar-text">الفريق الوطني للشباب الرقمي</span>
      </div>
    </div>
  </div>

  <div class="content-wrapper">
    <div class="auth-container">
      <div class="card">
        <div class="card-header">
          <h1 class="card-title">تسجيل الدخول</h1>
        </div>

        <div class="card-body">
          <form @submit.prevent="doLogin" class="auth-form">
            <div class="form-group">
              <label class="form-label" for="username">اسم المستخدم</label>
              <input
                id="username"
                v-model="username"
                type="text"
                class="form-input"
                placeholder="ادخل اسم المستخدم"
                required
                autocomplete="username"
              />
            </div>

            <div class="form-group">
              <label class="form-label" for="password">كلمة المرور</label>
              <input
                id="password"
                v-model="password"
                type="password"
                class="form-input"
                placeholder="ادخل كلمة المرور"
                required
                autocomplete="current-password"
              />
            </div>

            <div class="form-group">
              <label class="form-label" for="team_pin">رمز الفريق الرقمي</label>
              <input
                id="team_pin"
                v-model="team_pin"
                type="text"
                class="form-input"
                placeholder="ادخل رمز الفريق الرقمي"
                required
              />
            </div>

            <button type="submit" :disabled="busy" class="btn btn-primary btn-lg auth-submit">
              <svg v-if="busy" class="loading-spinner" width="20" height="20" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                  <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                  <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                </circle>
              </svg>
              <span v-if="!busy">دخول</span>
              <span v-else>جاري الدخول...</span>
            </button>

            <div v-if="error" class="error-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {{ error }}
            </div>

            <div class="auth-footer">
              <p class="auth-hint">
                ليس لديك حساب؟
                <router-link to="/register" class="auth-link">سجل الآن</router-link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* Enhanced Auth Container */
.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: var(--space-8) var(--space-4);
}

/* Enhanced Form Styling */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.auth-submit {
  margin-top: var(--space-4);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.auth-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.auth-submit:hover::before {
  left: 100%;
}

/* Loading Spinner Animation */
.loading-spinner {
  animation: spin 1s linear infinite;
  margin-right: var(--space-2);
}

/* Error Message Styling */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--color-error-500);
  border-radius: var(--radius-xl);
  color: var(--color-error-500);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: right;
  animation: slideDown 0.3s ease-out;
}

/* Auth Footer */
.auth-footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.auth-hint {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.auth-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);
  border-radius: var(--radius-base);
  padding: var(--space-1) var(--space-2);
  margin: 0 calc(-1 * var(--space-2));
}

.auth-link:hover {
  color: var(--color-primary-400);
  background: rgba(14, 165, 233, 0.1);
  transform: translateY(-1px);
}

/* Enhanced Card Styling */
.card {
  width: 100%;
  max-width: 480px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-dark-xl);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}
/* Enhanced Card Header */
.card-header {
  padding: var(--space-8) var(--space-8) var(--space-6);
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(168, 85, 247, 0.05));
  border-bottom: 1px solid var(--border-primary);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
}

/* Enhanced Card Body */
.card-body {
  padding: var(--space-8);
}

/* Responsive Design Improvements */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4) var(--space-2);
    min-height: calc(100vh - 100px);
  }

  .card {
    max-width: 100%;
    margin: 0;
    border-radius: var(--radius-2xl);
  }

  .card-header,
  .card-body {
    padding: var(--space-6) var(--space-4);
  }

  .card-title {
    font-size: var(--font-size-2xl);
  }

  .form-input {
    padding: var(--space-4);
    font-size: var(--font-size-base);
  }

  .auth-submit {
    padding: var(--space-4);
    font-size: var(--font-size-base);
  }

  .navbar {
    margin: var(--space-2);
  }

  .navbar-content {
    padding: var(--space-3) var(--space-4);
  }

  .navbar-brand {
    flex-direction: column;
    gap: var(--space-3);
  }

  .navbar-text {
    font-size: var(--font-size-lg);
  }

  .navbar-logo {
    height: 50px;
  }
}

@media (max-width: 768px) {
  .auth-container {
    padding: var(--space-6) var(--space-3);
  }

  .navbar-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .navbar-brand {
    flex-direction: column;
    gap: var(--space-4);
  }
}

/* Enhanced Focus States for Accessibility */
.form-input:focus-visible,
.auth-submit:focus-visible,
.auth-link:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Enhanced Hover Effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-2xl);
  border-color: var(--border-secondary);
}

/* Loading State Improvements */
.auth-submit:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.auth-submit:disabled:hover::before {
  left: -100%;
}

/* Form Validation States */
.form-input:invalid {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 1px var(--color-error-500);
}

.form-input:valid {
  border-color: var(--color-success-500);
}

/* Enhanced Animation Performance */
.card,
.auth-submit,
.form-input,
.auth-link {
  will-change: transform;
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .card {
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--border-primary);
  }

  .form-input {
    background: rgba(255, 255, 255, 0.06);
  }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  .card,
  .auth-submit,
  .form-input,
  .auth-link,
  .loading-spinner {
    animation: none !important;
    transition: none !important;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .auth-submit,
  .auth-footer {
    display: none;
  }

  .card {
    box-shadow: none;
    border: 1px solid #000;
    background: white;
    color: black;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card {
    border: 3px solid;
    background: var(--bg-primary);
  }

  .form-input {
    border: 2px solid;
  }

  .auth-submit {
    border: 2px solid;
  }
}
</style>