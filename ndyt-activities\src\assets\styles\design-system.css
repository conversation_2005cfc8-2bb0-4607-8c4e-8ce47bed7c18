/* ===== NDYT Activities Design System ===== */

:root {
  /* === Color Palette === */
  /* Primary Colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* Secondary Colors */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;

  /* Neutral Colors */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;

  /* Dark Theme Colors */
  --color-dark-50: #1e1e2e;
  --color-dark-100: #2a2a3e;
  --color-dark-200: #3a3a5e;
  --color-dark-300: #4a4a6e;
  --color-dark-400: #5a5a7e;
  --color-dark-500: #6a6a8e;
  --color-dark-600: #7a7a9e;
  --color-dark-700: #8a8aae;
  --color-dark-800: #9a9abe;
  --color-dark-900: #aaaace;

  /* Status Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;

  /* === Typography === */
  --font-family-primary: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  --font-family-arabic: 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', sans-serif;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* === Spacing === */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* === Border Radius === */
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* === Shadows === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Dark Theme Shadows */
  --shadow-dark-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-dark-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-dark-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-dark-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-dark-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);

  /* === Breakpoints === */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* === Z-Index === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* === Transitions === */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-slower: 500ms ease-in-out;

  /* === Component Specific Variables === */
  /* Buttons */
  --btn-height-sm: 2rem;      /* 32px */
  --btn-height-base: 2.5rem;  /* 40px */
  --btn-height-lg: 3rem;      /* 48px */
  --btn-height-xl: 3.5rem;    /* 56px */

  /* Form Elements */
  --input-height-sm: 2rem;
  --input-height-base: 2.5rem;
  --input-height-lg: 3rem;

  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* === Theme Colors === */
  --bg-primary: var(--color-dark-50);
  --bg-secondary: var(--color-dark-100);
  --bg-tertiary: var(--color-dark-200);

  --text-primary: var(--color-neutral-50);
  --text-secondary: var(--color-neutral-200);
  --text-tertiary: var(--color-neutral-400);

  --border-primary: var(--color-dark-300);
  --border-secondary: var(--color-dark-400);

  --accent-primary: var(--color-primary-500);
  --accent-secondary: var(--color-secondary-500);
}

/* === Global Reset & Base Styles === */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-arabic);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  min-height: 100vh;
  direction: rtl;
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === Utility Classes === */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === Responsive Grid System === */
.grid {
  display: grid;
  gap: var(--space-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* === Flexbox Utilities === */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

/* === Spacing Utilities === */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.p-2 { padding: var(--space-2); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* === Text Utilities === */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

/* === Responsive Design Utilities === */
@media (max-width: 480px) {
  .xs\:hidden { display: none; }
  .xs\:block { display: block; }
  .xs\:flex { display: flex; }
  .xs\:grid { display: grid; }
  .xs\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .xs\:text-sm { font-size: var(--font-size-sm); }
  .xs\:text-base { font-size: var(--font-size-base); }
  .xs\:p-2 { padding: var(--space-2); }
  .xs\:p-4 { padding: var(--space-4); }
  .xs\:gap-2 { gap: var(--space-2); }
  .xs\:gap-4 { gap: var(--space-4); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:flex-col { flex-direction: column; }
  .md\:text-center { text-align: center; }
  .md\:p-4 { padding: var(--space-4); }
  .md\:p-6 { padding: var(--space-6); }
  .md\:gap-4 { gap: var(--space-4); }
  .md\:gap-6 { gap: var(--space-6); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:flex-row { flex-direction: row; }
  .lg\:text-lg { font-size: var(--font-size-lg); }
  .lg\:text-xl { font-size: var(--font-size-xl); }
  .lg\:p-8 { padding: var(--space-8); }
  .lg\:gap-8 { gap: var(--space-8); }
}

/* === Component Base Styles === */

/* Button Base */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-xl);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-base);
  min-height: var(--btn-height-base);
  min-width: 120px;
  direction: rtl;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
  min-height: var(--btn-height-sm);
  min-width: 96px;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
  min-height: var(--btn-height-lg);
  min-width: 160px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  color: white;
  box-shadow: var(--shadow-dark-md);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-lg);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.08);
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.12);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--color-success-600), var(--color-success-500));
  color: white;
  box-shadow: var(--shadow-dark-md);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-lg);
}

.btn-warning {
  background: linear-gradient(135deg, var(--color-warning-600), var(--color-warning-500));
  color: white;
  box-shadow: var(--shadow-dark-md);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-lg);
}

.btn-error {
  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-500));
  color: white;
  box-shadow: var(--shadow-dark-md);
}

.btn-error:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-lg);
}

/* Form Elements Base */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-xl);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: all var(--transition-base);
  direction: rtl;
  text-align: right;
  min-height: var(--input-height-base);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-align: right;
}

/* Card Components */
.card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-3xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-dark-lg);
  transition: all var(--transition-base);
  direction: rtl;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-dark-xl);
  border-color: var(--border-secondary);
}

.card-header {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 2px solid var(--border-primary);
}

.card-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  text-align: center;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-body {
  color: var(--text-secondary);
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  padding: var(--space-4);
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-3xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-dark-2xl);
  animation: slideUp 0.3s ease-out;
  z-index: var(--z-modal);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 2px solid var(--border-primary);
  background: var(--bg-tertiary);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: var(--font-size-2xl);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  min-width: auto;
  min-height: auto;
}

.modal-close:hover {
  background: var(--border-primary);
  color: var(--text-primary);
  transform: none;
}

.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

/* Navigation Components */
.navbar {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  margin: var(--space-4);
  box-shadow: var(--shadow-dark-lg);
  backdrop-filter: blur(10px);
}

.navbar-content {
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-5);
  flex: 1;
  justify-content: center;
}

.navbar-logo {
  height: clamp(50px, 8vw, 70px);
  width: auto;
  border: 2px solid var(--border-secondary);
  border-radius: var(--radius-full);
  transition: transform var(--transition-base);
}

.navbar-logo:hover {
  transform: scale(1.05);
}

.navbar-text {
  font-weight: var(--font-weight-bold);
  font-size: clamp(var(--font-size-base), 3vw, var(--font-size-2xl));
  color: var(--text-primary);
  text-align: center;
  line-height: var(--line-height-tight);
}

/* Table Components */
.table-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-2xl);
  border: 2px solid var(--border-primary);
  backdrop-filter: blur(10px);
  overflow: hidden;
  box-shadow: var(--shadow-dark-lg);
}

.table-wrapper {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  direction: rtl;
}

.table th {
  background: rgba(255, 255, 255, 0.08);
  padding: var(--space-4) var(--space-3);
  text-align: right;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  border-bottom: 2px solid var(--border-primary);
  white-space: nowrap;
}

.table td {
  padding: var(--space-4) var(--space-3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  vertical-align: middle;
  color: var(--text-secondary);
}

.table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Status Badge Components */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  border: 1px solid transparent;
}

.badge-success {
  background: rgba(34, 197, 94, 0.2);
  color: var(--color-success-500);
  border-color: rgba(34, 197, 94, 0.3);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--color-warning-500);
  border-color: rgba(245, 158, 11, 0.3);
}

.badge-error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--color-error-500);
  border-color: rgba(239, 68, 68, 0.3);
}

.badge-info {
  background: rgba(59, 130, 246, 0.2);
  color: var(--color-info-500);
  border-color: rgba(59, 130, 246, 0.3);
}

.badge-secondary {
  background: rgba(168, 85, 247, 0.2);
  color: var(--color-secondary-500);
  border-color: rgba(168, 85, 247, 0.3);
}

/* Loading Components */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--accent-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  padding: var(--space-16) var(--space-4);
  color: var(--text-tertiary);
}

/* Animation Keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Layout Components */
.layout-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  padding: var(--space-4);
}

.content-wrapper {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section {
  margin-bottom: var(--space-8);
}

.section-header {
  margin-bottom: var(--space-6);
  text-align: center;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

/* Responsive Enhancements */
@media (max-width: 480px) {
  .layout-container {
    padding: var(--space-2);
  }

  .content-wrapper {
    padding: 0 var(--space-2);
  }

  .navbar {
    margin: var(--space-2);
  }

  .navbar-content {
    padding: var(--space-3) var(--space-4);
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .navbar-brand {
    flex-direction: column;
    gap: var(--space-3);
  }

  .card {
    padding: var(--space-4);
  }

  .modal-content {
    margin: var(--space-2);
    max-width: calc(100vw - var(--space-4));
  }

  .modal-header,
  .modal-body {
    padding: var(--space-4);
  }

  .table-container {
    border-radius: var(--radius-xl);
  }

  .btn {
    min-width: 100px;
    padding: var(--space-3) var(--space-4);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .section-subtitle {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 768px) {
  .navbar-content {
    flex-direction: column;
    gap: var(--space-4);
  }

  .table th,
  .table td {
    padding: var(--space-2);
    font-size: var(--font-size-sm);
  }

  .card {
    padding: var(--space-6);
  }

  .modal-content {
    max-width: calc(100vw - var(--space-8));
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
.btn:focus-visible,
.form-input:focus-visible,
.form-select:focus-visible,
.form-textarea:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #ffffff;
    --border-secondary: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
  }
}