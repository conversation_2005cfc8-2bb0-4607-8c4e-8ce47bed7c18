<script>
export default {
  name: 'RegisterView',
  data() {
    return {
      username: '',
      full_name: '',
      password: '',
      team_pin: '',
      governorate: '',
      busy: false,
      error: '',
      governorates: [
        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',
        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',
        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'
      ]
    };
  },
  methods: {
    async doRegister() {
      this.error = '';
      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {
        this.error = 'يرجى تعبئة جميع الحقول';
        return;
      }
      this.busy = true;
      try {
        const res = await fetch('/api/v1/ndyt-activities/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })
        });
        const data = await res.json();
        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');
        // persist auth and team pin
        localStorage.setItem('ndyt_token', data.token);
        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));
        localStorage.setItem('ndyt_team_pin', this.team_pin);
        const redirect = this.$route.query.redirect || '/';
        this.$router.replace(redirect);
      } catch (e) {
        this.error = e.message;
      } finally {
        this.busy = false;
      }
    }
  }
}
</script>
<template>
  <div class="navbar">
    <div class="navbar-content">
      <div class="navbar-brand">
        <span class="navbar-text">المجلس الأعلى للشباب</span>
        <img class="navbar-logo" src="../assets/ndyt_logo.jpg" alt="شعار الفريق">
        <span class="navbar-text">الفريق الوطني للشباب الرقمي</span>
      </div>
    </div>
  </div>

  <div class="content-wrapper">
    <div class="auth-container">
      <div class="card">
        <div class="card-header">
          <h1 class="card-title">إنشاء حساب جديد</h1>
          <p class="card-subtitle">انضم إلى الفريق الوطني للشباب الرقمي</p>
        </div>

        <div class="card-body">
          <form @submit.prevent="doRegister" class="auth-form">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label" for="username">اسم المستخدم</label>
                <input
                  id="username"
                  v-model="username"
                  type="text"
                  class="form-input"
                  placeholder="ادخل اسم المستخدم"
                  required
                  autocomplete="username"
                />
              </div>

              <div class="form-group">
                <label class="form-label" for="full_name">الاسم الكامل</label>
                <input
                  id="full_name"
                  v-model="full_name"
                  type="text"
                  class="form-input"
                  placeholder="ادخل الاسم الكامل"
                  required
                  autocomplete="name"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="governorate">المحافظة</label>
              <select
                id="governorate"
                v-model="governorate"
                class="form-select"
                required
              >
                <option value="" disabled>اختر المحافظة</option>
                <option v-for="gov in governorates" :key="gov" :value="gov">{{ gov }}</option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label" for="password">كلمة المرور</label>
                <input
                  id="password"
                  v-model="password"
                  type="password"
                  class="form-input"
                  placeholder="ادخل كلمة المرور"
                  required
                  autocomplete="new-password"
                  minlength="6"
                />
              </div>

              <div class="form-group">
                <label class="form-label" for="team_pin">رمز الفريق الرقمي</label>
                <input
                  id="team_pin"
                  v-model="team_pin"
                  type="text"
                  class="form-input"
                  placeholder="ادخل رمز الفريق الرقمي"
                  required
                />
              </div>
            </div>

            <button type="submit" :disabled="busy" class="btn btn-primary btn-lg auth-submit">
              <svg v-if="busy" class="loading-spinner" width="20" height="20" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                  <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                  <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                </circle>
              </svg>
              <span v-if="!busy">إنشاء الحساب</span>
              <span v-else>جاري إنشاء الحساب...</span>
            </button>

            <div v-if="error" class="error-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {{ error }}
            </div>

            <div class="auth-footer">
              <p class="auth-hint">
                لديك حساب بالفعل؟
                <router-link to="/login" class="auth-link">سجّل الدخول</router-link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* Enhanced Auth Container */
.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: var(--space-8) var(--space-4);
}

/* Enhanced Form Styling */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
}

.auth-submit {
  margin-top: var(--space-4);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.auth-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.auth-submit:hover::before {
  left: 100%;
}

/* Loading Spinner Animation */
.loading-spinner {
  animation: spin 1s linear infinite;
  margin-right: var(--space-2);
}

/* Error Message Styling */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--color-error-500);
  border-radius: var(--radius-xl);
  color: var(--color-error-500);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: right;
  animation: slideDown 0.3s ease-out;
}

/* Auth Footer */
.auth-footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.auth-hint {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.auth-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);
  border-radius: var(--radius-base);
  padding: var(--space-1) var(--space-2);
  margin: 0 calc(-1 * var(--space-2));
}

.auth-link:hover {
  color: var(--color-primary-400);
  background: rgba(14, 165, 233, 0.1);
  transform: translateY(-1px);
}

/* Enhanced Card Styling */
.card {
  width: 100%;
  max-width: 600px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-dark-xl);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

/* Enhanced Card Header */
.card-header {
  padding: var(--space-8) var(--space-8) var(--space-6);
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(168, 85, 247, 0.05));
  border-bottom: 1px solid var(--border-primary);
  position: relative;
  text-align: center;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: var(--space-2) 0 0;
  font-weight: var(--font-weight-medium);
}

/* Enhanced Card Body */
.card-body {
  padding: var(--space-8);
}

/* Enhanced Select Styling */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-left: 2.5rem;
  appearance: none;
}

/* Animation Keyframes */
@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design Improvements */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4) var(--space-2);
    min-height: calc(100vh - 100px);
  }

  .card {
    max-width: 100%;
    margin: 0;
    border-radius: var(--radius-2xl);
  }

  .card-header,
  .card-body {
    padding: var(--space-6) var(--space-4);
  }

  .card-title {
    font-size: var(--font-size-2xl);
  }

  .card-subtitle {
    font-size: var(--font-size-xs);
  }

  .form-input,
  .form-select {
    padding: var(--space-4);
    font-size: var(--font-size-base);
  }

  .auth-submit {
    padding: var(--space-4);
    font-size: var(--font-size-base);
  }

  .navbar {
    margin: var(--space-2);
  }

  .navbar-content {
    padding: var(--space-3) var(--space-4);
  }

  .navbar-brand {
    flex-direction: column;
    gap: var(--space-3);
  }

  .navbar-text {
    font-size: var(--font-size-lg);
  }

  .navbar-logo {
    height: 50px;
  }
}

@media (max-width: 768px) {
  .auth-container {
    padding: var(--space-6) var(--space-3);
  }

  .navbar-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .navbar-brand {
    flex-direction: column;
    gap: var(--space-4);
  }

  .card {
    max-width: 100%;
  }

  .card-header,
  .card-body {
    padding: var(--space-6);
  }
}

/* Enhanced Focus States for Accessibility */
.form-input:focus-visible,
.form-select:focus-visible,
.auth-submit:focus-visible,
.auth-link:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Enhanced Hover Effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark-2xl);
  border-color: var(--border-secondary);
}

/* Loading State Improvements */
.auth-submit:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.auth-submit:disabled:hover::before {
  left: -100%;
}

/* Form Validation States */
.form-input:invalid,
.form-select:invalid {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 1px var(--color-error-500);
}

.form-input:valid,
.form-select:valid {
  border-color: var(--color-success-500);
}

/* Enhanced Animation Performance */
.card,
.auth-submit,
.form-input,
.form-select,
.auth-link {
  will-change: transform;
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  .card,
  .auth-submit,
  .form-input,
  .form-select,
  .auth-link,
  .loading-spinner {
    animation: none !important;
    transition: none !important;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .auth-submit,
  .auth-footer {
    display: none;
  }

  .card {
    box-shadow: none;
    border: 1px solid #000;
    background: white;
    color: black;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card {
    border: 3px solid;
    background: var(--bg-primary);
  }

  .form-input,
  .form-select {
    border: 2px solid;
  }

  .auth-submit {
    border: 2px solid;
  }
}

.auth-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #101214, #1b1f24);
    padding: 16px;
}

.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 32px;
    width: 100%;
    max-width: 420px;
    color: #e5e7eb;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    direction: rtl;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.card h1 {
    font-size: 28px;
    margin: 0 0 32px;
    text-align: center;
    font-weight: 700;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 20px 0;
}

.field label {
    font-size: 14px;
    color: #cbd5e1;
    font-weight: 600;
    text-align: right;
}

input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e5e7eb;
    border-radius: 12px;
    padding: 14px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    text-align: right;
    direction: rtl;
    box-sizing: border-box;
}

input:focus {
    outline: none;
    border-color: #4f46e5;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

input::placeholder {
    color: #cbd5e1;
    text-align: right;
    opacity: 1;
}

.governorate-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e5e7eb;
    border-radius: 12px;
    padding: 14px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    text-align: right;
    direction: rtl;
    box-sizing: border-box;
    width: 100%;
    cursor: pointer;
}

.governorate-select:focus {
    outline: none;
    border-color: #4f46e5;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.governorate-select option {
    background: #1b1f24;
    color: #e5e7eb;
    padding: 8px;
}

button {
    width: 100%;
    height: 48px;
    min-width: 160px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    cursor: pointer;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
    margin-top: 8px;
}

button:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.error {
    color: #ef4444;
    margin-top: 10px;
    text-align: center;
    font-size: 14px;
}

.hint {
    margin-top: 24px;
    color: #cbd5e1;
    text-align: center;
    font-size: 14px;
}

.hint a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.hint a:hover {
    color: #6366f1;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .navbar-text {
        flex-direction: column;
        gap: 12px;
    }
    
    .org-name, .team-name {
        font-size: 18px;
    }
    
    .card {
        padding: 24px;
        margin: 16px;
    }
    
    .card h1 {
        font-size: 24px;
        margin-bottom: 24px;
    }
    
    input {
        padding: 12px 14px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    button {
        padding: 12px;
        font-size: 16px;
    }
    
    .field {
        margin: 16px 0;
    }
}

@media (max-width: 480px) {
    .navbar {
        margin: 8px;
    }
    
    .navbar-content {
        padding: 12px 16px;
    }
    
    .org-name, .team-name {
        font-size: 16px;
    }
    
    .logo {
        height: 50px;
    }
    
    .auth-wrapper {
        padding: 12px;
    }
    
    .card {
        padding: 20px;
    }
    
    .card h1 {
        font-size: 22px;
    }
    
    .field {
        margin: 14px 0;
    }
}
</style>