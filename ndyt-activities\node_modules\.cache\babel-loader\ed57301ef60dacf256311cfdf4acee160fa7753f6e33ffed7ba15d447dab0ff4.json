{"ast": null, "code": "export default {\n  name: 'RegisterView',\n  data() {\n    return {\n      username: '',\n      full_name: '',\n      password: '',\n      team_pin: '',\n      governorate: '',\n      busy: false,\n      error: '',\n      governorates: ['بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى', 'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين', 'ميسان', 'دهوك', 'السليمانية', 'حلبجة']\n    };\n  },\n  methods: {\n    async doRegister() {\n      this.error = '';\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\n        this.error = 'يرجى تعبئة جميع الحقول';\n        return;\n      }\n      this.busy = true;\n      try {\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: this.username,\n            full_name: this.full_name,\n            password: this.password,\n            team_pin: this.team_pin,\n            governorate: this.governorate\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\n        // persist auth and team pin\n        localStorage.setItem('ndyt_token', data.token);\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.replace(redirect);\n      } catch (e) {\n        this.error = e.message;\n      } finally {\n        this.busy = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "username", "full_name", "password", "team_pin", "governorate", "busy", "error", "governorates", "methods", "doRegister", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "json", "ok", "Error", "localStorage", "setItem", "token", "user", "redirect", "$route", "query", "$router", "replace", "e", "message"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\RegisterView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'RegisterView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      full_name: '',\r\n      password: '',\r\n      team_pin: '',\r\n      governorate: '',\r\n      busy: false,\r\n      error: '',\r\n      governorates: [\r\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\r\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\r\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    async doRegister() {\r\n      this.error = '';\r\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\r\n        // persist auth and team pin\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-brand\">\r\n        <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n        <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"content-wrapper\">\r\n    <div class=\"auth-container\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">إنشاء حساب جديد</h1>\r\n          <p class=\"card-subtitle\">انضم إلى الفريق الوطني للشباب الرقمي</p>\r\n        </div>\r\n\r\n        <div class=\"card-body\">\r\n          <form @submit.prevent=\"doRegister\" class=\"auth-form\">\r\n            <div class=\"form-row\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"username\">اسم المستخدم</label>\r\n                <input\r\n                  id=\"username\"\r\n                  v-model=\"username\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل اسم المستخدم\"\r\n                  required\r\n                  autocomplete=\"username\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"full_name\">الاسم الكامل</label>\r\n                <input\r\n                  id=\"full_name\"\r\n                  v-model=\"full_name\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل الاسم الكامل\"\r\n                  required\r\n                  autocomplete=\"name\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"governorate\">المحافظة</label>\r\n              <select\r\n                id=\"governorate\"\r\n                v-model=\"governorate\"\r\n                class=\"form-select\"\r\n                required\r\n              >\r\n                <option value=\"\" disabled>اختر المحافظة</option>\r\n                <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div class=\"form-row\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"password\">كلمة المرور</label>\r\n                <input\r\n                  id=\"password\"\r\n                  v-model=\"password\"\r\n                  type=\"password\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل كلمة المرور\"\r\n                  required\r\n                  autocomplete=\"new-password\"\r\n                  minlength=\"6\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\" for=\"team_pin\">رمز الفريق الرقمي</label>\r\n                <input\r\n                  id=\"team_pin\"\r\n                  v-model=\"team_pin\"\r\n                  type=\"text\"\r\n                  class=\"form-input\"\r\n                  placeholder=\"ادخل رمز الفريق الرقمي\"\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <button type=\"submit\" :disabled=\"busy\" class=\"btn btn-primary btn-lg auth-submit\">\r\n              <svg v-if=\"busy\" class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\r\n                  <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\r\n                  <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\r\n                </circle>\r\n              </svg>\r\n              <span v-if=\"!busy\">إنشاء الحساب</span>\r\n              <span v-else>جاري إنشاء الحساب...</span>\r\n            </button>\r\n\r\n            <div v-if=\"error\" class=\"error-message\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n              </svg>\r\n              {{ error }}\r\n            </div>\r\n\r\n            <div class=\"auth-footer\">\r\n              <p class=\"auth-hint\">\r\n                لديك حساب بالفعل؟\r\n                <router-link to=\"/login\" class=\"auth-link\">سجّل الدخول</router-link>\r\n              </p>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Enhanced Auth Container */\r\n.auth-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 120px);\r\n  padding: var(--space-8) var(--space-4);\r\n}\r\n\r\n/* Enhanced Form Styling */\r\n.auth-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-6);\r\n  width: 100%;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-2);\r\n}\r\n\r\n.form-row {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-4);\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .form-row {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n}\r\n\r\n.auth-submit {\r\n  margin-top: var(--space-4);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.auth-submit::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.auth-submit:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n/* Loading Spinner Animation */\r\n.loading-spinner {\r\n  animation: spin 1s linear infinite;\r\n  margin-right: var(--space-2);\r\n}\r\n\r\n/* Error Message Styling */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-3) var(--space-4);\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid var(--color-error-500);\r\n  border-radius: var(--radius-xl);\r\n  color: var(--color-error-500);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n  text-align: right;\r\n  animation: slideDown 0.3s ease-out;\r\n}\r\n\r\n/* Auth Footer */\r\n.auth-footer {\r\n  margin-top: var(--space-6);\r\n  padding-top: var(--space-4);\r\n  border-top: 1px solid var(--border-primary);\r\n  text-align: center;\r\n}\r\n\r\n.auth-hint {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin: 0;\r\n  line-height: var(--line-height-relaxed);\r\n}\r\n\r\n.auth-link {\r\n  color: var(--accent-primary);\r\n  text-decoration: none;\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  border-radius: var(--radius-base);\r\n  padding: var(--space-1) var(--space-2);\r\n  margin: 0 calc(-1 * var(--space-2));\r\n}\r\n\r\n.auth-link:hover {\r\n  color: var(--color-primary-400);\r\n  background: rgba(14, 165, 233, 0.1);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Enhanced Card Styling */\r\n.card {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 2px solid var(--border-primary);\r\n  border-radius: var(--radius-3xl);\r\n  box-shadow: var(--shadow-dark-xl);\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));\r\n  background-size: 200% 100%;\r\n  animation: shimmer 3s ease-in-out infinite;\r\n}\r\n\r\n/* Enhanced Card Header */\r\n.card-header {\r\n  padding: var(--space-8) var(--space-8) var(--space-6);\r\n  background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(168, 85, 247, 0.05));\r\n  border-bottom: 1px solid var(--border-primary);\r\n  position: relative;\r\n  text-align: center;\r\n}\r\n\r\n.card-header::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 80px;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));\r\n  border-radius: var(--radius-full);\r\n}\r\n\r\n.card-subtitle {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin: var(--space-2) 0 0;\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n/* Enhanced Card Body */\r\n.card-body {\r\n  padding: var(--space-8);\r\n}\r\n\r\n/* Enhanced Select Styling */\r\n.form-select {\r\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\");\r\n  background-position: left 0.75rem center;\r\n  background-repeat: no-repeat;\r\n  background-size: 1.5em 1.5em;\r\n  padding-left: 2.5rem;\r\n  appearance: none;\r\n}\r\n\r\n/* Animation Keyframes */\r\n@keyframes shimmer {\r\n  0%, 100% { background-position: 200% 0; }\r\n  50% { background-position: -200% 0; }\r\n}\r\n\r\n@keyframes slideDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Responsive Design Improvements */\r\n@media (max-width: 480px) {\r\n  .auth-container {\r\n    padding: var(--space-4) var(--space-2);\r\n    min-height: calc(100vh - 100px);\r\n  }\r\n\r\n  .card {\r\n    max-width: 100%;\r\n    margin: 0;\r\n    border-radius: var(--radius-2xl);\r\n  }\r\n\r\n  .card-header,\r\n  .card-body {\r\n    padding: var(--space-6) var(--space-4);\r\n  }\r\n\r\n  .card-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n\r\n  .card-subtitle {\r\n    font-size: var(--font-size-xs);\r\n  }\r\n\r\n  .form-input,\r\n  .form-select {\r\n    padding: var(--space-4);\r\n    font-size: var(--font-size-base);\r\n  }\r\n\r\n  .auth-submit {\r\n    padding: var(--space-4);\r\n    font-size: var(--font-size-base);\r\n  }\r\n\r\n  .navbar {\r\n    margin: var(--space-2);\r\n  }\r\n\r\n  .navbar-content {\r\n    padding: var(--space-3) var(--space-4);\r\n  }\r\n\r\n  .navbar-brand {\r\n    flex-direction: column;\r\n    gap: var(--space-3);\r\n  }\r\n\r\n  .navbar-text {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n\r\n  .navbar-logo {\r\n    height: 50px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .auth-container {\r\n    padding: var(--space-6) var(--space-3);\r\n  }\r\n\r\n  .navbar-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  .navbar-brand {\r\n    flex-direction: column;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  .card {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .card-header,\r\n  .card-body {\r\n    padding: var(--space-6);\r\n  }\r\n}\r\n\r\n/* Enhanced Focus States for Accessibility */\r\n.form-input:focus-visible,\r\n.form-select:focus-visible,\r\n.auth-submit:focus-visible,\r\n.auth-link:focus-visible {\r\n  outline: 2px solid var(--accent-primary);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Enhanced Hover Effects */\r\n.card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-dark-2xl);\r\n  border-color: var(--border-secondary);\r\n}\r\n\r\n/* Loading State Improvements */\r\n.auth-submit:disabled {\r\n  opacity: 0.8;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.auth-submit:disabled:hover::before {\r\n  left: -100%;\r\n}\r\n\r\n/* Form Validation States */\r\n.form-input:invalid,\r\n.form-select:invalid {\r\n  border-color: var(--color-error-500);\r\n  box-shadow: 0 0 0 1px var(--color-error-500);\r\n}\r\n\r\n.form-input:valid,\r\n.form-select:valid {\r\n  border-color: var(--color-success-500);\r\n}\r\n\r\n/* Enhanced Animation Performance */\r\n.card,\r\n.auth-submit,\r\n.form-input,\r\n.form-select,\r\n.auth-link {\r\n  will-change: transform;\r\n}\r\n\r\n/* Performance Optimizations */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .card,\r\n  .auth-submit,\r\n  .form-input,\r\n  .form-select,\r\n  .auth-link,\r\n  .loading-spinner {\r\n    animation: none !important;\r\n    transition: none !important;\r\n  }\r\n}\r\n\r\n/* Print Styles */\r\n@media print {\r\n  .navbar,\r\n  .auth-submit,\r\n  .auth-footer {\r\n    display: none;\r\n  }\r\n\r\n  .card {\r\n    box-shadow: none;\r\n    border: 1px solid #000;\r\n    background: white;\r\n    color: black;\r\n  }\r\n}\r\n\r\n/* High Contrast Mode */\r\n@media (prefers-contrast: high) {\r\n  .card {\r\n    border: 3px solid;\r\n    background: var(--bg-primary);\r\n  }\r\n\r\n  .form-input,\r\n  .form-select {\r\n    border: 2px solid;\r\n  }\r\n\r\n  .auth-submit {\r\n    border: 2px solid;\r\n  }\r\n}\r\n\r\n.auth-wrapper {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #101214, #1b1f24);\r\n    padding: 16px;\r\n}\r\n\r\n.card {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(20px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 20px;\r\n    padding: 32px;\r\n    width: 100%;\r\n    max-width: 420px;\r\n    color: #e5e7eb;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    transition: all 0.3s ease;\r\n    direction: rtl;\r\n}\r\n\r\n.card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.card h1 {\r\n    font-size: 28px;\r\n    margin: 0 0 32px;\r\n    text-align: center;\r\n    font-weight: 700;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.field {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin: 20px 0;\r\n}\r\n\r\n.field label {\r\n    font-size: 14px;\r\n    color: #cbd5e1;\r\n    font-weight: 600;\r\n    text-align: right;\r\n}\r\n\r\ninput {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n}\r\n\r\ninput:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\ninput::placeholder {\r\n    color: #cbd5e1;\r\n    text-align: right;\r\n    opacity: 1;\r\n}\r\n\r\n.governorate-select {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #e5e7eb;\r\n    border-radius: 12px;\r\n    padding: 14px 16px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    text-align: right;\r\n    direction: rtl;\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    cursor: pointer;\r\n}\r\n\r\n.governorate-select:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\n.governorate-select option {\r\n    background: #1b1f24;\r\n    color: #e5e7eb;\r\n    padding: 8px;\r\n}\r\n\r\nbutton {\r\n    width: 100%;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: white;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n    margin-top: 8px;\r\n}\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.error {\r\n    color: #ef4444;\r\n    margin-top: 10px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint {\r\n    margin-top: 24px;\r\n    color: #cbd5e1;\r\n    text-align: center;\r\n    font-size: 14px;\r\n}\r\n\r\n.hint a {\r\n    color: #4f46e5;\r\n    text-decoration: none;\r\n    font-weight: 600;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.hint a:hover {\r\n    color: #6366f1;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 24px;\r\n        margin: 16px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 24px;\r\n        margin-bottom: 24px;\r\n    }\r\n    \r\n    input {\r\n        padding: 12px 14px;\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n    \r\n    button {\r\n        padding: 12px;\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 16px 0;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .auth-wrapper {\r\n        padding: 12px;\r\n    }\r\n    \r\n    .card {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .card h1 {\r\n        font-size: 22px;\r\n    }\r\n    \r\n    .field {\r\n        margin: 14px 0;\r\n    }\r\n}\r\n</style>"], "mappings": "AACA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,CACZ,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAC1E,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EACrE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,OAAM;IAEzC,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,UAAUA,CAAA,EAAG;MACjB,IAAI,CAACH,KAAI,GAAI,EAAE;MACf,IAAI,CAAC,IAAI,CAACN,QAAO,IAAK,CAAC,IAAI,CAACC,SAAQ,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,WAAW,EAAE;QAC9F,IAAI,CAACE,KAAI,GAAI,wBAAwB;QACrC;MACF;MACA,IAAI,CAACD,IAAG,GAAI,IAAI;MAChB,IAAI;QACF,MAAMK,GAAE,GAAI,MAAMC,KAAK,CAAC,uCAAuC,EAAE;UAC/DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,SAAS,EAAE,IAAI,CAACA,SAAS;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,WAAW,EAAE,IAAI,CAACA;UAAY,CAAC;QAC9J,CAAC,CAAC;QACF,MAAML,IAAG,GAAI,MAAMW,GAAG,CAACO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACP,GAAG,CAACQ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACpB,IAAI,CAACO,KAAI,IAAK,kBAAkB,CAAC;QAC9D;QACAc,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEtB,IAAI,CAACuB,KAAK,CAAC;QAC9CF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEN,IAAI,CAACC,SAAS,CAACjB,IAAI,CAACwB,IAAG,IAAK,CAAC,CAAC,CAAC,CAAC;QAClEH,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAClB,QAAQ,CAAC;QACpD,MAAMqB,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAO,IAAK,GAAG;QAClD,IAAI,CAACG,OAAO,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,EAAE,OAAOK,CAAC,EAAE;QACV,IAAI,CAACvB,KAAI,GAAIuB,CAAC,CAACC,OAAO;MACxB,UAAU;QACR,IAAI,CAACzB,IAAG,GAAI,KAAK;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}