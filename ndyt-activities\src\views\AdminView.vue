<template>
  <div class="layout-container">
    <div class="content-wrapper">
      <div class="admin-header section-header">
        <div class="header-top">
          <button @click="goBack" class="btn btn-secondary back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
            العودة للصفحة الرئيسية
          </button>
        </div>
        <h1 class="section-title">إدارة المستخدمين</h1>
        <p class="section-subtitle">إدارة رتب المستخدمين ومحافظاتهم</p>
      </div>

      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">جاري تحميل المستخدمين...</p>
      </div>

      <div v-else-if="error" class="error-container card">
        <div class="error-message">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          {{ error }}
        </div>
        <button @click="fetchUsers" class="btn btn-primary retry-btn">إعادة المحاولة</button>
      </div>

      <div v-else class="table-container">
        <div class="table-header">
          <h2 class="table-title">قائمة المستخدمين</h2>
          <div class="user-count badge badge-info">{{ users.length }} مستخدم</div>
        </div>

        <div class="table-wrapper">
          <table class="table users-table">
            <thead>
              <tr>
                <th>المعرف</th>
                <th>اسم المستخدم</th>
                <th>الاسم الكامل</th>
                <th>الرتبة</th>
                <th>المحافظة</th>
                <th>تاريخ التسجيل</th>
                <th>آخر دخول</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id" class="user-row">
                <td>{{ user.id }}</td>
                <td class="username">{{ user.username }}</td>
                <td>{{ user.full_name || 'غير محدد' }}</td>
                <td>
                  <span class="badge rank-badge" :class="getRankBadgeClass(user.rank)">
                    {{ getRankLabel(user.rank) }}
                  </span>
                </td>
                <td>{{ user.governorate || 'غير محدد' }}</td>
                <td>{{ formatDate(user.created_at) }}</td>
                <td>{{ user.last_login ? formatDate(user.last_login) : 'لم يدخل بعد' }}</td>
                <td>
                  <button @click="editUser(user)" class="btn btn-sm btn-secondary edit-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                    تعديل
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Edit User Modal -->
      <div v-if="editingUser" class="modal-overlay" @click="closeModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3 class="modal-title">تعديل المستخدم: {{ editingUser.username }}</h3>
            <button @click="closeModal" class="modal-close">&times;</button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="updateUser" class="edit-form">
              <div class="form-group">
                <label class="form-label" for="rank">الرتبة:</label>
                <select v-model="editForm.rank" id="rank" required class="form-select">
                  <option value="member">عضو</option>
                  <option value="leader">منسق</option>
                  <option value="admin">مدير</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label" for="governorate">المحافظة:</label>
                <select v-model="editForm.governorate" id="governorate" required class="form-select">
                  <option v-for="gov in governorates" :key="gov" :value="gov">{{ gov }}</option>
                </select>
              </div>

              <div class="form-actions">
                <button type="button" @click="closeModal" class="btn btn-secondary cancel-btn">إلغاء</button>
                <button type="submit" :disabled="updating" class="btn btn-primary save-btn">
                  <svg v-if="updating" class="loading-spinner" width="16" height="16" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                      <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                      <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                    </circle>
                  </svg>
                  <span v-if="updating">جاري الحفظ...</span>
                  <span v-else>حفظ التغييرات</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminView',
  data() {
    return {
      users: [],
      loading: true,
      error: null,
      editingUser: null,
      editForm: {
        rank: '',
        governorate: ''
      },
      updating: false,
      governorates: [
        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',
        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',
        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'
      ]
    }
  },
  mounted() {
    this.checkAdminAccess();
  },
  methods: {
    async checkAdminAccess() {
      const token = localStorage.getItem('ndyt_token');
      if (!token) {
        this.$router.push('/login');
        return;
      }
      
      try {
        const response = await fetch('/api/v1/ndyt-activities/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.ok) {
          const user = await response.json();
          if (user.rank !== 'admin') {
            this.$swal.fire({
              title: 'غير مصرح',
              text: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
              icon: 'error'
            });
            this.$router.push('/');
            return;
          }
          this.fetchUsers();
        } else {
          this.$router.push('/login');
        }
      } catch (error) {
        console.error('Error checking admin access:', error);
        this.$router.push('/login');
      }
    },
    
    goBack() {
      this.$router.push('/');
    },
    
    async fetchUsers() {
      this.loading = true;
      this.error = null;
      
      try {
        const token = localStorage.getItem('ndyt_token');
        const response = await fetch('/api/v1/ndyt-activities/admin/users', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.ok) {
          this.users = await response.json();
        } else {
          let errorData = {};
          try {
            errorData = await response.json();
          } catch (jsonError) {
            console.error('Error parsing response JSON:', jsonError);
          }
          this.error = errorData.error || 'فشل في تحميل المستخدمين';
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        this.error = 'حدث خطأ أثناء تحميل المستخدمين';
      } finally {
        this.loading = false;
      }
    },
    
    editUser(user) {
      this.editingUser = user;
      this.editForm = {
        rank: user.rank,
        governorate: user.governorate || 'بغداد'
      };
    },
    
    closeModal() {
      this.editingUser = null;
      this.editForm = { rank: '', governorate: '' };
    },
    
    async updateUser() {
      this.updating = true;
      
      try {
        const token = localStorage.getItem('ndyt_token');
        const response = await fetch(`/api/v1/ndyt-activities/admin/users/${this.editingUser.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.editForm)
        });
        
        if (response.ok) {
          const updatedUser = await response.json();
          const index = this.users.findIndex(u => u.id === updatedUser.id);
          if (index !== -1) {
            this.users[index] = { ...this.users[index], ...updatedUser };
          }
          this.closeModal();
          this.$toast.success('تم تحديث المستخدم بنجاح!');
        } else {
          let errorData = {};
          try {
            errorData = await response.json();
          } catch (jsonError) {
            console.error('Error parsing response JSON:', jsonError);
          }
          this.$toast.error(`فشل في تحديث المستخدم: ${errorData.error || 'خطأ غير معروف'}`);
        }
      } catch (error) {
        console.error('Error updating user:', error);
        this.$toast.error('حدث خطأ أثناء تحديث المستخدم');
      } finally {
        this.updating = false;
      }
    },
    
    getRankLabel(rank) {
      const labels = {
        'member': 'عضو',
        'leader': 'منسق',
        'admin': 'مدير'
      };
      return labels[rank] || rank;
    },

    getRankBadgeClass(rank) {
      const classes = {
        'member': 'badge-secondary',
        'leader': 'badge-warning',
        'admin': 'badge-success'
      };
      return classes[rank] || 'badge-secondary';
    },
    
    formatDate(dateString) {
      if (!dateString) return 'غير محدد';
      return new Date(dateString).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
}
</script>

<style scoped>
/* Enhanced Admin Header */
.header-top {
  display: flex;
  justify-content: flex-start;
  margin-bottom: var(--space-5);
}

.back-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  min-width: auto;
}

.back-btn:hover {
  transform: translateY(-2px);
}

.back-btn svg {
  transition: transform var(--transition-fast);
}

.back-btn:hover svg {
  transform: translateX(-2px);
}

/* Enhanced Error Container */
.error-container {
  text-align: center;
  padding: var(--space-8);
  margin: var(--space-6) 0;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  color: var(--color-error-500);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-6);
}

.retry-btn {
  margin-top: var(--space-4);
}

/* Enhanced Table Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 2px solid var(--border-primary);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.user-count {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Enhanced User Table */
.users-table {
  direction: rtl;
}

.username {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.rank-badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-1) var(--space-3);
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  min-width: auto;
}

.edit-btn svg {
  flex-shrink: 0;
}

/* Enhanced Form Styling */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.cancel-btn,
.save-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  min-width: 120px;
  justify-content: center;
}

.save-btn .loading-spinner {
  width: 16px;
  height: 16px;
  margin-right: var(--space-1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .users-table {
    font-size: var(--font-size-sm);
  }

  .users-table th,
  .users-table td {
    padding: var(--space-2);
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-3);
  }

  .cancel-btn,
  .save-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-top {
    margin-bottom: var(--space-3);
  }

  .back-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
  }

  .users-table th,
  .users-table td {
    padding: var(--space-1);
    font-size: var(--font-size-xs);
  }

  .edit-btn {
    padding: var(--space-1) var(--space-2);
  }

  .modal-content {
    margin: var(--space-2);
    max-width: calc(100vw - var(--space-4));
  }
}

.admin-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.admin-subtitle {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #e74c3c;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.users-table-container {
  background: #1a1a2e;
  border-radius: 15px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  border: 1px solid #16213e;
}

.table-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.table-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-wrapper {
  overflow-x: auto;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.users-table th {
  background: #0f3460;
  padding: 15px 12px;
  text-align: right;
  font-weight: 600;
  color: #e2e8f0;
  border-bottom: 2px solid #16213e;
  position: sticky;
  top: 0;
  z-index: 10;
}

.users-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #16213e;
  vertical-align: middle;
  color: #cbd5e0;
  background: #1a1a2e;
}

.user-row {
  transition: all 0.3s ease;
}

.user-row:hover {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  transform: scale(1.01);
}

.username {
  font-weight: 600;
  color: #4facfe;
}

.rank-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}

.rank-member {
  background: #e3f2fd;
  color: #1976d2;
}

.rank-leader {
  background: #fff3e0;
  color: #f57c00;
}

.rank-admin {
  background: #ffebee;
  color: #d32f2f;
}

.edit-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: #1a1a2e;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: modalSlideIn 0.3s ease;
  border: 1px solid #16213e;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.edit-form {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #e2e8f0;
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #16213e;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #0f3460;
  color: #e2e8f0;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-width: 120px;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .admin-container {
    padding: 10px;
  }
  
  .admin-header h1 {
    font-size: 2rem;
  }
  
  .users-table {
    font-size: 0.85rem;
  }
  
  .users-table th,
  .users-table td {
    padding: 10px 8px;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .edit-form {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>