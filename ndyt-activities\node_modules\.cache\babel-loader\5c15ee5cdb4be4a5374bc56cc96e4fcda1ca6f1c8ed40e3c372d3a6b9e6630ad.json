{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport default {\n  name: 'LoginView',\n  data() {\n    return {\n      username: '',\n      password: '',\n      team_pin: '',\n      busy: false,\n      error: ''\n    };\n  },\n  mounted() {\n    // Aggressive cache busting for login component\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\n\n    // Add cache-busting meta tags specifically for login\n    const metaTags = [{\n      name: 'cache-control',\n      content: 'no-cache, no-store, must-revalidate, max-age=0'\n    }, {\n      name: 'pragma',\n      content: 'no-cache'\n    }, {\n      name: 'expires',\n      content: '0'\n    }, {\n      name: 'last-modified',\n      content: new Date().toUTCString()\n    }, {\n      name: 'etag',\n      content: cacheBuster\n    }, {\n      name: 'login-cache-buster',\n      content: cacheBuster\n    }];\n    metaTags.forEach(tag => {\n      // Remove existing meta tags with same name\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\n      if (existing) existing.remove();\n\n      // Add new meta tag\n      const meta = document.createElement('meta');\n      meta.setAttribute('http-equiv', tag.name);\n      meta.setAttribute('content', tag.content);\n      document.head.appendChild(meta);\n    });\n\n    // Force component refresh to prevent caching issues\n    this.$forceUpdate();\n\n    // Clear any cached form data\n    this.username = '';\n    this.password = '';\n    this.team_pin = '';\n    this.error = '';\n\n    // Clear only non-authentication browser storage\n    if (typeof Storage !== 'undefined') {\n      // Only clear sessionStorage, preserve localStorage auth tokens\n      sessionStorage.clear();\n    }\n\n    // Force browser to not cache this page\n    if (window.history && window.history.replaceState) {\n      const url = new URL(window.location.href);\n      url.searchParams.set('_cb', cacheBuster);\n      url.searchParams.set('_nocache', '1');\n      window.history.replaceState(null, null, url.toString());\n    }\n\n    // Add cache-busting attribute to component element\n    if (this.$el && this.$el.setAttribute) {\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\n      this.$el.setAttribute('data-no-cache', 'true');\n    }\n  },\n  beforeUnmount() {\n    // Clear form data when component is destroyed\n    this.username = '';\n    this.password = '';\n    this.team_pin = '';\n    this.error = '';\n  },\n  methods: {\n    async doLogin() {\n      this.error = '';\n      if (!this.username || !this.password || !this.team_pin) {\n        this.error = 'يرجى تعبئة جميع الحقول';\n        return;\n      }\n      this.busy = true;\n      try {\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: this.username,\n            password: this.password,\n            team_pin: this.team_pin\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\n        localStorage.setItem('ndyt_token', data.token);\n        // optional: store user info\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\n        // store team pin for later submissions\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.replace(redirect);\n      } catch (e) {\n        this.error = e.message;\n      } finally {\n        this.busy = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "username", "password", "team_pin", "busy", "error", "mounted", "cacheBuster", "Date", "now", "Math", "random", "toString", "substr", "metaTags", "content", "toUTCString", "for<PERSON>ach", "tag", "existing", "document", "querySelector", "remove", "meta", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "$forceUpdate", "Storage", "sessionStorage", "clear", "window", "history", "replaceState", "url", "URL", "location", "href", "searchParams", "set", "$el", "beforeUnmount", "methods", "do<PERSON><PERSON><PERSON>", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "json", "ok", "Error", "localStorage", "setItem", "token", "user", "redirect", "$route", "query", "$router", "replace", "e", "message"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-activities\\src\\views\\LoginView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'LoginView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      team_pin: '',\r\n      busy: false,\r\n      error: ''\r\n    };\r\n  },\r\n  mounted() {\r\n    // Aggressive cache busting for login component\r\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\r\n    \r\n    // Add cache-busting meta tags specifically for login\r\n    const metaTags = [\r\n      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\r\n      { name: 'pragma', content: 'no-cache' },\r\n      { name: 'expires', content: '0' },\r\n      { name: 'last-modified', content: new Date().toUTCString() },\r\n      { name: 'etag', content: cacheBuster },\r\n      { name: 'login-cache-buster', content: cacheBuster }\r\n    ];\r\n    \r\n    metaTags.forEach(tag => {\r\n      // Remove existing meta tags with same name\r\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\r\n      if (existing) existing.remove();\r\n      \r\n      // Add new meta tag\r\n      const meta = document.createElement('meta');\r\n      meta.setAttribute('http-equiv', tag.name);\r\n      meta.setAttribute('content', tag.content);\r\n      document.head.appendChild(meta);\r\n    });\r\n    \r\n    // Force component refresh to prevent caching issues\r\n    this.$forceUpdate();\r\n    \r\n    // Clear any cached form data\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n    \r\n    // Clear only non-authentication browser storage\r\n    if (typeof Storage !== 'undefined') {\r\n      // Only clear sessionStorage, preserve localStorage auth tokens\r\n      sessionStorage.clear();\r\n    }\r\n    \r\n    // Force browser to not cache this page\r\n    if (window.history && window.history.replaceState) {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.set('_cb', cacheBuster);\r\n      url.searchParams.set('_nocache', '1');\r\n      window.history.replaceState(null, null, url.toString());\r\n    }\r\n    \r\n    // Add cache-busting attribute to component element\r\n    if (this.$el && this.$el.setAttribute) {\r\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\r\n      this.$el.setAttribute('data-no-cache', 'true');\r\n    }\r\n  },\r\n  beforeUnmount() {\r\n    // Clear form data when component is destroyed\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n  },\r\n  methods: {\r\n    async doLogin() {\r\n      this.error = '';\r\n      if (!this.username || !this.password || !this.team_pin) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        // optional: store user info\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        // store team pin for later submissions\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-brand\">\r\n        <span class=\"navbar-text\">المجلس الأعلى للشباب</span>\r\n        <img class=\"navbar-logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"navbar-text\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"content-wrapper\">\r\n    <div class=\"auth-container\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">تسجيل الدخول</h1>\r\n        </div>\r\n\r\n        <div class=\"card-body\">\r\n          <form @submit.prevent=\"doLogin\" class=\"auth-form\">\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"username\">اسم المستخدم</label>\r\n              <input\r\n                id=\"username\"\r\n                v-model=\"username\"\r\n                type=\"text\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل اسم المستخدم\"\r\n                required\r\n                autocomplete=\"username\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"password\">كلمة المرور</label>\r\n              <input\r\n                id=\"password\"\r\n                v-model=\"password\"\r\n                type=\"password\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل كلمة المرور\"\r\n                required\r\n                autocomplete=\"current-password\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"form-label\" for=\"team_pin\">رمز الفريق الرقمي</label>\r\n              <input\r\n                id=\"team_pin\"\r\n                v-model=\"team_pin\"\r\n                type=\"text\"\r\n                class=\"form-input\"\r\n                placeholder=\"ادخل رمز الفريق الرقمي\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            <button type=\"submit\" :disabled=\"busy\" class=\"btn btn-primary btn-lg auth-submit\">\r\n              <svg v-if=\"busy\" class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\r\n                  <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\r\n                  <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\r\n                </circle>\r\n              </svg>\r\n              <span v-if=\"!busy\">دخول</span>\r\n              <span v-else>جاري الدخول...</span>\r\n            </button>\r\n\r\n            <div v-if=\"error\" class=\"error-message\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n              </svg>\r\n              {{ error }}\r\n            </div>\r\n\r\n            <div class=\"auth-footer\">\r\n              <p class=\"auth-hint\">\r\n                ليس لديك حساب؟\r\n                <router-link to=\"/register\" class=\"auth-link\">سجل الآن</router-link>\r\n              </p>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Enhanced Auth Container */\r\n.auth-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 120px);\r\n  padding: var(--space-8) var(--space-4);\r\n}\r\n\r\n/* Enhanced Form Styling */\r\n.auth-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-6);\r\n  width: 100%;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--space-2);\r\n}\r\n\r\n.auth-submit {\r\n  margin-top: var(--space-4);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.auth-submit::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.auth-submit:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n/* Loading Spinner Animation */\r\n.loading-spinner {\r\n  animation: spin 1s linear infinite;\r\n  margin-right: var(--space-2);\r\n}\r\n\r\n/* Error Message Styling */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-3) var(--space-4);\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid var(--color-error-500);\r\n  border-radius: var(--radius-xl);\r\n  color: var(--color-error-500);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n  text-align: right;\r\n  animation: slideDown 0.3s ease-out;\r\n}\r\n\r\n/* Auth Footer */\r\n.auth-footer {\r\n  margin-top: var(--space-6);\r\n  padding-top: var(--space-4);\r\n  border-top: 1px solid var(--border-primary);\r\n  text-align: center;\r\n}\r\n\r\n.auth-hint {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin: 0;\r\n  line-height: var(--line-height-relaxed);\r\n}\r\n\r\n.auth-link {\r\n  color: var(--accent-primary);\r\n  text-decoration: none;\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  border-radius: var(--radius-base);\r\n  padding: var(--space-1) var(--space-2);\r\n  margin: 0 calc(-1 * var(--space-2));\r\n}\r\n\r\n.auth-link:hover {\r\n  color: var(--color-primary-400);\r\n  background: rgba(14, 165, 233, 0.1);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Enhanced Card Styling */\r\n.card {\r\n  width: 100%;\r\n  max-width: 480px;\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 2px solid var(--border-primary);\r\n  border-radius: var(--radius-3xl);\r\n  box-shadow: var(--shadow-dark-xl);\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));\r\n  background-size: 200% 100%;\r\n  animation: shimmer 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0%, 100% { background-position: 200% 0; }\r\n  50% { background-position: -200% 0; }\r\n}\r\n/* Enhanced Card Header */\r\n.card-header {\r\n  padding: var(--space-8) var(--space-8) var(--space-6);\r\n  background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(168, 85, 247, 0.05));\r\n  border-bottom: 1px solid var(--border-primary);\r\n  position: relative;\r\n}\r\n\r\n.card-header::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60px;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));\r\n  border-radius: var(--radius-full);\r\n}\r\n\r\n/* Enhanced Card Body */\r\n.card-body {\r\n  padding: var(--space-8);\r\n}\r\n\r\n/* Responsive Design Improvements */\r\n@media (max-width: 480px) {\r\n  .auth-container {\r\n    padding: var(--space-4) var(--space-2);\r\n    min-height: calc(100vh - 100px);\r\n  }\r\n\r\n  .card {\r\n    max-width: 100%;\r\n    margin: 0;\r\n    border-radius: var(--radius-2xl);\r\n  }\r\n\r\n  .card-header,\r\n  .card-body {\r\n    padding: var(--space-6) var(--space-4);\r\n  }\r\n\r\n  .card-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n\r\n  .form-input {\r\n    padding: var(--space-4);\r\n    font-size: var(--font-size-base);\r\n  }\r\n\r\n  .auth-submit {\r\n    padding: var(--space-4);\r\n    font-size: var(--font-size-base);\r\n  }\r\n\r\n  .navbar {\r\n    margin: var(--space-2);\r\n  }\r\n\r\n  .navbar-content {\r\n    padding: var(--space-3) var(--space-4);\r\n  }\r\n\r\n  .navbar-brand {\r\n    flex-direction: column;\r\n    gap: var(--space-3);\r\n  }\r\n\r\n  .navbar-text {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n\r\n  .navbar-logo {\r\n    height: 50px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .auth-container {\r\n    padding: var(--space-6) var(--space-3);\r\n  }\r\n\r\n  .navbar-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  .navbar-brand {\r\n    flex-direction: column;\r\n    gap: var(--space-4);\r\n  }\r\n}\r\n\r\n/* Enhanced Focus States for Accessibility */\r\n.form-input:focus-visible,\r\n.auth-submit:focus-visible,\r\n.auth-link:focus-visible {\r\n  outline: 2px solid var(--accent-primary);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Enhanced Hover Effects */\r\n.card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-dark-2xl);\r\n  border-color: var(--border-secondary);\r\n}\r\n\r\n/* Loading State Improvements */\r\n.auth-submit:disabled {\r\n  opacity: 0.8;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.auth-submit:disabled:hover::before {\r\n  left: -100%;\r\n}\r\n\r\n/* Form Validation States */\r\n.form-input:invalid {\r\n  border-color: var(--color-error-500);\r\n  box-shadow: 0 0 0 1px var(--color-error-500);\r\n}\r\n\r\n.form-input:valid {\r\n  border-color: var(--color-success-500);\r\n}\r\n\r\n/* Enhanced Animation Performance */\r\n.card,\r\n.auth-submit,\r\n.form-input,\r\n.auth-link {\r\n  will-change: transform;\r\n}\r\n\r\n/* Dark Mode Enhancements */\r\n@media (prefers-color-scheme: dark) {\r\n  .card {\r\n    background: rgba(255, 255, 255, 0.06);\r\n    border-color: var(--border-primary);\r\n  }\r\n\r\n  .form-input {\r\n    background: rgba(255, 255, 255, 0.06);\r\n  }\r\n}\r\n\r\n/* Performance Optimizations */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .card,\r\n  .auth-submit,\r\n  .form-input,\r\n  .auth-link,\r\n  .loading-spinner {\r\n    animation: none !important;\r\n    transition: none !important;\r\n  }\r\n}\r\n\r\n/* Print Styles */\r\n@media print {\r\n  .navbar,\r\n  .auth-submit,\r\n  .auth-footer {\r\n    display: none;\r\n  }\r\n\r\n  .card {\r\n    box-shadow: none;\r\n    border: 1px solid #000;\r\n    background: white;\r\n    color: black;\r\n  }\r\n}\r\n\r\n/* High Contrast Mode */\r\n@media (prefers-contrast: high) {\r\n  .card {\r\n    border: 3px solid;\r\n    background: var(--bg-primary);\r\n  }\r\n\r\n  .form-input {\r\n    border: 2px solid;\r\n  }\r\n\r\n  .auth-submit {\r\n    border: 2px solid;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;AACA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,WAAU,GAAIC,IAAI,CAACC,GAAG,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAExE;IACA,MAAMC,QAAO,GAAI,CACf;MAAEf,IAAI,EAAE,eAAe;MAAEgB,OAAO,EAAE;IAAiD,CAAC,EACpF;MAAEhB,IAAI,EAAE,QAAQ;MAAEgB,OAAO,EAAE;IAAW,CAAC,EACvC;MAAEhB,IAAI,EAAE,SAAS;MAAEgB,OAAO,EAAE;IAAI,CAAC,EACjC;MAAEhB,IAAI,EAAE,eAAe;MAAEgB,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC;IAAE,CAAC,EAC5D;MAAEjB,IAAI,EAAE,MAAM;MAAEgB,OAAO,EAAER;IAAY,CAAC,EACtC;MAAER,IAAI,EAAE,oBAAoB;MAAEgB,OAAO,EAAER;IAAY,EACpD;IAEDO,QAAQ,CAACG,OAAO,CAACC,GAAE,IAAK;MACtB;MACA,MAAMC,QAAO,GAAIC,QAAQ,CAACC,aAAa,CAAC,oBAAoBH,GAAG,CAACnB,IAAI,IAAI,CAAC;MACzE,IAAIoB,QAAQ,EAAEA,QAAQ,CAACG,MAAM,CAAC,CAAC;;MAE/B;MACA,MAAMC,IAAG,GAAIH,QAAQ,CAACI,aAAa,CAAC,MAAM,CAAC;MAC3CD,IAAI,CAACE,YAAY,CAAC,YAAY,EAAEP,GAAG,CAACnB,IAAI,CAAC;MACzCwB,IAAI,CAACE,YAAY,CAAC,SAAS,EAAEP,GAAG,CAACH,OAAO,CAAC;MACzCK,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,CAACK,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAAC3B,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACE,KAAI,GAAI,EAAE;;IAEf;IACA,IAAI,OAAOwB,OAAM,KAAM,WAAW,EAAE;MAClC;MACAC,cAAc,CAACC,KAAK,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIC,MAAM,CAACC,OAAM,IAAKD,MAAM,CAACC,OAAO,CAACC,YAAY,EAAE;MACjD,MAAMC,GAAE,GAAI,IAAIC,GAAG,CAACJ,MAAM,CAACK,QAAQ,CAACC,IAAI,CAAC;MACzCH,GAAG,CAACI,YAAY,CAACC,GAAG,CAAC,KAAK,EAAEjC,WAAW,CAAC;MACxC4B,GAAG,CAACI,YAAY,CAACC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;MACrCR,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAEC,GAAG,CAACvB,QAAQ,CAAC,CAAC,CAAC;IACzD;;IAEA;IACA,IAAI,IAAI,CAAC6B,GAAE,IAAK,IAAI,CAACA,GAAG,CAAChB,YAAY,EAAE;MACrC,IAAI,CAACgB,GAAG,CAAChB,YAAY,CAAC,uBAAuB,EAAElB,WAAW,CAAC;MAC3D,IAAI,CAACkC,GAAG,CAAChB,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IAChD;EACF,CAAC;EACDiB,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACzC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACE,KAAI,GAAI,EAAE;EACjB,CAAC;EACDsC,OAAO,EAAE;IACP,MAAMC,OAAOA,CAAA,EAAG;MACd,IAAI,CAACvC,KAAI,GAAI,EAAE;MACf,IAAI,CAAC,IAAI,CAACJ,QAAO,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,QAAQ,EAAE;QACtD,IAAI,CAACE,KAAI,GAAI,wBAAwB;QACrC;MACF;MACA,IAAI,CAACD,IAAG,GAAI,IAAI;MAChB,IAAI;QACF,MAAMyC,GAAE,GAAI,MAAMC,KAAK,CAAC,oCAAoC,EAAE;UAC5DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAElD,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA;UAAS,CAAC;QACpG,CAAC,CAAC;QACF,MAAMH,IAAG,GAAI,MAAM6C,GAAG,CAACO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACP,GAAG,CAACQ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACtD,IAAI,CAACK,KAAI,IAAK,kBAAkB,CAAC;QAC9DkD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAExD,IAAI,CAACyD,KAAK,CAAC;QAC9C;QACAF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEN,IAAI,CAACC,SAAS,CAACnD,IAAI,CAAC0D,IAAG,IAAK,CAAC,CAAC,CAAC,CAAC;QAClE;QACAH,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAACrD,QAAQ,CAAC;QACpD,MAAMwD,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAO,IAAK,GAAG;QAClD,IAAI,CAACG,OAAO,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,EAAE,OAAOK,CAAC,EAAE;QACV,IAAI,CAAC3D,KAAI,GAAI2D,CAAC,CAACC,OAAO;MACxB,UAAU;QACR,IAAI,CAAC7D,IAAG,GAAI,KAAK;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}