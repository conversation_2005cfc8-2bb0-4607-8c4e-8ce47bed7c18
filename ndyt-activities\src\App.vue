<script>
import { RouterView } from 'vue-router';
import './assets/styles/design-system.css';

export default {
  name: 'App',
  components: {
    RouterView
  },
  computed: {
    routeKey() {
      // Force component re-render on route change to prevent caching
      return this.$route.fullPath + Date.now();
    }
  }
};
</script>
<template>
  <div class="layout-container">
    <RouterView :key="routeKey" />
  </div>
</template>
<style>
/* SweetAlert2 Custom Styles - Using Design System Variables */
.swal-popup {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary)) !important;
  border: 2px solid var(--border-primary) !important;
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-dark-xl) !important;
  backdrop-filter: blur(20px) !important;
}

.swal2-title {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-xl) !important;
  font-family: var(--font-family-arabic) !important;
}

.swal2-content {
  color: var(--text-secondary) !important;
  font-size: var(--font-size-base) !important;
  font-family: var(--font-family-arabic) !important;
  direction: rtl !important;
  text-align: right !important;
}

.swal-confirm-btn {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600)) !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-3) var(--space-6) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-family: var(--font-family-arabic) !important;
  box-shadow: var(--shadow-dark-md) !important;
  transition: all var(--transition-base) !important;
  min-height: var(--btn-height-base) !important;
}

.swal-confirm-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500)) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-dark-lg) !important;
}

.swal-cancel-btn {
  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-700)) !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-3) var(--space-6) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-family: var(--font-family-arabic) !important;
  box-shadow: var(--shadow-dark-md) !important;
  transition: all var(--transition-base) !important;
  min-height: var(--btn-height-base) !important;
}

.swal-cancel-btn:hover {
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600)) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-dark-lg) !important;
}

/* Toast Notifications Custom Styles - Using Design System Variables */
.Vue-Toastification__toast {
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-dark-lg) !important;
  border: 2px solid var(--border-primary) !important;
  backdrop-filter: blur(20px) !important;
  font-family: var(--font-family-arabic) !important;
}

.toast-success {
  background: linear-gradient(135deg, var(--color-success-600), var(--color-success-700)) !important;
  color: white !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.toast-error {
  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-700)) !important;
  color: white !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.toast-warning {
  background: linear-gradient(135deg, var(--color-warning-600), var(--color-warning-700)) !important;
  color: white !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.toast-info {
  background: linear-gradient(135deg, var(--color-info-600), var(--color-info-700)) !important;
  color: white !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.toast-body {
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-sm) !important;
  direction: rtl !important;
  text-align: right !important;
  line-height: var(--line-height-relaxed) !important;
}

.Vue-Toastification__progress-bar {
  background: rgba(255, 255, 255, 0.3) !important;
  height: 3px !important;
}

.Vue-Toastification__close-button {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  transition: all var(--transition-fast) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-1) !important;
}

.Vue-Toastification__close-button:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1) !important;
}

/* Enhanced Toast Container */
.Vue-Toastification__container {
  z-index: var(--z-tooltip) !important;
}

/* Toast Animation Improvements */
.Vue-Toastification__toast-enter-active {
  animation: slideInRight 0.3s ease-out !important;
}

.Vue-Toastification__toast-leave-active {
  animation: slideOutRight 0.3s ease-in !important;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
</style>